/************************************************************
 * Copyright 2000-2020 OPPO Mobile Comm Corp., Ltd.
 * All rights reserved.
 * <p>
 * FileName       : ShadowRecorderUtilBelowQ.java
 * Version Number : 1.0
 * Description    :
 * Author         : LI Kun
 * Date           : 2019/10/10
 * History        :( ID,     Date,         Author, Description)
 * v1.0, 2019/10/10, LI Kun, create
 ************************************************************/

package oplus.multimedia.soundrecorder.shadows;

import android.content.Context;

import org.robolectric.annotation.Implementation;
import org.robolectric.annotation.Implements;

import oplus.multimedia.soundrecorder.utils.RecorderUtil;

@Implements(RecorderUtil.class)
public class ShadowRecorderUtilBelowQ {

    @Implementation
    public static boolean isAndroidQOrLater() {
        return false;
    }

    @Implementation
    public static void enableBackgroundService(Context context) {

    }
}
