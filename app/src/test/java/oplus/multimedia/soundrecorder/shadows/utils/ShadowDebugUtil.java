package oplus.multimedia.soundrecorder.shadows.utils;

import com.soundrecorder.base.utils.DebugUtil;

import org.robolectric.annotation.Implementation;
import org.robolectric.annotation.Implements;

@Implements(DebugUtil.class)
public class ShadowDebugUtil {

    @Implementation
    public static void d(String tag, String msg) {
        System.out.println(tag + "->" + msg);
    }

    @Implementation
    public static void d(String tag, String msg, Boolean saveInXLog) {
        System.out.println(tag + "->" + msg);
    }

    @Implementation
    public static void i(String tag, String msg) {
        System.out.println(tag + "->" + msg);
    }

    @Implementation
    public static void i(String tag, String msg, Boolean saveInXLog) {
        System.out.println(tag + "->" + msg);
    }

    @Implementation
    public static void e(String tag, String msg) {
        System.out.println(tag + "->" + msg);
    }

    @Implementation
    public static void e(String tag, String msg, Throwable throwable) {
        throwable.printStackTrace();
        System.out.println(tag + "->" + msg);
    }
}
