package oplus.multimedia.soundrecorder.utils;

import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;

import com.oplus.os.OplusUsbEnvironment;
import com.soundrecorder.common.constant.RecordModeConstant;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;
import android.content.Context;
import android.graphics.Typeface;
import android.net.Uri;
import android.os.Build;
import android.text.TextUtils;
import androidx.test.core.app.ApplicationProvider;
import androidx.test.ext.junit.runners.AndroidJUnit4;
import com.soundrecorder.base.BaseApplication;
import com.soundrecorder.base.utils.BaseUtil;
import com.soundrecorder.base.utils.ClickUtils;
import org.junit.After;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.robolectric.annotation.Config;
import java.io.File;
import com.soundrecorder.base.utils.FileUtils;
import com.soundrecorder.common.databean.Record;
import com.soundrecorder.common.utils.RecordModeUtil;
import oplus.multimedia.soundrecorder.shadows.ShadowBaseUtils;
import oplus.multimedia.soundrecorder.shadows.ShadowOS12FeatureUtil;
import oplus.multimedia.soundrecorder.shadows.ShadowFeatureOption;
import oplus.multimedia.soundrecorder.shadows.ShadowOplusUsbEnvironment;
import oplus.multimedia.soundrecorder.shadows.ShadowRecorderUtilBelowQ;

@RunWith(AndroidJUnit4.class)
@Config(sdk = Build.VERSION_CODES.S, shadows = {ShadowOS12FeatureUtil.class, ShadowFeatureOption.class})
public class RecorderUtilTest {

    private Context mContext;
    private MockedStatic<BaseApplication> mMockedBaseApplication;

    private static final int SS = 1000;
    private static final int MI = SS * 60;
    private static final int HH = MI * 60;
    private static final int DD = HH * 24;
    private static final int INT_ZERO = 0;


    private static final String EMPTY_STRING = "";

    private static final String STRING_TEST_PATH = File.separator + "path" + File.separator + "of" + File.separator + "file" + File.separator + "test.java";
    private static final String STRING_TEST_JAVA = "test.java";
    private static final String INPUT_PATH_MP3 = "emulated/0/Music/Recordings/Standard Recordings/1.mp3";
    private static final String INPUT_PATH_AMR = "emulated/0/Music/Recordings/Call Recordings/2.amr";
    private static final String RELATIVE_PATH_STANDARD = "Music" + File.separator + "Recordings" + File.separator + "Standard Recordings" + File.separator;
    private static final String RELATIVE_PATH_MEETING = "Music" + File.separator + "Recordings" + File.separator + "Meeting Recordings" + File.separator;
    private static final String RELATIVE_PATH_INTERVIEW = "Music" + File.separator + "Recordings" + File.separator + "Interview Recordings" + File.separator;
    private static final String RELATIVE_PATH_CALL = "Music" + File.separator + "Recordings" + File.separator + "Call Recordings" + File.separator;
    private static final String PATH_OF_FILE = "/path/of/file";

    @Before
    public void setUp() {
        mContext = ApplicationProvider.getApplicationContext();
        mMockedBaseApplication = Mockito.mockStatic(BaseApplication.class);
        mMockedBaseApplication.when(BaseApplication::getAppContext).thenReturn(mContext);
    }

    @After
    public void tearDown() {
        if (mMockedBaseApplication != null) {
            mMockedBaseApplication.close();
            mMockedBaseApplication = null;
        }
        mContext = null;
    }

    @Test
    public void should_returnTrue_when_getExtention() {
        String mp3Extention = RecorderUtil.getExtension(INPUT_PATH_MP3);
        assertTrue(mp3Extention.equalsIgnoreCase("mp3"));
        String amrMimeType = RecorderUtil.getExtension(INPUT_PATH_AMR);
        assertTrue(amrMimeType.equalsIgnoreCase("amr"));
        assertNull(RecorderUtil.getExtension(null));
        assertTrue(TextUtils.isEmpty(RecorderUtil.getExtension(RELATIVE_PATH_CALL)));
        assertEquals(EMPTY_STRING, RecorderUtil.getExtension(EMPTY_STRING));
    }

    @Test
    @Config(shadows = ShadowOplusUsbEnvironment.class)
    public void should_returnNotNull_when_getSDCardStorageDir_withNormal() {
        assertNotNull(BaseUtil.getSDCardStorageDir(mContext));
    }

    @Test
    public void should_returnNotnull_when_getMimeTypeByPath() {
        assertNull(RecorderUtil.getMimeTypeByPath(null));
        String b1 = RecorderUtil.getMimeTypeByPath(INPUT_PATH_MP3);
        assertNull(b1);
    }

    @Test
    public void should_returnNotnull_when_getXTypeFont() {
        assertNotNull(RecorderUtil.getXTypeFont());
    }

    @Test
    @Ignore
    public void should_returnTrue_when_checkFileNotExistOrFileSizeZero() {
        assertTrue(FileUtils.checkFileNotExistOrFileSizeZero("TEST", Uri.parse("test")));
    }

    @Test
    public void should_returnTypeface_when_getMediumTypeFont_withNormal() {
        MockedStatic<Typeface> mocked = mockStatic(Typeface.class);
        Typeface mockTypeface = mock(Typeface.class);
        mocked.when(() -> Typeface.create(anyString(), anyInt())).thenReturn(mockTypeface);
        Typeface mediumTypeFont = RecorderUtil.getMediumTypeFont();
        assertNotNull(mediumTypeFont);
        mocked.when(() -> Typeface.create(anyString(), anyInt())).thenThrow(new RuntimeException());
        Typeface typeFont = RecorderUtil.getMediumTypeFont();
        assertNotNull(typeFont);
        mocked.close();
    }

    @Test
    @Config(shadows = ShadowRecorderUtilBelowQ.class)
    public void should_returnStringPath_when_getPhoneStorageDir_with_different_input() {
        MockedStatic<OplusUsbEnvironment> mocked = mockStatic(OplusUsbEnvironment.class);
        File file = new File(PATH_OF_FILE);
        mocked.when(() -> OplusUsbEnvironment.getInternalSdDirectory(mContext)).thenReturn(file, (File) null);
        String phoneStorageDir = BaseUtil.getPhoneStorageDir(mContext);
        assertEquals(file.getPath(), phoneStorageDir);
        phoneStorageDir = BaseUtil.getPhoneStorageDir(mContext);
        assertNull(phoneStorageDir);
        mocked.close();
    }


    @Test
    public void should_correct_when_isQuickClick() {
        new Thread(() -> {
            try {
                assertTrue(ClickUtils.isQuickClick());
                Thread.sleep(300L);
            } catch (InterruptedException ignored) {
            }
            assertFalse(ClickUtils.isQuickClick());
        }).start();
    }

    @Test
    @Config(shadows = ShadowBaseUtils.class)
    public void should_returnType_when_getRecordTypeForMediaRecord() {
        assertEquals(INT_ZERO, RecordModeUtil.getRecordTypeForMediaRecord(null));
        Record record = mock(Record.class);
        doReturn("", RELATIVE_PATH_STANDARD, RELATIVE_PATH_MEETING, RELATIVE_PATH_INTERVIEW, RELATIVE_PATH_CALL).when(record).getRelativePath();
        assertEquals(0, RecordModeUtil.getRecordTypeForMediaRecord(record));
        assertEquals(RecordModeConstant.RECORD_TYPE_STANDARD, RecordModeUtil.getRecordTypeForMediaRecord(record));
        assertEquals(RecordModeConstant.RECORD_TYPE_CONFERENCE, RecordModeUtil.getRecordTypeForMediaRecord(record));
        assertEquals(RecordModeConstant.RECORD_TYPE_INTERVIEW, RecordModeUtil.getRecordTypeForMediaRecord(record));
        assertEquals(RecordModeConstant.RECORD_TYPE_CALL, RecordModeUtil.getRecordTypeForMediaRecord(record));
    }
}
