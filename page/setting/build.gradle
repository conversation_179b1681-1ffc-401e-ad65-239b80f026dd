apply from:"../../common_build.gradle"

android {
    buildTypes {
        debug {
            /*"https://beian.miit.gov.cn"*/
            buildConfigField "String", "icpUrl", "\"`||x{2''jmaif&eaa|&og~&kf\""
        }

        release {
            buildConfigField "String", "icpUrl", "\"`||x{2''jmaif&eaa|&og~&kf\""
        }
    }
    buildFeatures {
        buildConfig true
    }
    namespace "com.soundrecorder.setting"
}

dependencies {
    implementation fileTree(include: ['*.so'], dir: 'libs')
    implementation libs.androidx.support
    implementation libs.androidx.appcompat
    //kotlin
    implementation libs.org.kotlin.stdlib
    implementation libs.androidx.lifecycle.livedata
    implementation libs.androidx.lifecycle.viewmodel
    implementation libs.androidx.lifecycle.extensions
    // fragment
    implementation libs.androidx.fragment.ktx

    kaptTest libs.androidx.databinding.compiler

    // base包为必须引用的包，prop_versionName需保持一致
    implementation (libs.oplus.coui.core) {
        exclude group: 'com.squareup.okio', module: 'okio'
    }
    // 以下子包应用可选使用，如有使用了如下子包的控件，则需要添加，未使用可以不引用
    implementation libs.oplus.coui.poplist
    implementation libs.oplus.coui.snackbar
    implementation libs.oplus.coui.toolbar
    implementation libs.oplus.coui.preference
    implementation libs.oplus.coui.recyclerview
    implementation libs.oplus.coui.dialog
    implementation libs.oplus.coui.card
    implementation libs.oplus.coui.panel
    implementation libs.oplus.coui.component

    implementation(libs.oplus.material)

    // Koin for Android
    implementation(libs.koin)

    testImplementation project(':common:RecorderLogBase')
    implementation project(':common:modulerouter')
    implementation project(':common:libbase')
    implementation project(':common:libcommon')
}
