/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: RecordModeDialog
 * Description:
 * Version: 1.0
 * Date: 2023/7/4
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2023/7/4 1.0 create
 */

package com.soundrecorder.setting.setting.dialog

import android.content.Context
import androidx.appcompat.app.AlertDialog
import com.coui.appcompat.dialog.COUIAlertDialogBuilder
import com.coui.appcompat.dialog.adapter.ChoiceListAdapter
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.PrefUtil
import com.soundrecorder.common.buryingpoint.RecorderUserAction
import com.soundrecorder.common.utils.RecordModeUtil
import com.soundrecorder.common.utils.ViewUtils

/**
 * 外销选择录音音频格式弹窗
 */
class RecordModeDialog(val context: Context) {
    companion object {
        private const val PREFERENCE_KEY_RECORD_MODE_RED_DOT_VISIBLE = "record_mode_redDot_visible"

        @JvmStatic
        fun getModeName(modeValue: Int): String {
            val context = BaseApplication.getAppContext()
            return when (modeValue) {
                RecordModeUtil.FRAGMENTS_TYPE_NORMAL -> context.getString(com.soundrecorder.common.R.string.standard_mode)
                RecordModeUtil.FRAGMENTS_TYPE_MEETING -> context.getString(com.soundrecorder.common.R.string.conference_mode)
                RecordModeUtil.FRAGMENTS_TYPE_INTERVIEW -> context.getString(com.soundrecorder.common.R.string.interview_mode)
                else -> context.getString(com.soundrecorder.common.R.string.standard_mode)
            }
        }

        /**
         * 获取是否需要显示录制模式小红点
         */
        @JvmStatic
        fun isNeedShowRecordModeRedDot(): Boolean {
            return !PrefUtil.getBoolean(
                BaseApplication.getAppContext(),
                PREFERENCE_KEY_RECORD_MODE_RED_DOT_VISIBLE,
                false
            )
        }

        /**
         * 设置录制模式小红点已经显示过了
         */
        @JvmStatic
        fun setRecordModeRedDotShowed() {
            PrefUtil.putBoolean(
                BaseApplication.getAppContext(),
                PREFERENCE_KEY_RECORD_MODE_RED_DOT_VISIBLE,
                true
            )
        }
    }

    private var mDialog: AlertDialog? = null
    var mDialogItemListener: DialogChildClickListener? = null

    fun showDialog(modeValue: Int) {
        val mode = arrayOf(
            context.getString(com.soundrecorder.common.R.string.standard_mode),
            context.getString(com.soundrecorder.common.R.string.conference_mode),
            context.getString(com.soundrecorder.common.R.string.interview_mode)
        )
        val description =
            arrayOf(
                context.getString(com.soundrecorder.common.R.string.standard_mode_description),
                context.getString(com.soundrecorder.common.R.string.conference_mode_description),
                context.getString(com.soundrecorder.common.R.string.interview_mode_description)
            )
        val checkboxStates = booleanArrayOf(false, false, false)
        val disableStatus = booleanArrayOf(false, false, false)
        checkboxStates[modeValue] = true
        val singleChoiceListAdapter = ChoiceListAdapter(
            context,
            com.support.dialog.R.layout.coui_select_dialog_singlechoice,
            mode,
            description, checkboxStates, disableStatus, false
        )
        mDialog = COUIAlertDialogBuilder(context, com.support.dialog.R.style.COUIAlertDialog_BottomAssignment)
            .setBlurBackgroundDrawable(true)
            .setTitle(context.getString(com.soundrecorder.common.R.string.record_mode))
            .setAdapter(singleChoiceListAdapter) { _, which ->
                mDialogItemListener?.click(which, mode[which])
                release()
                RecordModeUtil.setModeValue(which)
                addBuryPoint(which)
            }
            .setNegativeButton(com.soundrecorder.common.R.string.cancel, null)
            .show()
        ViewUtils.updateWindowLayoutParams(mDialog?.window)
    }

    private fun addBuryPoint(mode: Int) {
        val eventInfo = HashMap<String?, String?>()
        eventInfo["mode"] = mode.toString()
        RecorderUserAction.addCommonUserAction(
            BaseApplication.getAppContext(), RecorderUserAction.USER_ACTION_MAIN_VIEW_TAG,
            RecorderUserAction.EVENT_RECORDER_START, eventInfo, false
        )
    }

    fun release() {
        mDialog?.dismiss()
        mDialogItemListener = null
        mDialog = null
    }

    fun interface DialogChildClickListener {
        fun click(audioFormat: Int, name: String)
    }
}