/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: SettingRecordBehaviorTest
 * Description:
 * Version: 1.0
 * Date: 2023/5/10
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2023/5/10 1.0 create
 */

package com.soundrecorder.setting.opensource

import android.content.Context
import android.os.Build
import android.view.View
import android.widget.LinearLayout
import androidx.coordinatorlayout.widget.CoordinatorLayout
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.google.android.material.appbar.AppBarLayout
import com.soundrecorder.setting.R
import com.soundrecorder.setting.shadows.ShadowFeatureOption
import com.soundrecorder.setting.shadows.ShadowOS12FeatureUtil
import com.soundrecorder.setting.shadows.ShadowRecorderLogger
import org.junit.Assert
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mockito
import org.powermock.reflect.Whitebox
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(
    sdk = [Build.VERSION_CODES.S],
    shadows = [ShadowOS12FeatureUtil::class, ShadowFeatureOption::class, ShadowRecorderLogger::class]
)
class SettingRecordBehaviorTest {
    private val mContext: Context = ApplicationProvider.getApplicationContext()

    @Test
    fun when_onRestoreInstanceState() {
        val behavior = SettingRecordBehavior(mContext, null)
        var state = SavedState(null, behavior)
        val consLayout = CoordinatorLayout(mContext).apply {
            addView(View(mContext).apply { id = R.id.divider_line })
            addView(View(mContext).apply { id = 1 })
        }
        val appBarLayout = Mockito.mock(AppBarLayout::class.java)
        behavior.onRestoreInstanceState(consLayout, appBarLayout, state)
        Assert.assertNotNull(Whitebox.getInternalState(behavior, "dividerView"))
        Assert.assertNull(Whitebox.getInternalState(behavior, "targetView"))

        behavior.targetViewId = 1
        state = SavedState(null, behavior)
        behavior.onRestoreInstanceState(consLayout, appBarLayout, state)
        Assert.assertNotNull(Whitebox.getInternalState(behavior, "targetView"))
    }

    @Test
    fun when_onStartNestedScroll() {
        val behavior = SettingRecordBehavior(mContext, null)
        behavior.targetViewId = 1
        val state = SavedState(null, behavior)
        val consLayout = CoordinatorLayout(mContext).apply {
            addView(View(mContext).apply { id = R.id.divider_line })
            addView(View(mContext).apply { id = 1 })
        }
        val appBarLayout = Mockito.mock(AppBarLayout::class.java)
        behavior.onRestoreInstanceState(consLayout, appBarLayout, state)
        Whitebox.invokeMethod<Boolean>(
            behavior,
            "onStartNestedScroll",
            consLayout,
            appBarLayout,
            View(mContext),
            View(mContext).apply { id = 2 },
            3,
            0
        )
    }

    @Test
    fun when_onListScroll() {
        val behavior = SettingRecordBehavior(mContext, null)
        behavior.onScrollChange(null, 0, 0, 0, 0)
        val state = SavedState(null, behavior)
        val consLayout = CoordinatorLayout(mContext).apply {
            addView(View(mContext).apply { id = R.id.divider_line })
            addView(View(mContext).apply { id = 1 })
        }
        val appBarLayout = Mockito.mock(AppBarLayout::class.java)
        behavior.onRestoreInstanceState(consLayout, appBarLayout, state)
        behavior.onScrollChange(null, 0, 0, 0, 0)
        Assert.assertTrue(0F == Whitebox.getInternalState<View>(behavior, "dividerView").alpha)

        Whitebox.setInternalState(behavior, "targetView", LinearLayout(mContext))
        behavior.onScrollChange(null, 0, 0, 0, 0)
        Assert.assertTrue(0F == Whitebox.getInternalState<View>(behavior, "dividerView").alpha)

        Whitebox.setInternalState(behavior, "targetView", consLayout)
    }
}