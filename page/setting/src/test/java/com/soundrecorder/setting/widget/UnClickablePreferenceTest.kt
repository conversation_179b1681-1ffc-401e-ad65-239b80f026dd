/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: UnClickablePreferenceTest
 * Description:
 * Version: 1.0
 * Date: 2023/5/10
 * Author: ********(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * ******** 2023/5/10 1.0 create
 */

package com.soundrecorder.setting.widget

import android.content.Context
import android.os.Build
import androidx.preference.PreferenceViewHolder
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.coui.appcompat.cardlist.COUICardListSelectedItemLayout
import com.soundrecorder.setting.shadows.ShadowFeatureOption
import com.soundrecorder.setting.shadows.ShadowOS12FeatureUtil
import com.soundrecorder.setting.shadows.ShadowRecorderLogger
import org.junit.Assert
import org.junit.Test
import org.junit.runner.RunWith
import org.powermock.reflect.Whitebox
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(
    sdk = [Build.VERSION_CODES.S],
    shadows = [ShadowOS12FeatureUtil::class, ShadowFeatureOption::class, ShadowRecorderLogger::class]
)
class UnClickablePreferenceTest {
    private val mContext: Context = ApplicationProvider.getApplicationContext()

    @Test
    fun when_onBindViewHolder() {
        val view = UnClickablePreference(mContext)
        val itemView = COUICardListSelectedItemLayout(mContext)
        val viewHolder = PreferenceViewHolder.createInstanceForTests(itemView)
        view.onBindViewHolder(viewHolder)
        Assert.assertFalse(Whitebox.getInternalState(itemView, "mBackgroundAnimationEnabled"))
    }
}