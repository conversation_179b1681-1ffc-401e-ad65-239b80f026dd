/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: RecorderAppCardDialogUtilsTest
 Description:
 Version: 1.0
 Date: 2022/9/22
 Author: W9013333(v-zhen<PERSON><PERSON><PERSON><PERSON>@oppo.com)
 -----------Revision History-----------
 <author> <date> <version> <desc>
 W9013333 2022/9/22 1.0 create
 */

package com.soundrecorder.record.card

import android.os.Build
import android.os.Looper
import android.view.View
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.record.shadows.ShadowFeatureOption
import com.soundrecorder.record.shadows.ShadowOS12FeatureUtil
import com.soundrecorder.record.shadows.ShadowOplusUsbEnvironment
import oplus.multimedia.soundrecorder.card.dragonFly.RecorderAppCardActivity
import oplus.multimedia.soundrecorder.card.dragonFly.RecorderAppCardDialogUtils.showSaveFileSuccessDialog
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.Robolectric
import org.robolectric.Shadows
import org.robolectric.android.controller.ActivityController
import org.robolectric.annotation.Config
import com.soundrecorder.record.R

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowOplusUsbEnvironment::class, ShadowOS12FeatureUtil::class, ShadowFeatureOption::class])
class RecorderAppCardDialogUtilsTest {
    private var activityController: ActivityController<RecorderAppCardActivity>? = null

    @Before
    fun setUp() {
        activityController = Robolectric.buildActivity(RecorderAppCardActivity::class.java)
    }

    @After
    fun tearDown() {
        activityController = null
    }

    @Test
    fun showSaveFileSuccessDialog() {
        val activity = activityController?.create()?.get() ?: return
        Shadows.shadowOf(Looper.getMainLooper()).idle()
        val dialog = activity.showSaveFileSuccessDialog("A") {}
        dialog?.setOnShowListener {
            val btn = dialog.findViewById<View>(R.id.btnView)
            Assert.assertNotNull(btn)
            btn?.performClick()
        }
        Assert.assertNotNull(dialog)
        Assert.assertTrue(dialog?.isShowing == true)
        dialog?.dismiss()
    }
}
