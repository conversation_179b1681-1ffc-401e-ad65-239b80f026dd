/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: StartRecordPathInterpolator
 Description:
 Version: 1.0
 Date: 2022/12/22
 Author: W9010241
 -----------Revision History-----------
 <author> <date> <version> <desc>
 W9010241 2022/12/22 1.0 create
 */

package com.soundrecorder.record.views.wave.anima

import android.view.animation.PathInterpolator
import com.soundrecorder.record.views.wave.RecorderWaveItemView.Companion.ALPHA_20_PERCENT
import com.soundrecorder.record.views.wave.RecorderWaveItemView.Companion.ALPHA_40_PERCENT
import com.soundrecorder.record.views.wave.RecorderWaveItemView.Companion.ENTER_ANIMATION_CHANGE_DURATION
import com.soundrecorder.record.views.wave.RecorderWaveItemView.Companion.ENTER_ANIMATION_FALL_ALPHA_DURATION
import com.soundrecorder.record.views.wave.RecorderWaveItemView.Companion.ENTER_ANIMATION_RISE_DURATION

/**
 * 进入录制界面动效插值器。
 * 上升和下降速度不一样，所以使用2个插值器处理波形高度变化。
 * 同时还需要2个波形alpha插值器处理颜色变化。
 *
 * 贝塞尔曲线仅仅表示速度变化曲线，不是数值变化曲线，曲线的上升下降表示高度变化的快慢，需要结合最大高度实时计算出真正显示的高度
 *
 * 所以波形高度降低区间，高度实际高度 = 初始高度 2 + （1f - heightFallPathInterpolator.getInterpolation(t)） * maxHeight
 * 同理alpha降低区间（40% - 20%）的实际 alpha = 0.2 * 255 + (1f - alphaFallPathInterpolator.getInterpolation(t)) * (0.2 * 255)
 */
class StartRecordPathInterpolator {
    companion object {
        const val ENTER_ANIMATION_FALL_DURATION = ENTER_ANIMATION_CHANGE_DURATION - ENTER_ANIMATION_RISE_DURATION
        //alpha动画的总时长 = 增加时长 + 减少时长
        const val ENTER_ANIMATION_FALL_ALPHA_DURATION_TOTAL = ENTER_ANIMATION_RISE_DURATION + ENTER_ANIMATION_FALL_ALPHA_DURATION
    }

    //波形alpha增加速率曲线（用于0-40%区间）
    private val alphaRisePathInterpolator = PathInterpolator(0.33f, 0f, 0.67f, 0.87f)

    //波形alpha减少速率曲线（用于40%-20%区间）
    private val alphaFallPathInterpolator = PathInterpolator(0.34f, -0.23f, 0.67f, 1f)

    //波形上升速率贝塞尔曲线（用于0-100%高度区间，对应2- maxHeight）
    private val heightRisePathInterpolator = PathInterpolator(0.51f, 0.03f, 0.52f, 1.02f)

    //波形下降速率贝塞尔曲线（用于100-0%高度区间，对应maxHeight - 2）
    private val heightFallPathInterpolator = PathInterpolator(0.48f, 0.02f, 0.33f, 1f)

    /**
     * 根据当前执行动画的时间决定当前波形的高度 默认高度 - 最大值 - 默认高度
     */
    fun getHeightInterpolation(currentAnimationTime: Float, defaultHeight: Int, changeHeight: Int): Float {
        val heightInterpolation = if (currentAnimationTime <= ENTER_ANIMATION_RISE_DURATION) {
            //执行高度上升动画期间
            heightRisePathInterpolator.getInterpolation(currentAnimationTime / ENTER_ANIMATION_RISE_DURATION)
        } else {
            //执行高度降低动画期间
            1f - heightFallPathInterpolator.getInterpolation((currentAnimationTime - ENTER_ANIMATION_RISE_DURATION) / (ENTER_ANIMATION_FALL_DURATION))
        }
//        DebugUtil.e(
//            "WaveItemView",
//            "heightInterpolation2= $heightInterpolation"
//        )
        return (defaultHeight + heightInterpolation * changeHeight)
    }

    /**
     * 根据当前执行动画的时间决定当前波形的alpha 默认alpha 0% - 40%纯黑 - 20%纯黑
     */
    fun getAlphaInterpolation(currentAnimationTime: Float): Int {
        val alphaInterpolation: Float
        return when {
            //执行高度上升动画期间
            //从0 - 40%开始进行alpha增加，也就是从透明到40%纯黑
            currentAnimationTime <= ENTER_ANIMATION_RISE_DURATION -> {
                alphaInterpolation =
                    alphaRisePathInterpolator.getInterpolation(currentAnimationTime / ENTER_ANIMATION_RISE_DURATION)
                (ALPHA_40_PERCENT * alphaInterpolation).toInt()
            }
            //执行高度降低动画期间
            //从40% -20%递减,alphaInterpolation是从1到0
            currentAnimationTime <= ENTER_ANIMATION_FALL_ALPHA_DURATION_TOTAL -> {
                alphaInterpolation = 1f - alphaFallPathInterpolator.getInterpolation(
                    (currentAnimationTime - ENTER_ANIMATION_RISE_DURATION)
                            / (ENTER_ANIMATION_FALL_DURATION)
                )
                (ALPHA_20_PERCENT + (ALPHA_20_PERCENT * alphaInterpolation)).toInt()
            }
            //动画执行完成期间，则位置alpha不变
            else -> {
                ALPHA_20_PERCENT
            }
        }
    }

    /**
     * 获取上升期间的波形高度百分比
     */
    fun getRiseHeightInterpolation(t: Float): Float {
        return heightRisePathInterpolator.getInterpolation(t)
    }

    /**
     * 获取上升期间的波形alpha百分比
     */
    fun getRiseAlphaInterpolation(t: Float): Float {
        return alphaRisePathInterpolator.getInterpolation(t)
    }

    /**
     * 获取下降期间的波形高度百分比
     */
    fun getFallHeightInterpolation(t: Float): Float {
        return 1f - heightFallPathInterpolator.getInterpolation(t)
    }

    /**
     * 获取下降期间的波形alpha变化百分比
     */
    fun getFallAlphaInterpolation(t: Float): Float {
        return 1f - alphaFallPathInterpolator.getInterpolation(t)
    }
}