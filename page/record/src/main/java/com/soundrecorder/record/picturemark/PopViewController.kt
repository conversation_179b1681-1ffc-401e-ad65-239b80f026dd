/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: PopViewController.java
 Description:
 Version: 1.0
 Date: 2009-12-15
 Author: zhengtaijuan
 -----------Revision History-----------
 <author> <date> <version> <desc>
 zhengtaijuan 2009-12-15 create
 */

package com.soundrecorder.record.picturemark

import android.os.CountDownTimer
import android.os.SystemClock
import android.view.View
import android.widget.FrameLayout
import androidx.activity.ComponentActivity
import androidx.core.view.contains
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.soundrecorder.base.utils.BaseUtil
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.buryingpoint.BuryingPoint
import com.soundrecorder.common.databean.markdata.MarkDataBean
import com.soundrecorder.common.utils.FunctionOption
import com.soundrecorder.common.utils.TipUtil
import com.soundrecorder.common.utils.TipUtil.Companion.setEnableShow
import com.soundrecorder.common.utils.ViewUtils.doOnAttachState
import com.soundrecorder.modulerouter.photoviewer.SHOW_BIG_IMAGE_REQUEST_CODE_X
import com.soundrecorder.modulerouter.recorder.PAUSED
import com.soundrecorder.modulerouter.recorder.RECORDING
import com.soundrecorder.modulerouter.recorder.RecorderServiceInterface
import com.soundrecorder.modulerouter.recorder.SaveFileState
import com.soundrecorder.modulerouter.utils.Injector
import com.soundrecorder.wavemark.mark.MarkHelper
import kotlinx.coroutines.Dispatchers.IO
import kotlinx.coroutines.Dispatchers.Main
import kotlinx.coroutines.Job
import kotlinx.coroutines.async
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class PopViewController : ViewModel(), View.OnClickListener, IPermissionCallback,
    DefaultLifecycleObserver {
    companion object {
        private const val TAG = "PopViewController"
        private const val TIME_10_SECONDS = 10000L
        private const val TIME_SECOND = 1000L
        private const val TIME_DUPLICATION_BACKGROUND = 150L
        private const val MAX_QUERY_COUNT = 5
        private const val TIME_QUERY_DELAY = 300L
    }

    private var popViewDataCallback: PopViewDataCallback? = null
    private var isBackground = true
    private var popViewWidget: PopViewWidget? = null
    private var mCountDownTimer: CountDownTimer? = null
    private var popPictureJob: Job? = null
    private var mPopPictures: List<PopPicture> = listOf()
    private var intoForegroundLastTime = -1L
    private var mPopPermission: PopPermissionController? = null
    private var mIsForeground = false

    private val recorderViewModelApi by lazy {
        Injector.injectFactory<RecorderServiceInterface>()
    }

    init {
        PopTimeSliceManager.onBindRecorderActivityOnCreate(
            recorderViewModelApi?.getCurrentStatus() ?: SaveFileState.INIT
        )
        mPopPermission = PopPermissionController(this)
    }

    fun setPopViewDataCallback(callback: PopViewDataCallback) {
        popViewDataCallback = callback
    }

    fun onConfigurationChangedBefore() {
        DebugUtil.i(TAG, "onConfigurationChangedBefore")
        dismissPopView()
    }

    //进入后台
    fun onBackground() {
        DebugUtil.i(TAG, "onBackground")
        mIsForeground = false
        dismissPopView(shouldAnimator = false)
        if (SystemClock.elapsedRealtime() - intoForegroundLastTime > TIME_DUPLICATION_BACKGROUND) {
            PopTimeSliceManager.reset()
        }
        val list: List<MarkDataBean> = recorderViewModelApi?.getMarkData() ?: listOf()
        val size = list.size
        val hasLessThanMax50 = size < MarkHelper.MAX_MARKER_COUNT
        val requestCode = popViewDataCallback?.getRequestCodeX() ?: -1
        DebugUtil.i(TAG, "requestCode = $requestCode, supportMarkRecommend == ${FunctionOption.isSupportPhotoMarkRecommend()}")
        val hasSupportRequestCode = requestCode in intArrayOf(-1, SHOW_BIG_IMAGE_REQUEST_CODE_X)
        if (FunctionOption.isSupportPhotoMarkRecommend() && hasSupportRequestCode && !isBackground && hasLessThanMax50) {
            //doRecordBackground执行条件
            // 1、智能图片开关已开启
            // 2、录制界面没有覆盖大图预览界面、相机界面、智能图片相关界面
            // 3、 isBackground是前台状态
            // 4、已标记总数小于50
            doRecordBackground()
        } else {
            DebugUtil.i(TAG,
                "onBackgroun ,hasSupportRequestCode=$hasSupportRequestCode, isBackground=$isBackground,hasLessThanMax50=$hasLessThanMax50")
        }
    }

    private fun doRecordBackground() {
        DebugUtil.i(TAG, "doRecordBackground")
        isBackground = true
        if (recorderViewModelApi?.getCurrentStatus() == RECORDING) {
            PopTimeSliceManager.startTimeSlice(recorderViewModelApi?.getAmplitudeCurrentTime() ?: 0)
        }
    }

    //退出后台，返回前台
    fun onForeground() {
        mIsForeground = true
        intoForegroundLastTime = SystemClock.elapsedRealtime()
        DebugUtil.i(TAG, "onForeground")
        val requestCode = popViewDataCallback?.getRequestCodeX() ?: -1
        DebugUtil.i(TAG, "requestCode = $requestCode")
        val hasSupportRequestCode = requestCode in intArrayOf(-1, SHOW_BIG_IMAGE_REQUEST_CODE_X)
        if (FunctionOption.isSupportPhotoMarkRecommend() && hasSupportRequestCode && isBackground) {
            doRecordForeground()
        } else {
            DebugUtil.i(TAG, "onForeground  hasSupportRequestCode=$hasSupportRequestCode, isBackground=$isBackground")
        }
    }

    private fun doRecordForeground() {
        DebugUtil.i(TAG, "doRecordForeground")
        isBackground = false
        val list: List<MarkDataBean> = recorderViewModelApi?.getMarkData() ?: listOf()
        if (list.size >= MarkHelper.MAX_MARKER_COUNT) {
            return
        }
        if (recorderViewModelApi?.getCurrentStatus() == RECORDING) {
            PopTimeSliceManager.endTimeSlice(recorderViewModelApi?.getAmplitudeCurrentTime() ?: 0)
        }
        queryPopPicture()
    }

    private fun queryPopPicture() {
        val timeSlices = PopTimeSliceManager.getTimeSlices().toTypedArray().toList()
        if (timeSlices.isNotEmpty()) {
            popPictureJob = viewModelScope.launch(IO) {
                val popPicturesDeferred = async {
                    var data = listOf<PopPicture>()
                    var count = MAX_QUERY_COUNT
                    while (count in 1..MAX_QUERY_COUNT) {
                        delay(TIME_QUERY_DELAY)
                        count -= 1
                        kotlin.runCatching {
                            data = PopPictureManager.query(
                                timeSlices, MAX_QUERY_COUNT * TIME_QUERY_DELAY
                            )
                        }.onFailure {
                            DebugUtil.e(TAG, "queryPopPicture count: $count, error: $it")
                        }

                        if (data.isNotEmpty()) {
                            break
                        }
                    }
                    DebugUtil.i(TAG, "查询次数：${MAX_QUERY_COUNT - count}")
                    data
                }
                val popPictures = popPicturesDeferred.await()
                if (popPictures.isNotEmpty()) {
                    DebugUtil.i(TAG, "相机图片查询不为空，显示气泡")
                    withContext(Main) {
                        if (FunctionOption.isSupportPhotoMarkRecommend()) {
                            showPopView(popPictures)
                        }
                    }
                } else {
                    DebugUtil.i(TAG, "相机图片查询为空，无需显示气泡")
                }
            }
        } else {
            DebugUtil.i(TAG, "时间片为空，无需查询相机图片数据")
        }
    }

    /**
     * 录制界面权限同意后，不会重新执行onStart
     * 无法对isBackground 赋值，需要在权限授予成功后手动赋值
     */
    fun updateBackgroundValueFalse() {
        isBackground = false
    }

    fun onStateChange(state: Int) {
        when (state) {
            RECORDING -> {
                DebugUtil.i(TAG, "onStateChange RECORDING,isBackground= $isBackground")
                if (popViewDataCallback != null && isBackground) {
                    PopTimeSliceManager.startTimeSlice(recorderViewModelApi?.getAmplitudeCurrentTime() ?: 0)
                }
            }

            PAUSED -> {
                DebugUtil.i(TAG, "onStateChange PAUSED,isBackground= $isBackground")
                if (popViewDataCallback != null && isBackground) {
                    PopTimeSliceManager.endTimeSlice(recorderViewModelApi?.getAmplitudeCurrentTime() ?: 0)
                }
            }
        }
    }

    //显示气泡
    private fun showPopView(popPictures: List<PopPicture>) {
        DebugUtil.i(TAG, "showPopView")
        TipUtil.dismissSelf(TipUtil.TYPE_PICTURE_MARK)
        mPopPictures = popPictures
        val contentView = popViewDataCallback?.getContentView() ?: return
        val pictureMarkView = popViewDataCallback?.getPictureMarkView() ?: return
        popViewWidget?.let {
            if (contentView.contains(it)) {
                contentView.removeView(it)
            }
        }
        if (popViewWidget == null) {
            popViewWidget = PopViewWidget(contentView.context)
        }
        popViewWidget?.doOnAttachState { _, state ->
            pictureMarkView.setEnableShow(!state)
        }
        contentView.addView(
            popViewWidget,
            FrameLayout.LayoutParams(
                FrameLayout.LayoutParams.MATCH_PARENT,
                FrameLayout.LayoutParams.MATCH_PARENT
            )
        )
        popViewWidget?.apply {
//            setPopUpLocation(pictureMarkView)
            setRedDotNum(popPictures.size)
            setPopPictures(popPictures)
            setPopUpOnClickListener(this@PopViewController)
        }
        if (mCountDownTimer == null) {
            mCountDownTimer = object : CountDownTimer(TIME_10_SECONDS, TIME_SECOND) {
                override fun onTick(millisUntilFinished: Long) {
                    if (!contentView.contains(popViewWidget ?: return)) {
                        cancel()
                    }
                }

                override fun onFinish() {
                    dismissPopView()
                }
            }
        }
        mCountDownTimer?.start()
        BuryingPoint.shouPopNumber()
    }

    //隐藏气泡
    private fun dismissPopView(fromClick: Boolean = false, shouldAnimator: Boolean = true) {
        DebugUtil.i(TAG, "dismissPopView")
        popPictureJob?.cancel()
        val contentView = popViewDataCallback?.getContentView() ?: return
        val view = this.popViewWidget ?: return
        if (contentView.contains(view)) {
            if (shouldAnimator) {
                view.exitAndRemoveSelf()
            } else {
                contentView.removeView(view)
            }
        }
        this.popViewWidget = null
        mCountDownTimer?.cancel()
        if (!fromClick) {
            BuryingPoint.dismissPopNumberExcludeUserAction()
        }
    }

    override fun onClick(v: View?) {
        /**
        when (v?.id) {
            R.id.cl_popup_root_view -> {
                dismissPopView(true, shouldAnimator = false)
                val recorderActivity = v.context as? RecorderActivity
                if (recorderActivity != null) {
                    if (mPopPictures.size == 1) {
                        val shareView = v.findViewById<ImageView>(R.id.iv_picture_recommendation)
                        if (recorderActivity.topActivity() !is PhotoViewerActivity) {
                            recorderActivity.launchPhotoView(mPopPictures, shareView)
                        }
                    } else if (mPopPictures.size > 1) {
                        DebugUtil.i("taskFirst", recorderActivity.topActivity().toString())
                        if (recorderActivity.topActivity() !is PictureSelectActivity) {
                            recorderActivity.gotoPhotoActivity(mPopPictures)
                        }
                    }
                }
                BuryingPoint.clickOnPopNumber()
            }
        }*/
    }

    fun onBackPressed(): Boolean {
        DebugUtil.i(TAG, "onBackPressed")
        val contentView = popViewDataCallback?.getContentView() ?: return false
        if (contentView.contains(popViewWidget ?: return false)) {
            popViewWidget?.exitAndRemoveSelf()
            popViewWidget = null
            return true
        }
        return false
    }

    /**
     * 初始化并注册权限申请
     */
    fun registerImagesPermission(activity: ComponentActivity) {
        if (!BaseUtil.isAndroidUOrLater) {
            return
        }
        mPopPermission?.registerImagesPermission(activity, this)
    }

    /**
     * 检查权限提示是否需要弹出
     */
    fun checkReadImagePermissionTips() {
        if (!BaseUtil.isAndroidUOrLater) {
            return
        }
        if (mPopPermission?.mPressHomeBtnNeedShowPermissionTipsNextOnResume == true) {
            checkImagesPermissionShowTips()
        } else {
            mPopPermission?.checkReadImagePermissionTips()
        }
    }

    /**
     * 弹出权限提示
     */
    private fun checkImagesPermissionShowTips() {
        if (!BaseUtil.isAndroidUOrLater) {
            return
        }
        if (popViewDataCallback?.canShowReadImageSnackOnResume() == true) {
            mPopPermission?.launchImagesPermissionTips(popViewDataCallback?.getRootView())
        } else {
            DebugUtil.i(
                TAG,
                "checkImagesPermissionShowTips not match canShowReadImageSnackOnResume=${popViewDataCallback?.canShowReadImageSnackOnResume()}"
            )
        }
    }

    override fun onResume(owner: LifecycleOwner) {
        super.onResume(owner)
        checkImagesPermissionShowTips()
    }

    override fun onDestroy(owner: LifecycleOwner) {
        super.onDestroy(owner)
        mPopPermission?.release()
        mPopPermission = null
    }

    override fun onCleared() {
        super.onCleared()
        popViewDataCallback = null
        popViewWidget = null
        mCountDownTimer?.cancel()
        mCountDownTimer = null
        PopTimeSliceManager.clear()
        popPictureJob?.cancel()
    }

    override fun isForeground(): Boolean {
        return mIsForeground
    }
}

interface PopViewDataCallback {
    fun getContentView(): FrameLayout
    fun getPictureMarkView(): View
    fun getRequestCodeX(): Int
    fun getRootView(): View?

    /**
     * onResume准备显示图片标记权限引导弹窗，check是否可以显示
     */
    fun canShowReadImageSnackOnResume(): Boolean
}