<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:paddingTop="@dimen/dp16">

    <LinearLayout
        android:id="@+id/layout_data_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        tools:ignore="UselessParent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/data_speaker"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/background_speaker"
            app:max_padding_end="@dimen/dp14">

            <LinearLayout
                android:id="@+id/ll_speaker"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                tools:ignore="MissingConstraints,UseCompoundDrawables">

                <TextView
                    android:id="@+id/tv_speaker"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:ellipsize="end"
                    android:fontFamily="sans-serif-medium"
                    android:includeFontPadding="false"
                    android:lines="1"
                    android:maxWidth="@dimen/dp100"
                    android:paddingStart="@dimen/dp8"
                    android:paddingTop="@dimen/dp6"
                    android:paddingEnd="@dimen/dp10"
                    android:paddingBottom="@dimen/dp6"
                    android:textColor="@color/percent_85_black"
                    android:textSize="@dimen/sp10"
                    tools:text="@string/convert_speaker" />

            </LinearLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>

        <TextView
            android:id="@+id/start_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:contentDescription=""
            android:ellipsize="end"
            android:fontFamily="sys-sans-en"
            android:fontFeatureSettings="tnum"
            android:lines="1"
            android:paddingTop="@dimen/dp4"
            android:paddingBottom="@dimen/dp4"
            android:text="00:12"
            android:textColor="?attr/couiColorLabelSecondary"
            android:textSize="@dimen/sp14" />
    </LinearLayout>

    <TextView
        android:id="@+id/item_content"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp16"
        android:layout_marginTop="@dimen/dp16"
        android:layout_marginEnd="@dimen/dp16"
        android:layout_marginBottom="@dimen/dp16"

        android:textColor="?attr/couiColorLabelSecondary"/>


</LinearLayout>