<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/cl_popup_root_view"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <TextView
        android:id="@+id/tv_picture_num"
        android:layout_width="@dimen/dp18"
        android:layout_height="@dimen/dp18"
        android:layout_marginTop="@dimen/dp2"
        android:layout_marginEnd="@dimen/dp7"
        android:background="@drawable/ic_recorder_red_circle"
        android:gravity="center"
        android:textColor="@color/white_color"
        android:textSize="@dimen/dp10"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="3" />

    <LinearLayout
        android:id="@+id/ll_body"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/ic_popup_background"
        android:orientation="vertical"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <com.google.android.material.imageview.ShapeableImageView
            android:id="@+id/iv_picture_recommendation"
            android:layout_width="@dimen/dp60"
            android:layout_height="@dimen/dp60"
            android:layout_margin="@dimen/dp4"
            android:forceDarkAllowed="false"
            android:scaleType="centerCrop"
            android:src="@drawable/ic_launcher_recorder"
            app:shapeAppearance="@style/roundedCornerStyle" />

        <TextView
            android:id="@+id/tv_text_recommendation"
            android:layout_width="@dimen/dp60"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp4"
            android:layout_marginTop="@dimen/dp2"
            android:layout_marginEnd="@dimen/dp4"
            android:layout_marginBottom="@dimen/dp7"
            android:breakStrategy="high_quality"
            android:ellipsize="end"
            android:hyphenationFrequency="full"
            android:lineHeight="@dimen/dp14"
            android:maxLines="4"
            android:text="@string/photo_mark_recommend_popup_insert"
            android:textColor="@color/percent_55_black"
            android:textSize="@dimen/dp10" />
    </LinearLayout>


</androidx.constraintlayout.widget.ConstraintLayout>