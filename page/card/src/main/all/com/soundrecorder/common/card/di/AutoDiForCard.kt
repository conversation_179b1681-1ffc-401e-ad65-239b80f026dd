/*********************************************************************
 * * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  AutoDiForCard.kt
 * * Description : AutoDiForCard
 * * Version     : 1.0
 * * Date        : 2025/04/18
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 *    renjiahao                             1.0        create
 ***********************************************************************/
package com.soundrecorder.common.card.di

import com.soundrecorder.common.card.api.WidgetCodeApi
import com.soundrecorder.modulerouter.WidgetCodeInterface
import org.koin.dsl.module

object AutoDiForCard {
    val cardModule = module {
        single<WidgetCodeInterface>(createdAtStart = true) {
            WidgetCodeApi
        }
    }
}