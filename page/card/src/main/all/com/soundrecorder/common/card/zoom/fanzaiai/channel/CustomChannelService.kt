/*********************************************************************
 * * Copyright (C), 2023, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  CustomChannelService.kt
 * * Description :
 * * Version     : 1.0
 * * Date        : 2025/3/18
 * * Author      : W9067780
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.common.card.zoom.fanzaiai.channel

import com.soundrecorder.base.utils.DebugUtil
import org.hapjs.features.channel.ChannelService
import org.hapjs.features.channel.HapChannelManager

class CustomChannelService : ChannelService() {
    companion object {
        const val TAG = "CustomChannelService"
    }

    override fun onChannelServiceCreate() {
        super.onChannelServiceCreate()
        DebugUtil.d(TAG, "onChannelServiceCreate")
        HapChannelManager.get().initialize(applicationContext)
        val handler = CustomChannelHandler(applicationContext)
        HapChannelManager.get().setDefaultChannelHandler(handler)
    }
}