/*
 Copyright (C), 2008-2023 OPLUS Mobile Comm Corp., Ltd.
 File: WidgetCodeApi
 Description:
 Version: 1.0
 Date: 2023/3/22
 Author: W9013333(v-z<PERSON><PERSON><PERSON><PERSON><PERSON>@oppo.com)
 -----------Revision History-----------
 <author> <date> <version> <desc>
 W9013333 2023/3/22 1.0 create
 */

package com.soundrecorder.common.card.api

import com.oplus.cardwidget.util.getCardType
import com.soundrecorder.modulerouter.WidgetCodeInterface

object WidgetCodeApi : WidgetCodeInterface {
    override fun getCardType(widgetCode: String): Int {
        return widgetCode.getCardType()
    }
}