<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.soundrecorder.common.card">

    <queries>
        <package android:name="com.oplus.smartengine" />
    </queries>
    <application>
        <meta-data
            android:name="com.soundRecorder.smallCard.versionCode"
            android:value="3" />

        <meta-data
            android:name="com.oplus.ocs.card.AUTH_CODE"
            android:value="ADBEAiBgCgjh5ix1bL0MhmtHS5qRpFxu697CB6sgnGdXDcwAdAIgA5bQ0llmvg59RjCbqtsz6x3Ui00a25gVKxdUTeNjJIZuGVlN"
            tools:replace="android:value"/>

        <!--速览外销Oneplus-->
        <provider
            android:name="com.soundrecorder.common.card.OnePlusSmallCardWidgetProvider"
            android:authorities="com.soundrecorder.common.provider.recorder.smallcard"
            android:enabled="true"
            android:exported="true"
            android:permission="com.oplus.permission.safe.ASSISTANT">
            <intent-filter>
                <action android:name="android.appcard.action.APPCARD_UPDATE" />
            </intent-filter>
            <meta-data
                android:name="android.card.provider"
                android:resource="@xml/one_plus_small_card_app_widget" />
        </provider>
        <!--外销小卡-->
        <provider
            android:name="com.soundrecorder.common.card.RecordSeedlingSmallCardWidgetProvider"
            android:authorities="com.oplus.soundRecorder.RecordSeedlingSmallCardWidgetProvider"
            android:enabled="true"
            android:exported="true"
            android:permission="com.oplus.permission.safe.ASSISTANT">
            <intent-filter>
                <action android:name="com.oplus.seedling.action.SEEDLING_CARD" />
            </intent-filter>
            <meta-data
                android:name="oplus.seedling.provider"
                android:value="RecordSeeding4.upk" />
        </provider>
    </application>
</manifest>
