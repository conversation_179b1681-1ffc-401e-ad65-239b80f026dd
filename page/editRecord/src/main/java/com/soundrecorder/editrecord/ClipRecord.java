/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: ClipRecord
 Description:
 Version: 1.0
 Date: 2022/12/2
 Author: ********(<EMAIL>)
 -----------Revision History-----------
 <author> <date> <version> <desc>
 ******** 2022/12/2 1.0 create
 */

// OPLUS Java File Skip Rule:MethodLength,MethodComplexity,FileLength,LineLength,IllegalCatch
package com.soundrecorder.editrecord;

import android.annotation.SuppressLint;
import android.content.ContentValues;
import android.content.Context;
import android.media.AudioFormat;
import android.media.MediaExtractor;
import android.media.MediaFormat;
import android.media.MediaMetadataRetriever;
import android.net.Uri;
import android.os.ParcelFileDescriptor;
import android.provider.MediaStore;

import com.soundrecorder.base.utils.BaseUtil;
import com.soundrecorder.base.utils.DebugUtil;
import com.soundrecorder.base.utils.FileUtils;
import com.soundrecorder.base.utils.TimeUtils;
import com.soundrecorder.common.constant.Constants;
import com.soundrecorder.common.constant.RecordConstant;
import com.soundrecorder.common.databean.Record;
import com.soundrecorder.common.databean.markdata.MarkDataBean;
import com.soundrecorder.common.db.MediaDBUtils;
import com.soundrecorder.common.executor.ExecutorManager;
import com.soundrecorder.common.utils.AudioNameUtils;
import com.soundrecorder.editrecord.utils.RecorderHeaderUtils;

import java.io.File;
import java.io.FileDescriptor;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.ByteBuffer;
import java.nio.channels.FileChannel;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class ClipRecord {
    public static final long OP_EXTRACT = 1;
    public static final long OP_DELETE = 2;
    public static final int BIT_NUMBER_8 = 8;
    public static final int BIT_NUMBER_16 = 16;
    public static final int BIT_NUMBER_32 = 32;
    public static final int DEFAULT_NUMBER_1 = 1;
    public static final int DEFAULT_NUMBER_44100 = 44100;
    public static final String CLIP = "CUT";
    private static String TAG = "ClipRecord";
    private static final String AUDIO = "audio";
    private static final String TEMP = "TMP";
    private static final byte[] HEADER_AMR = "#!AMR\n".getBytes(Constants.UTF_8);
    private static final byte[] HEADER_AMR_WB = "#!AMR-WB\n".getBytes(Constants.UTF_8);
    private static final long US_TIMES = 1000;
    private static final float SECOND_PER_MILLISION = 1000F;
    private static final int PROGRESS_100 = 100;
    private static final List<Integer> AVPRIV_AUDIO_SAMPLE_RATES =
            Arrays.asList(96000, 88200, 64000, 48000, 44100, 32000, 24000, 22050, 16000, 12000, 11025, 8000, 7350);
    private Context mContext = null;
    private Record mClippedObject = null;
    private FileDescriptor mInFileDescriptor = null;
    private String mMimeType = null;
    private String mSuffix;
    private long mClipStartDuration;
    private long mClipTotalTime;
    private long mClipCurrentTime;
    private Uri mClipUri = null;
    private List<Integer> mClipAmplitudes = null;
    private List<Integer> mObjAmplitudes = null;
    private List<MarkDataBean> mMarkList = null;
    private ArrayList<MarkDataBean> mClipMarkList = null;
    private OnProgressChangeListener mOnProgressChangeListener = null;
    private int mProgress = 0;
    private FileOutputStream mFileOutputStream = null;
    private long mPosition = 0;
    private ParcelFileDescriptor mParcelFileDescriptor;
    private String mClipPath = null;
    private int mClipCode;
    private boolean mIsCanceled = false;

    public ClipRecord(Context context, Record record) {
        mClippedObject = record;
        mContext = context;
        init();
    }

    private void init() {
        mMimeType = mClippedObject.getMimeType();
        Uri uri = MediaDBUtils.genUri(mClippedObject.getId());
        try {
            mParcelFileDescriptor = mContext.getContentResolver().openFileDescriptor(uri, "r");
        } catch (FileNotFoundException e) {
            DebugUtil.e(TAG, "openFileDescriptor error", e);
            mClipCode = ClipResultCode.CLIP_OPEN_SOURCE_FILE_FAIL;
        }
        if (mParcelFileDescriptor != null) {
            mInFileDescriptor = mParcelFileDescriptor.getFileDescriptor();
        }
        DebugUtil.i(TAG, "from media db mMimeType:" + mMimeType);
        switch (mMimeType) {
            case RecordConstant.MIMETYPE_MP3:
                mSuffix = ".mp3";
                break;
            case RecordConstant.MIMETYPE_AMR:
            case RecordConstant.MIMETYPE_AMR_WB:
            case RecordConstant.MIMETYPE_3GPP:
                mSuffix = ".amr";
                break;
            case RecordConstant.MIMETYPE_WAV:
                mSuffix = ".wav";
                break;
            case RecordConstant.MIMETYPE_ACC:
            case RecordConstant.MIMETYPE_ACC_ADTS:
                mSuffix = ".aac";
                break;
            default:
                DebugUtil.e(TAG, "not support mime type:" + mMimeType);
                break;
        }
    }

    public void setCanceled(boolean mIsCanceled, boolean needDelete) {
        DebugUtil.i(TAG, "setCanceled " + mIsCanceled);
        this.mIsCanceled = mIsCanceled;
        if (needDelete && mClipUri != null) {
            ExecutorManager.getSingleExecutor().execute(() -> {
                MediaDBUtils.delete(mClipUri);
            });
        }
    }

    /**
     * Extraction of selected audio clip
     * @param clipStart
     * @param clipEnd
     * @param totalDuration
     */
    public void extract(long clipStart, long clipEnd, long totalDuration) {
        if (clipStart > clipEnd) {
            DebugUtil.e(TAG, "clip start duration can't larger clip end duration");
            mClipCode = ClipResultCode.CLIP_TIME_RANGE_ERROR;
            return;
        }
        mClipStartDuration = clipStart;
        long start = System.currentTimeMillis();
        String clipName = AudioNameUtils.genDefaultFileName(mClippedObject.getRelativePath(), mClippedObject.getDisplayName());
        try {
            mClipUri = MediaDBUtils.genUri(genClipContentValues(clipName));
        } catch (Exception e) {
            DebugUtil.e(TAG, "extract error", e);
        }
        if (mClipUri == null) {
            DebugUtil.i(TAG, "exact genClipUri null, exact failed, ");
            mClipCode = ClipResultCode.CLIP_GEN_DEST_FILE_FAIL;
            return;
        }
        if (mInFileDescriptor == null) {
            DebugUtil.i(TAG, "mInFileDescriptor is null, exact failed, ");
            return;
        }
        DebugUtil.i(TAG, "extract : mClipUri: " + mClipUri);
        boolean clipResult = false;
        MediaExtractor extractor = null;
        FileChannel outChannel = null;
        try {
            extractor = new MediaExtractor();
            extractor.setDataSource(mInFileDescriptor);
            int track = getAudioTrack(extractor);
            if (track < 0) {
                mClipCode = ClipResultCode.CLIP_AUDIO_TRACK_FAIL;
                return;
            }
            extractor.selectTrack(track);
            mFileOutputStream = (FileOutputStream) mContext.getContentResolver().openOutputStream(mClipUri);
            if (mFileOutputStream == null) {
                DebugUtil.e(TAG, "mFileOutputStream is null");
                mClipCode = ClipResultCode.CLIP_OPEN_DEST_FILE_FAIL;
                return;
            }
            outChannel = mFileOutputStream.getChannel();
            switch (mMimeType) {
                case RecordConstant.MIMETYPE_MP3:
                    mPosition = 0;
                    clipResult = clip(extractor, outChannel, clipStart * US_TIMES, clipEnd * US_TIMES, totalDuration * US_TIMES);
                    break;
                case RecordConstant.MIMETYPE_3GPP:
                case RecordConstant.MIMETYPE_AMR:
                    addHeader(mFileOutputStream, HEADER_AMR);
                    mPosition = HEADER_AMR.length;
                    clipResult = clip(extractor, outChannel, clipStart * US_TIMES, clipEnd * US_TIMES, totalDuration * US_TIMES);
                    break;
                case RecordConstant.MIMETYPE_AMR_WB:
                    addHeader(mFileOutputStream, HEADER_AMR_WB);
                    mPosition = HEADER_AMR_WB.length;
                    clipResult = clip(extractor, outChannel, clipStart * US_TIMES, clipEnd * US_TIMES, totalDuration * US_TIMES);
                    break;
                case RecordConstant.MIMETYPE_WAV:
                    byte[] wavHeader = getWaveHeaderForExtract(extractor, clipStart, clipEnd);
                    if (wavHeader == null) {
                        break;
                    }
                    addHeader(mFileOutputStream, wavHeader);
                    mPosition = wavHeader.length;
                    clipResult = clip(extractor, outChannel, clipStart * US_TIMES, clipEnd * US_TIMES, totalDuration * US_TIMES);
                    break;
                case RecordConstant.MIMETYPE_ACC:
                case RecordConstant.MIMETYPE_ACC_ADTS:
                    clipResult = clipAAC(extractor, outChannel, clipStart * US_TIMES, clipEnd * US_TIMES, totalDuration * US_TIMES);
                    break;
                default:
                    break;
            }
            if (clipResult) {
                mClipCode = ClipResultCode.CLIP_SUCCESS;
                DebugUtil.i(TAG, "clip file success");
            } else if (!mIsCanceled) {
                if (mClipUri != null) {
                    MediaDBUtils.delete(mClipUri);
                    mClipUri = null;
                }
                DebugUtil.e(TAG, "clip file failed");
            }
            start = System.currentTimeMillis() - start;
            DebugUtil.i(TAG, "clip use time : " + start + " ms");
        } catch (IOException e) {
            DebugUtil.e(TAG, "open clip file error", e);
        } finally {
            if (extractor != null) {
                extractor.release();
            }

            if (mFileOutputStream != null) {
                try {
                    mFileOutputStream.close();
                } catch (IOException e) {
                    DebugUtil.e(TAG, "close outputStream error", e);
                }
            }

            if (outChannel != null) {
                try {
                    outChannel.close();
                } catch (IOException e) {
                    DebugUtil.e(TAG, "close outChannel error", e);
                }
            }
            if (mParcelFileDescriptor != null) {
                try {
                    mParcelFileDescriptor.close();
                } catch (IOException e) {
                    DebugUtil.e(TAG, "close mParcelFileDescriptor error", e);
                }
            }
        }
    }

    public void delete(long clipStart, long clipEnd, long totalDuration) {
        if (clipStart > clipEnd) {
            DebugUtil.e(TAG, "clip start duration can't larger clip end duration");
            mClipCode = ClipResultCode.CLIP_TIME_RANGE_ERROR;
            return;
        }
        mClipStartDuration = clipStart;
        long start = System.currentTimeMillis();
        String clipName = AudioNameUtils.genDefaultFileName(mClippedObject.getRelativePath(), mClippedObject.getDisplayName());
        try {
            mClipUri = MediaDBUtils.genUri(genClipContentValues(clipName));
        } catch (Exception e) {
            DebugUtil.e(TAG, "delete error", e);
        }
        if (mClipUri == null) {
            DebugUtil.i(TAG, "genClipUri is null, delete failed, ");
            mClipCode = ClipResultCode.CLIP_GEN_DEST_FILE_FAIL;
            return;
        }
        if (mInFileDescriptor == null) {
            DebugUtil.i(TAG, "mInFileDescriptor is null, delete failed, ");
            return;
        }
        boolean clipResult = false;
        MediaExtractor extractor = null;
        FileChannel outChannel = null;
        try {
            extractor = new MediaExtractor();
            extractor.setDataSource(mInFileDescriptor);
            int track = getAudioTrack(extractor);
            if (track < 0) {
                mClipCode = ClipResultCode.CLIP_AUDIO_TRACK_FAIL;
                return;
            }
            extractor.selectTrack(track);
            mFileOutputStream = (FileOutputStream) mContext.getContentResolver().openOutputStream(mClipUri);
            if (mFileOutputStream == null) {
                DebugUtil.e(TAG, "mFileOutputStream is null");
                mClipCode = ClipResultCode.CLIP_OPEN_DEST_FILE_FAIL;
                return;
            }
            outChannel = mFileOutputStream.getChannel();
            switch (mMimeType) {
                case RecordConstant.MIMETYPE_MP3:
                    mPosition = 0;
                    clipResult = clipRevert(extractor, outChannel, clipStart * US_TIMES, clipEnd * US_TIMES, totalDuration * US_TIMES);
                    break;
                case RecordConstant.MIMETYPE_3GPP:
                case RecordConstant.MIMETYPE_AMR:
                    addHeader(mFileOutputStream, HEADER_AMR);
                    mPosition = HEADER_AMR.length;
                    clipResult = clipRevert(extractor, outChannel, clipStart * US_TIMES, clipEnd * US_TIMES, totalDuration * US_TIMES);
                    break;
                case RecordConstant.MIMETYPE_AMR_WB:
                    addHeader(mFileOutputStream, HEADER_AMR_WB);
                    mPosition = HEADER_AMR_WB.length;
                    clipResult = clipRevert(extractor, outChannel, clipStart * US_TIMES, clipEnd * US_TIMES, totalDuration * US_TIMES);
                    break;
                case RecordConstant.MIMETYPE_WAV:
                    byte[] wavHeader = getWaveHeaderForDelete(extractor, clipStart, clipEnd, totalDuration);
                    if (wavHeader == null) {
                        break;
                    }
                    addHeader(mFileOutputStream, wavHeader);
                    mPosition = wavHeader.length;
                    clipResult = clipRevert(extractor, outChannel, clipStart * US_TIMES, clipEnd * US_TIMES, totalDuration * US_TIMES);
                    break;
                case RecordConstant.MIMETYPE_ACC:
                case RecordConstant.MIMETYPE_ACC_ADTS:
                    clipResult = clipRevertAAC(extractor, outChannel, clipStart * US_TIMES, clipEnd * US_TIMES, totalDuration * US_TIMES);
                    break;
                default:
                    break;
            }
            if (clipResult) {
                mClipCode = ClipResultCode.CLIP_SUCCESS;
                DebugUtil.i(TAG, "clip file success");
            } else if (!mIsCanceled) {
                if (mClipUri != null) {
                    MediaDBUtils.delete(mClipUri);
                }
                DebugUtil.e(TAG, "clip file failed");
            }
            start = System.currentTimeMillis() - start;
            DebugUtil.i(TAG, "clip use time : " + start + " ms");
        } catch (Exception e) {
            DebugUtil.e(TAG, "open clip file error", e);
        } finally {
            if (mFileOutputStream != null) {
                try {
                    mFileOutputStream.close();
                } catch (IOException e) {
                    DebugUtil.e(TAG, "close clip file error", e);
                }
            }
            if (outChannel != null) {
                try {
                    outChannel.close();
                } catch (IOException e) {
                    DebugUtil.e(TAG, "close clip file channel error", e);
                }
            }
            if (extractor != null) {
                extractor.release();
            }
            if (mParcelFileDescriptor != null) {
                try {
                    mParcelFileDescriptor.close();
                } catch (IOException e) {
                    DebugUtil.e(TAG, "close mParcelFileDescriptor error", e);
                }
            }
        }
    }

    private ContentValues genClipContentValues(String name) {
        mClipPath = FileUtils.core2FullWithEdit(name, mClippedObject.getData());
        long current = System.currentTimeMillis();
        String absPath = "";
        ContentValues values = new ContentValues();
        values.put(MediaStore.Audio.Media.IS_MUSIC, "0");
        values.put(MediaStore.Audio.Media.DISPLAY_NAME, name);
        values.put(MediaStore.Audio.Media.TITLE, MediaDBUtils.getTitleByName(name));
        values.put(MediaStore.Audio.Media.DATE_ADDED, current / TimeUtils.TIME_ONE_SECOND);
        values.put(MediaStore.Audio.Media.DATE_MODIFIED, current / TimeUtils.TIME_ONE_SECOND);
        values.put(MediaStore.Audio.Media.MIME_TYPE, mMimeType);
        if (BaseUtil.isAndroidQOrLater()) {
            values.put(MediaStore.Audio.Media.RELATIVE_PATH, mClippedObject.getRelativePath());
        } else {
            File file = new File(mClippedObject.getData());
            File clipFile = new File(file.getParent(), name);
            values.put(MediaStore.Audio.Media.DATA, clipFile.getAbsolutePath());
            absPath = clipFile.getAbsolutePath();
        }
        DebugUtil.i(TAG, name + " clip values:" + values.toString().replace(absPath, ""));
        return values;
    }

    private boolean clip(MediaExtractor extractor, FileChannel outChannel, long start, long end, long duration) {
        try {
            DebugUtil.i(TAG, "start =" + start + " , end = " + end + " , duration = " + duration);
            clipAmplitudes(start, end, duration);
            clipMarkList(start, end, duration);
            mClipTotalTime = end - start;
            boolean clipResult = false;
            extractor.seekTo(start, MediaExtractor.SEEK_TO_PREVIOUS_SYNC);
            int iSampleSize = (int) extractor.getSampleSize();
            if (iSampleSize <= 0) {
                DebugUtil.e(TAG, "iSampleSize is error,iSampleSize = " + iSampleSize);
                mClipCode = ClipResultCode.CLIP_SAMPLE_SIZE_ERROR;
                return false;
            }
            ByteBuffer buffer = ByteBuffer.allocate(iSampleSize * 2);
            int readSampleDataLength = 0;
            do {
                buffer.clear();
                readSampleDataLength = extractor.readSampleData(buffer, 0);
                long timeStamp = extractor.getSampleTime();
                mClipCurrentTime = timeStamp - start;
                if (readSampleDataLength < 0) {
                    DebugUtil.i(TAG, "break,timestamp = " + timeStamp + " , sampleSize = " + readSampleDataLength);
                    clipResult = true;
                    break;
                }
                if (timeStamp > end) {
                    DebugUtil.i(TAG, "break,timestamp = " + timeStamp + " , end = " + end);
                    clipResult = true;
                    break;
                }
                mPosition += readSampleDataLength;
                buffer.rewind();
                buffer.limit(readSampleDataLength);
                try {
                    outChannel.write(buffer);
                    outChannel.position(mPosition);
                } catch (IOException e) {
                    DebugUtil.e(TAG, "clip write error:", e);
                    mClipCode = mIsCanceled ? ClipResultCode.CLIP_IS_CANCELED : ClipResultCode.CLIP_WRITE_FAIL;
                    break;
                }
                extractor.advance();
                updateProgress();
            } while (readSampleDataLength > 0 && !mIsCanceled);
            return clipResult;
        } catch (Throwable ignored) {
            DebugUtil.e(TAG, "clip error:", ignored);
            mClipCode = ClipResultCode.CLIP_ERROR;
            return false;
        }
    }

    @SuppressLint("NewApi")
    private boolean clipAAC(MediaExtractor extractor, FileChannel outChannel, long start, long end, long duration) {
        try {
            DebugUtil.i(TAG, "start =" + start + " , end = " + end + " ,duration = " + duration);
            clipAmplitudes(start, end, duration);
            clipMarkList(start, end, duration);
            mClipTotalTime = end - start;
            boolean clipResult = false;
            extractor.seekTo(start, MediaExtractor.SEEK_TO_PREVIOUS_SYNC);
            int iSampleSize = (int) extractor.getSampleSize();
            if (iSampleSize <= 0) {
                DebugUtil.e(TAG, "iSampleSize is error,iSampleSize = " + iSampleSize);
                mClipCode = ClipResultCode.CLIP_SAMPLE_SIZE_ERROR;
                return false;
            }
            int trackIndex = extractor.getSampleTrackIndex();
            if (trackIndex == -1) {
                DebugUtil.e(TAG, "getSampleTrackIndex is -1 ");
                mClipCode = ClipResultCode.CLIP_SAMPLE_TRACK_INDEX_ERROR;
                return false;
            }

            MediaFormat mediaFormat = extractor.getTrackFormat(trackIndex);
            int channelCount = 0;
            int sampleRate = 0;
            int profile = 0;
            if (BaseUtil.isAndroidQOrLater()) {
                channelCount = mediaFormat.getInteger(MediaFormat.KEY_CHANNEL_COUNT, 1);
                sampleRate = mediaFormat.getInteger(MediaFormat.KEY_SAMPLE_RATE, 1);
                profile = mediaFormat.getInteger(MediaFormat.KEY_AAC_PROFILE, 2);
            } else {
                channelCount = mediaFormat.getInteger(MediaFormat.KEY_CHANNEL_COUNT);
                sampleRate = mediaFormat.getInteger(MediaFormat.KEY_SAMPLE_RATE);
                profile = mediaFormat.getInteger(MediaFormat.KEY_AAC_PROFILE);
            }
            // default is 44100
            int freqIdx = AVPRIV_AUDIO_SAMPLE_RATES.contains(sampleRate) ? AVPRIV_AUDIO_SAMPLE_RATES.indexOf(sampleRate) : 4;
            DebugUtil.i(TAG, "profile " + profile + " sampleRate " + sampleRate + " freqIdx " + freqIdx + " channelCount " + channelCount);
            ByteBuffer buffer = ByteBuffer.allocate(iSampleSize * 2);
            int readSampleDataLength = 0;
            do {
                buffer.clear();
                readSampleDataLength = extractor.readSampleData(buffer, 0);
                long timeStamp = extractor.getSampleTime();
                mClipCurrentTime = timeStamp - start;
                if (readSampleDataLength < 0) {
                    DebugUtil.i(TAG, "break,timestamp = " + timeStamp + " , sampleSize = " + readSampleDataLength);
                    clipResult = true;
                    break;
                }
                if (timeStamp > end) {
                    DebugUtil.i(TAG, "break,timestamp = " + timeStamp + " , end = " + end);
                    clipResult = true;
                    break;
                }

                outChannel.write(ByteBuffer.wrap(RecorderHeaderUtils.getADTSHeader(readSampleDataLength
                        + RecorderHeaderUtils.AAC_HEAD_SIZE, profile, freqIdx, channelCount)));
                mPosition += readSampleDataLength + RecorderHeaderUtils.AAC_HEAD_SIZE;
                buffer.rewind();
                buffer.limit(readSampleDataLength);
                try {
                    outChannel.write(buffer);
                    outChannel.position(mPosition);
                } catch (IOException e) {
                    DebugUtil.e(TAG, "clipAAc write error:", e);
                    mClipCode = mIsCanceled ? ClipResultCode.CLIP_IS_CANCELED : ClipResultCode.CLIP_WRITE_FAIL;
                    break;
                }
                extractor.advance();
                updateProgress();
            } while (readSampleDataLength > 0 && !mIsCanceled);
            return clipResult;
        } catch (Throwable ignored) {
            DebugUtil.e(TAG, "clip error:", ignored);
            mClipCode = ClipResultCode.CLIP_ERROR;
            return false;
        }
    }

    private void updateProgress() {
        if (mOnProgressChangeListener != null) {
            if (getCurrentProgress() > mProgress) {
                mProgress = getCurrentProgress();
                mOnProgressChangeListener.onProgressChanged(mProgress);
            }
        }
    }

    private boolean clipRevert(MediaExtractor extractor, FileChannel outChannel, long start, long end, long duration) {
        try {
            DebugUtil.i(TAG, "start = " + start + ", end = " + end + ", duration = " + duration);
            deleteAmplitudes(start, end, duration);
            deleteMarkList(start, end, duration);
            boolean clipResult = false;
            long sEnd = end;
            if (sEnd > duration) {
                sEnd = duration;
            }
            mClipTotalTime = duration - (sEnd - start);
            extractor.seekTo(0, MediaExtractor.SEEK_TO_PREVIOUS_SYNC);
            int iSampleSize = (int) extractor.getSampleSize();
            if (iSampleSize <= 0) {
                DebugUtil.e(TAG, "iSampleSize is error,iSampleSize = " + iSampleSize);
                mClipCode = ClipResultCode.CLIP_SAMPLE_SIZE_ERROR;
                return clipResult;
            }
            ByteBuffer buffer = ByteBuffer.allocate(iSampleSize * 2);
            int readSampleDataLength = 0;
            do {
                buffer.clear();
                readSampleDataLength = extractor.readSampleData(buffer, 0);
                long timeStamp = extractor.getSampleTime();
                mClipCurrentTime = timeStamp;
                if (timeStamp > start) {
                    DebugUtil.i(TAG, "break,timestamp = " + timeStamp + " , start = " + start);
                    clipResult = true;
                    break;
                }
                if (readSampleDataLength < 0) {
                    DebugUtil.i(TAG, "break,readSampleDataLength = " + readSampleDataLength);
                    clipResult = true;
                    break;
                }
                mPosition += readSampleDataLength;
                buffer.rewind();
                buffer.limit(readSampleDataLength);
                try {
                    outChannel.write(buffer);
                    outChannel.position(mPosition);
                } catch (IOException e) {
                    DebugUtil.e(TAG, "cut start file error", e);
                    mClipCode = mIsCanceled ? ClipResultCode.CLIP_IS_CANCELED : ClipResultCode.CLIP_WRITE_FAIL;
                    break;
                }
                extractor.advance();
                updateProgress();
            } while (readSampleDataLength > 0 && !mIsCanceled);
            extractor.seekTo(sEnd, MediaExtractor.SEEK_TO_PREVIOUS_SYNC);
            do {
                buffer.clear();
                readSampleDataLength = extractor.readSampleData(buffer, 0);
                long timeStamp = extractor.getSampleTime();
                mClipCurrentTime = timeStamp - (sEnd - start);
                if (timeStamp > duration) {
                    DebugUtil.i(TAG, "break,timestamp = " + timeStamp + ", end = " + sEnd);
                    clipResult = true;
                    break;
                }
                if (readSampleDataLength < 0) {
                    DebugUtil.i(TAG, "break,readSampleDataLength = " + readSampleDataLength);
                    clipResult = true;
                    break;
                }
                mPosition += readSampleDataLength;
                buffer.rewind();
                buffer.limit(readSampleDataLength);
                try {
                    outChannel.write(buffer);
                    outChannel.position(mPosition);
                } catch (IOException e) {
                    DebugUtil.e(TAG, "cut end file error", e);
                    mClipCode = mIsCanceled ? ClipResultCode.CLIP_IS_CANCELED : ClipResultCode.CLIP_WRITE_FAIL;
                    break;
                }
                extractor.advance();
                updateProgress();
            } while (readSampleDataLength > 0 && !mIsCanceled);
            return clipResult;
        } catch (Throwable ignored) {
            DebugUtil.e(TAG, "clipRevert error", ignored);
            mClipCode = ClipResultCode.CLIP_ERROR;
            return false;
        }
    }

    @SuppressLint("NewApi")
    private boolean clipRevertAAC(MediaExtractor extractor, FileChannel outChannel, long start, long end, long duration) {
        try {
            int trackIndex = extractor.getSampleTrackIndex();
            if (trackIndex == -1) {
                DebugUtil.e(TAG, "getSampleTrackIndex is -1 ");
                mClipCode = ClipResultCode.CLIP_SAMPLE_TRACK_INDEX_ERROR;
                return false;
            }
            MediaFormat mediaFormat = extractor.getTrackFormat(trackIndex);
            int channelCount = 0;
            int sampleRate = 0;
            int profile = 0;
            if (BaseUtil.isAndroidQOrLater()) {
                channelCount = mediaFormat.getInteger(MediaFormat.KEY_CHANNEL_COUNT, 1);
                sampleRate = mediaFormat.getInteger(MediaFormat.KEY_SAMPLE_RATE, 1);
                profile = mediaFormat.getInteger(MediaFormat.KEY_AAC_PROFILE, 2);
            } else {
                channelCount = mediaFormat.getInteger(MediaFormat.KEY_CHANNEL_COUNT);
                sampleRate = mediaFormat.getInteger(MediaFormat.KEY_SAMPLE_RATE);
                profile = mediaFormat.getInteger(MediaFormat.KEY_AAC_PROFILE);
            }
            int freqIdx = AVPRIV_AUDIO_SAMPLE_RATES.contains(sampleRate) ? AVPRIV_AUDIO_SAMPLE_RATES.indexOf(sampleRate) : 4;

            DebugUtil.i(TAG, "start = " + start + ", end = " + end + ", duration = " + duration);
            deleteAmplitudes(start, end, duration);
            deleteMarkList(start, end, duration);
            boolean clipResult = false;
            long sEnd = end;
            if (sEnd > duration) {
                sEnd = duration;
            }
            mClipTotalTime = duration - (sEnd - start);
            extractor.seekTo(0, MediaExtractor.SEEK_TO_PREVIOUS_SYNC);
            int iSampleSize = (int) extractor.getSampleSize();
            if (iSampleSize <= 0) {
                DebugUtil.e(TAG, "iSampleSize is error,iSampleSize = " + iSampleSize);
                mClipCode = ClipResultCode.CLIP_SAMPLE_SIZE_ERROR;
                return clipResult;
            }
            ByteBuffer buffer = ByteBuffer.allocate(iSampleSize * 2);
            int readSampleDataLength = 0;
            do {
                buffer.clear();
                readSampleDataLength = extractor.readSampleData(buffer, 0);
                long timeStamp = extractor.getSampleTime();
                mClipCurrentTime = timeStamp;
                if (timeStamp > start) {
                    DebugUtil.i(TAG, "break,timestamp = " + timeStamp + " , start = " + start);
                    clipResult = true;
                    break;
                }
                if (readSampleDataLength < 0) {
                    DebugUtil.i(TAG, "break,readSampleDataLength = " + readSampleDataLength);
                    clipResult = true;
                    break;
                }
                outChannel.write(ByteBuffer.wrap(RecorderHeaderUtils.getADTSHeader(readSampleDataLength + RecorderHeaderUtils.AAC_HEAD_SIZE, profile, freqIdx, channelCount)));
                mPosition += readSampleDataLength + RecorderHeaderUtils.AAC_HEAD_SIZE;

                buffer.rewind();
                buffer.limit(readSampleDataLength);
                try {
                    outChannel.write(buffer);
                    outChannel.position(mPosition);
                } catch (IOException e) {
                    DebugUtil.e(TAG, "cut start file error", e);
                    mClipCode = mIsCanceled ? ClipResultCode.CLIP_IS_CANCELED : ClipResultCode.CLIP_WRITE_FAIL;
                    break;
                }
                extractor.advance();
                updateProgress();
            } while (readSampleDataLength > 0 && !mIsCanceled);
            extractor.seekTo(sEnd, MediaExtractor.SEEK_TO_PREVIOUS_SYNC);
            do {
                buffer.clear();
                readSampleDataLength = extractor.readSampleData(buffer, 0);
                long timeStamp = extractor.getSampleTime();
                mClipCurrentTime = timeStamp - (sEnd - start);
                if (timeStamp > duration) {
                    DebugUtil.i(TAG, "break,timestamp = " + timeStamp + ", end = " + sEnd);
                    clipResult = true;
                    break;
                }
                if (readSampleDataLength < 0) {
                    DebugUtil.i(TAG, "break,readSampleDataLength = " + readSampleDataLength);
                    clipResult = true;
                    break;
                }
                outChannel.write(ByteBuffer.wrap(RecorderHeaderUtils.getADTSHeader(readSampleDataLength + RecorderHeaderUtils.AAC_HEAD_SIZE, profile, freqIdx, channelCount)));
                // +7,7是header的长度
                mPosition += readSampleDataLength + RecorderHeaderUtils.AAC_HEAD_SIZE;
                buffer.rewind();
                buffer.limit(readSampleDataLength);
                try {
                    outChannel.write(buffer);
                    outChannel.position(mPosition);
                } catch (IOException e) {
                    DebugUtil.e(TAG, "cut end file error", e);
                    mClipCode = mIsCanceled ? ClipResultCode.CLIP_IS_CANCELED : ClipResultCode.CLIP_WRITE_FAIL;
                    break;
                }
                extractor.advance();
                updateProgress();
            } while (readSampleDataLength > 0 && !mIsCanceled);
            return clipResult;
        } catch (Throwable ignored) {
            DebugUtil.e(TAG, "clipRevert error", ignored);
            mClipCode = ClipResultCode.CLIP_ERROR;
            return false;
        }
    }

    public void addHeader(FileOutputStream outputStream, byte[] header) throws IOException {
        outputStream.write(header);
    }

    private int getAudioTrack(MediaExtractor extractor) {
        for (int i = 0; i < extractor.getTrackCount(); i++) {
            MediaFormat format = extractor.getTrackFormat(i);
            String mime = format.getString(MediaFormat.KEY_MIME);
            if (mime.startsWith(AUDIO)) {
                /* 有的 aac-adts 读取 mimeType为 audio/mp4a-latm，所以这个地方不需要重新赋值
                 * mMimeType = mime; */
                DebugUtil.i(TAG, "getAudioTrack mime : " + mime + " , track : " + i);
                return i;
            }
        }
        return -1;
    }

    public long getDurationByFD(FileDescriptor fd) {
        MediaMetadataRetriever retriever = new MediaMetadataRetriever();
        long duration = 0;
        try {
            retriever.setDataSource(fd);
            duration = Long.parseLong(retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_DURATION));
        } catch (Exception e) {
            DebugUtil.e(TAG, "getDurationByFD error:", e);
        } finally {
            try {
                retriever.release();
            } catch (Exception e) {
                DebugUtil.e(TAG, " retriever.release() error:", e);
            }
        }
        return duration;
    }

    public Uri getClipUri() {
        return mClipUri;
    }

    public String getClipPath() {
        return mClipPath;
    }

    public long getClipStartDuration() {
        return mClipStartDuration;
    }

    public int getCurrentProgress() {
        if ((mClipTotalTime <= 0) || (mClipCurrentTime < 0)) {
            DebugUtil.e(TAG, "mClipTotalTime must larger zero");
            return PROGRESS_100;
        }
        int progress = (int) (mClipCurrentTime * 100L / mClipTotalTime);
        return progress;
    }

    public void setObjAmplitudes(List<Integer> objAmplitudes) {
        mObjAmplitudes = objAmplitudes;
    }

    public void setMarkList(List<MarkDataBean> mMarkList) {
        this.mMarkList = mMarkList;
    }

    public ArrayList<MarkDataBean> getClipMarkList() {
        return mClipMarkList;
    }

    public List<Integer> getClipAmplitudes() {
        return mClipAmplitudes;
    }

    private void clipAmplitudes(long start, long end, long duration) {
        if (mObjAmplitudes == null) {
            DebugUtil.e(TAG, "mObjAmplitudes is null.");
            return;
        }
        if (duration == 0) {
            DebugUtil.e(TAG, "the duration can't is zero.");
            return;
        }
        int objSize = mObjAmplitudes.size();
        int startIndex = (int) (start * objSize / duration);
        int endIndex = (int) (end * objSize / duration);
        if (startIndex >= endIndex) {
            DebugUtil.e(TAG, "startIndex >= endIndex");
            return;
        }
        DebugUtil.i(TAG, "clipAmplitudes startIndex = " + startIndex
                + " , endIndex = " + endIndex + " mObjAmplitudes.size = " + mObjAmplitudes.size());
        if (endIndex <= objSize) {
            mClipAmplitudes = new ArrayList<>(mObjAmplitudes.subList(startIndex, endIndex));
        }
    }

    private void deleteAmplitudes(long start, long end, long duration) {
        if (mObjAmplitudes == null) {
            DebugUtil.e(TAG, "mObjAmplitudes is null.");
            return;
        }
        if (duration == 0) {
            DebugUtil.e(TAG, "the duration can't is zero.");
            return;
        }
        int objSize = mObjAmplitudes.size();
        int startIndex = (int) (start * objSize / duration);
        int endIndex = (int) (end * objSize / duration);
        DebugUtil.i(TAG, "deleteAmplitudes startIndex = " + startIndex + " , endIndex = "
                + endIndex + " mObjAmplitudes.size = " + mObjAmplitudes.size());
        if (startIndex >= endIndex) {
            DebugUtil.e(TAG, "startIndex >= endIndex");
            return;
        }
        mClipAmplitudes = new ArrayList<>(mObjAmplitudes.subList(0, startIndex));
        if (endIndex < objSize) {
            mClipAmplitudes.addAll(mObjAmplitudes.subList(endIndex, objSize));
        }
    }

    public interface OnProgressChangeListener {
        void onProgressChanged(int progress);
    }

    public void setOnProgressChanged(OnProgressChangeListener listener) {
        mOnProgressChangeListener = listener;
    }

    private void clipMarkList(long start, long end, long duration) {
        if (mClipMarkList == null) {
            mClipMarkList = new ArrayList<>();
        }
        long lStart = start / US_TIMES;
        long lEnd = end / US_TIMES;
        long lDuration = duration / US_TIMES;
        if (mMarkList == null) {
            DebugUtil.w(TAG, "mMarkList is null.");
        } else {
            for (MarkDataBean mark : mMarkList) {
                DebugUtil.i(TAG, "clipMarkList mark = " + mark);
                mark.release(false);
                long lTime = mark.getTimeInMills();
                if (lTime > lDuration) {
                    DebugUtil.e(TAG, "clipMarkList markTime > duration, adjust the markTime = duration !!!!");
                    lTime = lDuration;
                    mark.setTimeInMills(lDuration);
                    mark.correctTime(lDuration);
                }
                if ((lTime >= lStart) && (lTime <= lEnd)) {
                    long nTime = lTime - lStart;
                    MarkDataBean markDataBean = new MarkDataBean(nTime, mark.getVersion());
                    markDataBean.setDefault(mark.isDefault());
                    markDataBean.setMarkText(mark.getMarkText());
                    markDataBean.setDefaultNo(mark.getDefaultNo());
                    markDataBean.setPictureFilePath(mark.getPictureFilePath());
                    mClipMarkList.add(markDataBean);
                    DebugUtil.i(TAG, "nMark = " + markDataBean);
                }
            }
        }
    }

    private void deleteMarkList(long start, long end, long duration) {
        if (mClipMarkList == null) {
            mClipMarkList = new ArrayList<>();
        }
        long lStart = start / US_TIMES;
        long lEnd = end / US_TIMES;
        long lDuration = duration / US_TIMES;
        if (mMarkList == null) {
            DebugUtil.w(TAG, "mMarkList is null.");
        } else {
            for (MarkDataBean mark : mMarkList) {
                DebugUtil.i(TAG, "deleteMarkList mark = " + mark);
                mark.release(false);
                long lTime = mark.getTimeInMills();
                if (lTime > lDuration) {
                    DebugUtil.e(TAG, "deleteMarkList markTime > duration, adjust the markTime = duration !!!!");
                    lTime = lDuration;
                    mark.setTimeInMills(lDuration);
                    mark.correctTime(lDuration);
                }
                if (lTime <= lStart) {
                    MarkDataBean markDataBean = new MarkDataBean(mark.getTimeInMills(), mark.getVersion());
                    markDataBean.setDefault(mark.isDefault());
                    markDataBean.setMarkText(mark.getMarkText());
                    markDataBean.setDefaultNo(mark.getDefaultNo());
                    markDataBean.setPictureFilePath(mark.getPictureFilePath());
                    mClipMarkList.add(markDataBean);
                    DebugUtil.i(TAG, "nMark = " + mark);
                }
                if (lTime >= lEnd) {
                    long nTime = lTime - (lEnd - lStart);
                    MarkDataBean markDataBean = new MarkDataBean(nTime, mark.getVersion());
                    markDataBean.setDefault(mark.isDefault());
                    markDataBean.setMarkText(mark.getMarkText());
                    markDataBean.setDefaultNo(mark.getDefaultNo());
                    markDataBean.setPictureFilePath(mark.getPictureFilePath());
                    mClipMarkList.add(markDataBean);
                    DebugUtil.i(TAG, "nMark = " + markDataBean);
                }
            }
        }
    }

    @SuppressLint("NewApi")
    private byte[] getWaveHeaderForExtract(MediaExtractor extractor, long clipStart, long clipEnd) throws IOException {
        int trackIndex = extractor.getSampleTrackIndex();
        if (trackIndex == -1) {
            mClipCode = ClipResultCode.CLIP_SAMPLE_TRACK_INDEX_ERROR;
            DebugUtil.e(TAG, "getWaveHeader  getSampleTrackIndex is -1");
            return null;
        }
        MediaFormat mediaFormat = extractor.getTrackFormat(trackIndex);
        //根据pcmEncoding编码格式，得到采样精度，MediaFormat.KEY_PCM_ENCODING这个值不一定有
        int pcmEncoding = mediaFormat.containsKey(MediaFormat.KEY_PCM_ENCODING)
                ? mediaFormat.getInteger(MediaFormat.KEY_PCM_ENCODING) : AudioFormat.ENCODING_PCM_16BIT;
        int bitNum = 0;
        switch (pcmEncoding) {
            case AudioFormat.ENCODING_PCM_FLOAT:
                bitNum = BIT_NUMBER_32;
                break;
            case AudioFormat.ENCODING_PCM_8BIT:
                bitNum = BIT_NUMBER_8;
                break;
            case AudioFormat.ENCODING_PCM_16BIT:
            default:
                bitNum = BIT_NUMBER_16;
                break;
        }
        int sampleRate = 0;
        int channel = 0;
        if (BaseUtil.isAndroidQOrLater()) {
            sampleRate = mediaFormat.getInteger(MediaFormat.KEY_SAMPLE_RATE, DEFAULT_NUMBER_44100);
            channel = mediaFormat.getInteger(MediaFormat.KEY_CHANNEL_COUNT, DEFAULT_NUMBER_1);
        } else {
            sampleRate = mediaFormat.getInteger(MediaFormat.KEY_SAMPLE_RATE);
            channel = mediaFormat.getInteger(MediaFormat.KEY_CHANNEL_COUNT);
        }
        long contentSize = RecorderHeaderUtils.getPositionFromWave((clipEnd - clipStart)
                / SECOND_PER_MILLISION, sampleRate, channel, bitNum);
        DebugUtil.i(TAG, " getWaveHeaderForExtract contentSize: " + contentSize);
        return RecorderHeaderUtils.calWaveHeader(contentSize, sampleRate, channel, bitNum);
    }

    @SuppressLint("NewApi")
    private byte[] getWaveHeaderForDelete(MediaExtractor extractor, long clipStart, long clipEnd, long duration) throws IOException {
        int trackIndex = extractor.getSampleTrackIndex();
        if (trackIndex == -1) {
            mClipCode = ClipResultCode.CLIP_SAMPLE_TRACK_INDEX_ERROR;
            DebugUtil.e(TAG, "getWaveHeader  getSampleTrackIndex is -1");
            return null;
        }
        MediaFormat mediaFormat = extractor.getTrackFormat(trackIndex);
        //根据pcmEncoding编码格式，得到采样精度，MediaFormat.KEY_PCM_ENCODING这个值不一定有
        int pcmEncoding = mediaFormat.containsKey(MediaFormat.KEY_PCM_ENCODING) ? mediaFormat.getInteger(MediaFormat.KEY_PCM_ENCODING) : AudioFormat.ENCODING_PCM_16BIT;
        int bitNum = 0;
        switch (pcmEncoding) {
            case AudioFormat.ENCODING_PCM_FLOAT:
                bitNum = BIT_NUMBER_32;
                break;
            case AudioFormat.ENCODING_PCM_8BIT:
                bitNum = BIT_NUMBER_8;
                break;
            case AudioFormat.ENCODING_PCM_16BIT:
            default:
                bitNum = BIT_NUMBER_16;
                break;
        }
        int sampleRate = 0;
        int channel = 0;
        if (BaseUtil.isAndroidQOrLater()) {
            sampleRate = mediaFormat.getInteger(MediaFormat.KEY_SAMPLE_RATE, DEFAULT_NUMBER_44100);
            channel = mediaFormat.getInteger(MediaFormat.KEY_CHANNEL_COUNT, DEFAULT_NUMBER_1);
        } else {
            sampleRate = mediaFormat.getInteger(MediaFormat.KEY_SAMPLE_RATE);
            channel = mediaFormat.getInteger(MediaFormat.KEY_CHANNEL_COUNT);
        }

        // 总时长-（移除结束时间-移除开始时间）
        long contentTimeLen = duration - (clipEnd - clipStart);
        long clipContentLen = RecorderHeaderUtils.getPositionFromWave(contentTimeLen / SECOND_PER_MILLISION, sampleRate, channel, bitNum);
        DebugUtil.i(TAG, "getWaveHeaderForDelete contentTimeLen: " + contentTimeLen + "  clipContentLen - " + clipContentLen);
        return RecorderHeaderUtils.calWaveHeader(clipContentLen, sampleRate,
                channel, bitNum);
    }

    /**
     * 获取裁切结果
     * @return
     */
    public int getClipCode() {
        return mClipCode;
    }

}
