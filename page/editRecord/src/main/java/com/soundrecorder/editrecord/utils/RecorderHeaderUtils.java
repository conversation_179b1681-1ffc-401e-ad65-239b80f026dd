/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: RecorderHeaderUtils
 Description:
 Version: 1.0
 Date: 2022/12/2
 Author: ********(<EMAIL>)
 -----------Revision History-----------
 <author> <date> <version> <desc>
 ******** 2022/12/2 1.0 create
 */

// OPLUS Java File Skip Rule:NumberIntUse,MethodLength
package com.soundrecorder.editrecord.utils;

import java.io.IOException;

public class RecorderHeaderUtils {
    public static final int AAC_HEAD_SIZE = 7;


    /**
     * get ADTS header at the beginning of each and every AAC packet.
     * This is needed as MediaCodec encoder generates a packet of raw
     * AAC data.
     * <p>
     * Note the packetLen must count in the ADTS header itself.
     *
     * @param packetLen
     * @param profile   表示使用哪个级别的AAC，在MPEG-2 AAC中定义了3种：
     * @param freqIdx   采样率下标
     * @param chanCfg   声道数
     **/
    public static byte[] getADTSHeader(int packetLen, int profile, int freqIdx, int chanCfg) {
        byte[] packet = new byte[AAC_HEAD_SIZE];
        // fill in ADTS data
        packet[0] = (byte) 0xFF;
        packet[1] = (byte) 0xF9;
        packet[2] = (byte) (((profile - 1) << 6) + (freqIdx << 2) + (chanCfg >> 2));
        packet[3] = (byte) (((chanCfg & 3) << 6) + (packetLen >> 11));
        packet[4] = (byte) ((packetLen & 0x7FF) >> 3);
        packet[5] = (byte) (((packetLen & 7) << 5) + 0x1F);
        packet[6] = (byte) 0xFC;
        return packet;
    }

    /**
     * 获取Wav header 字节数据
     *
     * @param totalAudioLen 整个音频PCM数据大小
     * @param sampleRate    采样率
     * @param channels      声道数
     * @param bitNum        采样位数
     * @throws IOException
     */
    public static byte[] calWaveHeader(long totalAudioLen, int sampleRate, int channels, int bitNum) throws IOException {
        //总大小，由于不包括RIFF和WAV，所以是44 - 8 = 36，在加上PCM文件大小
        long totalDataLen = totalAudioLen + 36;
        //采样字节byte率
        long byteRate = (long) sampleRate * channels * bitNum / 8;

        byte[] header = new byte[44];
        header[0] = 'R'; // RIFF
        header[1] = 'I';
        header[2] = 'F';
        header[3] = 'F';
        header[4] = (byte) (totalDataLen & 0xff);//数据大小
        header[5] = (byte) ((totalDataLen >> 8) & 0xff);
        header[6] = (byte) ((totalDataLen >> 16) & 0xff);
        header[7] = (byte) ((totalDataLen >> 24) & 0xff);
        header[8] = 'W';//WAVE
        header[9] = 'A';
        header[10] = 'V';
        header[11] = 'E';
        //FMT Chunk
        header[12] = 'f'; // 'fmt '
        header[13] = 'm';
        header[14] = 't';
        header[15] = ' ';//过渡字节
        //数据大小
        header[16] = 16; // 4 bytes: size of 'fmt ' chunk
        header[17] = 0;
        header[18] = 0;
        header[19] = 0;
        //编码方式 10H为PCM编码格式
        header[20] = 1; // format = 1
        header[21] = 0;
        //通道数
        header[22] = (byte) channels;
        header[23] = 0;
        //采样率，每个通道的播放速度
        header[24] = (byte) (sampleRate & 0xff);
        header[25] = (byte) ((sampleRate >> 8) & 0xff);
        header[26] = (byte) ((sampleRate >> 16) & 0xff);
        header[27] = (byte) ((sampleRate >> 24) & 0xff);
        //音频数据传送速率,采样率*通道数*采样深度/8
        header[28] = (byte) (byteRate & 0xff);
        header[29] = (byte) ((byteRate >> 8) & 0xff);
        header[30] = (byte) ((byteRate >> 16) & 0xff);
        header[31] = (byte) ((byteRate >> 24) & 0xff);
        // 确定系统一次要处理多少个这样字节的数据，确定缓冲区，通道数*采样位数
        header[32] = (byte) (channels * 16 / 8);
        header[33] = 0;
        //每个样本的数据位数
        header[34] = 16;
        header[35] = 0;
        //Data chunk
        header[36] = 'd';//data
        header[37] = 'a';
        header[38] = 't';
        header[39] = 'a';
        header[40] = (byte) (totalAudioLen & 0xff);
        header[41] = (byte) ((totalAudioLen >> 8) & 0xff);
        header[42] = (byte) ((totalAudioLen >> 16) & 0xff);
        header[43] = (byte) ((totalAudioLen >> 24) & 0xff);

        return header;
    }

    /**
     * 获取指定时间节点PCM数据大小
     *
     * @param time 时间为秒
     * @param sampleRate
     * @param channels
     * @param bitNum
     * @return
     */
    public static long getPositionFromWave(float time, int sampleRate, int channels, int bitNum) {
        int byteNum = bitNum / 8;
        long position = (long) (time * sampleRate * channels * byteNum);

        //这里要特别注意，要取整（byteNum * channels）的倍数
        position = position / (byteNum * channels) * (byteNum * channels);

        return position;
    }
}
