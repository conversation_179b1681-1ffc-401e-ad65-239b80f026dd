/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: ClipTask
 Description:
 Version: 1.0
 Date: 2022/12/2
 Author: ********(<EMAIL>)
 -----------Revision History-----------
 <author> <date> <version> <desc>
 ******** 2022/12/2 1.0 create
 */

// OPLUS Java File Skip Rule:MethodLength,MethodComplexity,FileLength,LineLength
package com.soundrecorder.editrecord;

import android.content.Context;
import android.os.AsyncTask;

import com.soundrecorder.base.BaseApplication;
import com.soundrecorder.base.utils.BaseUtil;
import com.soundrecorder.base.utils.DebugUtil;
import com.soundrecorder.base.utils.StorageUtil;
import com.soundrecorder.common.constant.Constants;
import com.soundrecorder.common.databean.Record;
import com.soundrecorder.common.databean.markdata.MarkDataBean;
import com.soundrecorder.common.db.MediaDBUtils;
import com.soundrecorder.common.utils.RecordModeUtil;
import com.soundrecorder.editrecord.ui.ClipTaskViewModel;

import java.util.ArrayList;
import java.util.List;

public class ClipTask extends AsyncTask<Long, Integer, ClipRecord> {
    public static final int MAX_PROGRESS = 100;
    private static final int PARAMS_LENGTH = 4;
    private static String TAG = "ClipTask";
    private ClipRecord mClipRecord;
    private List<Integer> mObjAmplitudes;
    private ArrayList<MarkDataBean> mMarkList = null;
    private long mOpType = ClipRecord.OP_EXTRACT;
    private int mRecordType = -1;
    private ClipTaskViewModel mTaskViewModel;


    public ClipTask(ClipTaskViewModel taskViewModel, int recordType) {
        this.mTaskViewModel = taskViewModel;
        mRecordType = recordType;
    }

    public void setObjAmplitudes(List<Integer> objAmplitudes) {
        mObjAmplitudes = objAmplitudes;
    }

    public void setMarkList(ArrayList<MarkDataBean> mMarkList) {
        this.mMarkList = mMarkList;
    }

    public void cancelAndDelete(boolean needDelete) {
        this.cancel(true);
        if (mClipRecord != null) {
            mClipRecord.setCanceled(true, needDelete);
        }
    }

    @Override
    protected ClipRecord doInBackground(Long... params) {
        mClipRecord = null;
        if (params.length > PARAMS_LENGTH) {
            long rowId = params[0];
            long clipStartDuration = params[1];
            long clipEndDuration = params[2];
            long totalDuration = params[3];
            long opType = params[4];
            mOpType = opType;
            Record objRecord = MediaDBUtils.queryRecordById(rowId);
            if (objRecord == null) {
                DebugUtil.e(TAG, "queryRecordById objRecord is null.");
                return mClipRecord;
            }
            boolean isEnoughSpace = checkDiskWhenTrim(BaseApplication.getAppContext(), objRecord, clipStartDuration, clipEndDuration, totalDuration, opType);
            if (mTaskViewModel != null) {
                if (isEnoughSpace) {
                    mTaskViewModel.getDialogStatus().postValue(ClipTaskViewModel.CLIP_SHOW_DIALOG);
                } else {
                    mTaskViewModel.getDialogStatus().postValue(ClipTaskViewModel.CLIP_ERROR_DIALOG);
                    return mClipRecord;
                }
            }
            if (!BaseUtil.isAndroidQOrLater()) {
                String relativePath = RecordModeUtil.getRelativePathByRecordType(mRecordType, true);
                objRecord.setRelativePath(relativePath);
            }
            mClipRecord = new ClipRecord(BaseApplication.getAppContext(), objRecord);
            mClipRecord.setObjAmplitudes(mObjAmplitudes);
            mClipRecord.setMarkList(mMarkList);
            mClipRecord.setOnProgressChanged(new ClipRecord.OnProgressChangeListener() {
                @Override
                public void onProgressChanged(int progress) {
                    mUpdateProgressRunnable();
                }
            });
            if (opType == ClipRecord.OP_EXTRACT) {
                mClipRecord.extract(clipStartDuration, clipEndDuration, totalDuration);
            } else if (opType == ClipRecord.OP_DELETE) {
                mClipRecord.delete(clipStartDuration, clipEndDuration, totalDuration);
            } else {
                DebugUtil.e(TAG, "Unsupported operation types:" + opType);
            }
        } else {
            DebugUtil.e(TAG, "illegal arguments ");
        }
        return mClipRecord;
    }

    private void mUpdateProgressRunnable() {
        int progress = 0;
        if (getClipRecord() != null) {
            progress = getClipRecord().getCurrentProgress();
        }
        if (mTaskViewModel != null) {
            mTaskViewModel.getDialogProgress().postValue(progress);
        }
    }

    @Override
    protected void onPostExecute(ClipRecord aClipRecord) {
        super.onPostExecute(aClipRecord);
        mClipRecord = aClipRecord;
        dismissProgressDialog();
        release();
    }

    @Override
    protected void onCancelled() {
        super.onCancelled();
        dismissProgressDialog();
        release();
    }

    private void dismissProgressDialog() {
        if (mTaskViewModel != null) {
            mTaskViewModel.getDialogStatus().postValue(ClipTaskViewModel.CLIP_DISMISS_DIALOG);
        }
    }


    private String getTitle(int progress) {
        String title = BaseApplication.getAppContext().getResources().getString(com.soundrecorder.common.R.string.record_clipping);
        title += progress + "%";
        return title;
    }

    public ClipRecord getClipRecord() {
        return mClipRecord;
    }

    public long getOpType() {
        return mOpType;
    }

    public void release() {
        mTaskViewModel = null;
    }

    private boolean checkDiskWhenTrim(Context context, Record objRecord, long clipStart, long clipEnd, long totalDuration, long opType) {
        if (context == null) {
            return false;
        }
        long clipPredictSize = (long) (objRecord.getFileSize() * getTrimPercent(clipStart, clipEnd, totalDuration, opType));
        long reservedSpaceSize = clipPredictSize + Constants.RESERVED_SPACE;
        boolean isEnoughSpace = StorageUtil.INSTANCE.getAvailableSpace(context) > reservedSpaceSize;
        DebugUtil.i(TAG, "checkDiskWhenTrim isEnoughSpace = " + isEnoughSpace + " , reservedSpaceSize = " + reservedSpaceSize);
        return isEnoughSpace;
    }


    private float getTrimPercent(long clipStart, long clipEnd, long totalDuration, long opType) {
        float percent = 0F;
        if (totalDuration == 0) {
            return percent;
        }
        if (opType == ClipRecord.OP_EXTRACT) {
            percent = (float) (clipEnd - clipStart) / (float) totalDuration;
        } else if (opType == ClipRecord.OP_DELETE) {
            percent = (float) (totalDuration - (clipEnd - clipStart)) / (float) totalDuration;
        }
        DebugUtil.i(TAG, "getTrimPercent : percent = " + percent);
        return percent;
    }

}
