/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: RegionSelectListener
 Description:
 Version: 1.0
 Date: 2022/12/2
 Author: pingyong<PERSON>@oppo.com
 -----------Revision History-----------
 <author> <date> <version> <desc>
 ******** 2022/12/2 1.0 create
 */

package com.soundrecorder.editrecord.views.wave

import android.content.Context
import android.os.Handler
import android.os.Looper
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.ViewGroup
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.NumberConstant
import com.soundrecorder.editrecord.R
import com.soundrecorder.editrecord.views.wave.EditWaveItemView.Companion.LEFT
import com.soundrecorder.editrecord.views.wave.EditWaveItemView.Companion.NONE
import com.soundrecorder.editrecord.views.wave.EditWaveItemView.Companion.RIGHT
import com.soundrecorder.editrecord.views.wave.interfaze.IPublisher
import com.soundrecorder.editrecord.views.wave.interfaze.ISubscriber
import com.soundrecorder.wavemark.wave.view.WaveItemView.TIME_GAP
import com.soundrecorder.wavemark.wave.view.WaveRecyclerView
import kotlin.math.abs
import kotlin.math.ceil

class EditWaveRecyclerView(context: Context?, attrs: AttributeSet?) :
    WaveRecyclerView<EditWaveItemView>(context, attrs), IPublisher {

    var onTouchDownOnWaveBar: (() -> Unit)? = null
    var onTouchUpOnWaveBar: (() -> Unit)? = null
    var handlerMoveListener: HandlerMoveListener? = null
    var startRecord: Long? = null
    var endRecord: Long? = null
    var hitStatus = NONE
    private var mLastTime: Long? = null
    private var mSubscribers: MutableList<ISubscriber>? = null
    private val mainHandler = Handler(Looper.getMainLooper())

    @Volatile
    private var isRunningOnTouchLeft = false

    @Volatile
    private var isRunningOnTouchRight = false
    private var moveEventX = -1f
    private val dp16 = resources.getDimension(com.soundrecorder.common.R.dimen.dp16)
    private val dp24 = resources.getDimension(com.soundrecorder.common.R.dimen.dp24)
    private val onTouchLeftRunnable = object : Runnable {
        override fun run() {
            if (hitStatus == NONE) {
                return
            }
            val moveX = moveEventX
            if (moveX == -1f) return
            val subscribers = mSubscribers ?: return
            val startTime = startRecord ?: return
            val endTime = endRecord ?: return
            DebugUtil.i("EditWaveRecyclerView", "onTouchLeftRunnable, hitStatus=$hitStatus")

            val oldCurTime = getSlideTime("onTouchLeftRunnable")
            if (hitStatus == LEFT) {
                // 左侧线条往左侧拉
                val moveTime = getMoveTime(moveX)
                var time = startTime - moveTime
                if (time <= 0) {
                    time = 0
                }
                startRecord = time
                var curTime = oldCurTime - moveTime
                if (curTime < 0) {
                    curTime = 0
                }
                val dTime = curTime - oldCurTime
                scrollBy((dTime * mPxPerMs).toInt(), 0)
            } else if (hitStatus == RIGHT) {
                // 终点线已经到最左侧了，且中线已在蓝色区域内，不能更往左了
                if ((endTime - startTime) <= TIME_GAP && (oldCurTime in startTime..endTime)) {
                    DebugUtil.d("EditWaveRecyclerView", "no need to scroll")
                    return
                }
                // 右侧线条往左侧拉
                var moveTime = getMoveTime(moveX)
                var time = endTime - moveTime
                if (time <= startTime + TIME_GAP) {
                    time = startTime + TIME_GAP
                }

                /**
                 * 终点线右往左拉动到距离起点线1s位置，波形是往右边滚动的；最多滚动到中线与终点线重叠的位置
                 * 若滚动moveTime后新的中线位置 小于 终点位置，则需要滚动到终点位置
                 * 波形从左往右滚动：moveTime=446, oldCurTime=1141,time=1000;correctTime=141
                 * 实际波形滚动 141就到终点位置了，若滚动446，中线跟终点线就有间隙了
                 */
                if ((endRecord == time) && (oldCurTime - moveTime) < time) {
                    DebugUtil.d("EditWaveRecyclerView", "correctMoveTime, moveTime=$moveTime, oldCurTime=$oldCurTime,time=$time")
                    // 终点线到底后，波形是还可以往左滚动的，中线位置在终点线左侧，不让他滚动了，避免中线滚动到起点位置
                    moveTime = oldCurTime - time
                }

                endRecord = time

                var curTime = oldCurTime - moveTime
                if (curTime < startTime) {
                    curTime = startTime
                }
                val dTime = curTime - oldCurTime
                scrollBy((dTime * mPxPerMs).toInt(), 0)
            }
            for (subscriber in subscribers) {
                subscriber.notifyTimeChangeByX(correctX(moveX))
            }
            mainHandler.postDelayed(this, DURATION.toLong())
        }
    }
    private val onTouchRightRunnable = object : Runnable {
        override fun run() {
            if (hitStatus == NONE) {
                return
            }
            val moveX = moveEventX
            if (moveX == -1f) return
            val subscribers = mSubscribers ?: return
            val startTime = startRecord ?: return
            val endTime = endRecord ?: return
            DebugUtil.i("EditWaveRecyclerView", "onTouchRightRunnable, hitStatus=$hitStatus")

            val oldCurTime = getSlideTime("onTouchRightRunnable")
            if (hitStatus == LEFT) {
                // 起点线已经在最右侧了，且中线也在蓝色区域内，不能再滚动了
                if ((endTime - startTime) <= TIME_GAP && (oldCurTime in startTime..endTime)) {
                    DebugUtil.d("EditWaveRecyclerView", "no need to scroll")
                    return
                }
                // 左侧线条往右侧拉
                var moveTime = getMoveTime(moveX)
                var time = startTime + moveTime
                if (time >= endTime - TIME_GAP) {
                    time = endTime - TIME_GAP
                }
                /**
                 * 起点线从左往右拉动到距离终点线1s位置，波形是往左边滚动的；最多滚动到中线与起点线重叠的位置
                 * 若滚动moveTime后新的中线位置 大于 起点位置，则需要滚动到起点位置
                 * 波形从右往左滚动：moveTime=391, oldCurTime=254397,time=254749, correctTime = 352
                 * 实际波形滚动 352就到中线跟起点位置重叠了，若滚动391，就滚多了，起点跟中线有距离了
                 */
                if ((startRecord == time) && (oldCurTime + moveTime) > time) {
                    DebugUtil.d("EditWaveRecyclerView", "correctMoveTime, moveTime=$moveTime, oldCurTime=$oldCurTime,time=$time")
                    moveTime = time - oldCurTime
                }
                startRecord = time

                var curTime = oldCurTime + moveTime
                if (curTime > endTime) {
                    curTime = endTime
                }
                val dTime = curTime - oldCurTime
                scrollBy((dTime * mPxPerMs).toInt(), 0)
            } else if (hitStatus == RIGHT) {
                // 右侧线条往右侧拉
                val moveTime = getMoveTime(moveX)
                var time = endTime + moveTime
                if (time > totalTime) {
                    time = totalTime
                }
                endRecord = time
                var curTime = oldCurTime + moveTime
                if (curTime > totalTime) {
                    curTime = totalTime
                }
                val dTime = curTime - oldCurTime
                scrollBy(ceil(dTime * mPxPerMs).toInt(), 0)
            }
            for (subscriber in subscribers) {
                subscriber.notifyTimeChangeByX(correctX(moveX))
            }
            mainHandler.postDelayed(this, DURATION.toLong())
        }
    }

    /**
     * 计算每次波形移动的距离，具体算法与当前的eventX有关系
     */
    private fun getMoveTime(x: Float): Long {
        val diffX = when {
            x < dp24 -> (dp24 - x)
            x > width - dp24 -> (x - (width - dp24))
            else -> x
        }
        val ratio = diffX * NumberConstant.NUM_F0_5
        return (ratio * DURATION).toLong()
    }

    override fun onInterceptTouchEvent(e: MotionEvent): Boolean {
        if (mCanScrollHorizontally) {
            if (e.action == MotionEvent.ACTION_DOWN) {
                mTouchDownX = e.x
                if (isTouchHandler(e.x)) {
                    mCanScrollHorizontally = false
                    return true
                }
            }
            return super.onInterceptTouchEvent(e)
        }
        return true
    }

    override fun onTouchEvent(e: MotionEvent): Boolean {
        checkMarkRegion(e)
        return if (mCanScrollHorizontally) {
            when (e.action) {
                MotionEvent.ACTION_DOWN -> mTouchDownX = e.x
                MotionEvent.ACTION_UP -> {
                    val touchDeltaX = e.x - mTouchDownX
                    if (abs(touchDeltaX) > mSlop) {
                        mDragListener?.onDragged()
                    }
                }
            }
            return super.onTouchEvent(e)
        } else {
            when (e.action) {
                MotionEvent.ACTION_DOWN -> {
                    onTouchDownOnWaveBar?.invoke()
                    mTouchDownX = e.x
                    return super.onTouchEvent(e)
                }
                MotionEvent.ACTION_MOVE -> notifySubscribers(e.x)
                MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                    mCanScrollHorizontally = true
                    notifySubscribersUp()
                    onTouchUpOnWaveBar?.invoke()
                }
            }
            true
        }
    }

    override fun register(subscriber: ISubscriber) {
        val subscribers = mSubscribers ?: mutableListOf<ISubscriber>().apply { mSubscribers = this }
        subscribers.add(subscriber)
    }

    private fun notifySubscribersUp() {
        DebugUtil.d("EditWaveRecyclerView", "notifySubscribersUp")
        moveEventX = -1f
        removeRunLeftOnClipBar()
        removeRunRightOnClipBar()
        val subscribers = mSubscribers ?: return
        for (subscriber in subscribers) {
            subscriber.notifyUp()
        }
        hitStatus = NONE
    }

    private fun isTouchHandler(x: Float): Boolean {
        val subscribers = mSubscribers ?: return false
        for (subscriber in subscribers) {
            if (subscriber.isTouchHandler(x)) {
                return true
            }
        }
        return false
    }

    private fun notifySubscribers(x: Float) {
        val subscribers = mSubscribers ?: return
        when {
            x <= dp24 -> {
                moveEventX = x.coerceAtLeast(0f)
                runLeftOnClipBar()
            }
            x >= width - dp24 -> {
                moveEventX = x.coerceAtMost(width.toFloat())
                runRightClipBar()
            }
            else -> {
                moveEventX = x
                removeRunLeftOnClipBar()
                removeRunRightOnClipBar()
            }
        }
        for (subscriber in subscribers) {
            subscriber.notifyTimeChangeByX(correctX(x))
        }
    }

    private fun correctX(x: Float) = when {
        x < dp16 -> dp16
        x > width - dp16 -> width - dp16
        else -> x
    }

    private fun runLeftOnClipBar() {
        if (!isRunningOnTouchLeft) {
            isRunningOnTouchLeft = true
            mainHandler.post(onTouchLeftRunnable)
        }
    }

    private fun runRightClipBar() {
        if (!isRunningOnTouchRight) {
            isRunningOnTouchRight = true
            mainHandler.post(onTouchRightRunnable)
        }
    }

    private fun removeRunLeftOnClipBar() {
        if (isRunningOnTouchLeft) {
            isRunningOnTouchLeft = false
            mainHandler.removeCallbacks(onTouchLeftRunnable)
        }
    }

    private fun removeRunRightOnClipBar() {
        if (isRunningOnTouchRight) {
            isRunningOnTouchRight = false
            mainHandler.removeCallbacks(onTouchRightRunnable)
        }
    }

    override fun createNewItemView(context: Context, parent: ViewGroup): EditWaveItemView {
        return LayoutInflater.from(context).inflate(R.layout.item_ruler_edit, parent, false) as EditWaveItemView
    }

    override fun fixItemCount(totalCount: Int): Int {
        return totalCount
    }

    override fun onItemViewCreated(parent: ViewGroup, rulerView: EditWaveItemView) {
        /*
         * When the big screen recording,
         * it will call the onCreateViewHolder() again and again.
         * register() does not stop call, resulting in continuous growth of memory,
         * and the actual recording business scene does not need IPublisher object.
         */
        if (parent is IPublisher) {
            (parent as IPublisher).register(rulerView)
        }
    }

    override fun onBindItemView(rulerView: EditWaveItemView, position: Int) {
        rulerView.amplitudes = mAmplitudeValue
        rulerView.setDecodedAmplitudeList(mDecodedAmplitudeList)
    }

    fun setCutStartTime(time: Long) {
        startRecord = time
        val subscribers = mSubscribers ?: return
        for (subscriber in subscribers) {
            subscriber.notifyStartTime()
        }
    }

    fun setCutEndTime(time: Long) {
        endRecord = time
        val subscribers = mSubscribers ?: return
        for (subscriber in subscribers) {
            subscriber.notifyEndTime()
        }
    }

    fun removeAllPointTime() {
        startRecord = null
        endRecord = null
        val subscribers = mSubscribers ?: return
        for (subscriber in subscribers) {
            subscriber.notifyEndTime()
        }
    }

    fun resetStatus() {
        startRecord = null
        endRecord = null
        postInvalidateOnAnimation()
    }

    fun setLastPlayTime(time: Long) {
        mLastTime = time
        notifyLastTimeChange()
    }

    fun getLastTime(): Long? {
        return mLastTime
    }

    private fun notifyLastTimeChange() {
        val subscribers = mSubscribers ?: return
        for (subscriber in subscribers) {
            subscriber.notifyEndTime()
        }
    }

    override fun calculateCorrectOffset(currentTimeMillis: Long, offset: Int): Int {
        var calculateOffset = offset
        if (mTotalTime > 0 && currentTimeMillis == mTotalTime) {
            calculateOffset -= diffMoreThanLastItemRealWidth
        }
        return calculateOffset
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        mainHandler.removeCallbacksAndMessages(null)
        mSubscribers = null
        onTouchDownOnWaveBar = null
        onTouchUpOnWaveBar = null
        handlerMoveListener = null
    }
}