apply from: "../../common_build.gradle"
apply plugin: 'org.jetbrains.kotlin.android'

android {
    namespace "com.soundrecorder.browsefile"
}

dependencies {
    implementation fileTree(include: ['*.so'], dir: 'libs')
    implementation libs.androidx.support
    implementation libs.androidx.appcompat
    //kotlin
    implementation libs.org.kotlin.stdlib
    implementation libs.androidx.lifecycle.livedata
    implementation libs.androidx.lifecycle.viewmodel
    implementation libs.androidx.lifecycle.extensions
    implementation libs.androidx.paging
    implementation libs.androidx.constraintlayout
    // fragment
    implementation libs.androidx.fragment.ktx
    implementation project(path: ':common:RecorderLogBase')

    kaptTest libs.androidx.databinding.compiler

    // base包为必须引用的包，prop_versionName需保持一致
    implementation (libs.oplus.coui.core) {
        exclude group: 'com.squareup.okio', module: 'okio'
    }
    // 以下子包应用可选使用，如有使用了如下子包的控件，则需要添加，未使用可以不引用
    implementation libs.oplus.coui.reddot
    implementation libs.oplus.coui.snackbar
    implementation libs.oplus.coui.cardview
    implementation libs.oplus.coui.bottomnavigation
    implementation libs.oplus.coui.recyclerview
    implementation libs.oplus.coui.dialog
    implementation libs.oplus.coui.scrollbar
    implementation libs.oplus.coui.toolbar
    implementation libs.oplus.coui.springchain
    implementation libs.oplus.coui.poplist
    implementation libs.oplus.coui.tips
    implementation libs.oplus.coui.card
    implementation libs.oplus.coui.preference
    implementation libs.oplus.coui.input
    implementation libs.oplus.coui.seekbar
    implementation libs.oplus.coui.rotateview
    implementation libs.oplus.coui.grid
    implementation libs.oplus.coui.chip
    implementation libs.oplus.coui.progressbar
    implementation libs.oplus.coui.panel
    implementation libs.oplus.coui.responsiveui

    implementation(libs.oplus.material)
    compileOnly libs.oplus.addon
    testImplementation libs.oplus.addon

    implementation(libs.facebook)
    implementation libs.gson

    testImplementation libs.oplus.support.adapter
    //sau
    implementation libs.oplus.coui.sauaar

    // Koin for Android
    implementation(libs.koin)

    testImplementation project(':common:RecorderLogBase')
    implementation project(':common:modulerouter')
    implementation project(':common:libbase')
    implementation project(':common:libcommon')
    implementation project(':component:player')
    implementation project(':component:summary')
}
