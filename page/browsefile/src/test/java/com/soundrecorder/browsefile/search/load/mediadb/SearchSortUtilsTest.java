package com.soundrecorder.browsefile.search.load.mediadb;

import static org.mockito.Mockito.when;
import android.database.Cursor;
import android.os.Build;
import android.provider.MediaStore;

import androidx.test.ext.junit.runners.AndroidJUnit4;

import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.powermock.reflect.Whitebox;
import org.robolectric.annotation.Config;

import java.util.ArrayList;

import com.soundrecorder.browsefile.search.load.ItemSearchViewModel;
import com.soundrecorder.browsefile.shadows.ShadowFeatureOption;

@RunWith(AndroidJUnit4.class)
@Config(sdk = Build.VERSION_CODES.S, shadows = {ShadowFeatureOption.class})
public class SearchSortUtilsTest {
    private ArrayList<ItemSearchViewModel> searchBeans = new ArrayList<>();

    @Before
    public void setUp() {
        ItemSearchViewModel itemSearchViewModel1 = new ItemSearchViewModel();
        itemSearchViewModel1.setSearchValue("111");
        itemSearchViewModel1.setDisplayName("1111111");
        ItemSearchViewModel itemSearchViewModel2 = new ItemSearchViewModel();
        itemSearchViewModel2.setSearchValue("111");
        itemSearchViewModel2.setDisplayName("111");
        ItemSearchViewModel itemSearchViewModel3 = new ItemSearchViewModel();
        itemSearchViewModel3.setSearchValue("111");
        itemSearchViewModel3.setDisplayName("111111111111111111111");
        searchBeans.add(itemSearchViewModel1);
        searchBeans.add(itemSearchViewModel2);
        searchBeans.add(itemSearchViewModel3);
    }

    @Test
    public void should_sort_when_doSortSearchBeans() {
        SearchSortUtils.doSortSearchBeans(searchBeans);
        Assert.assertEquals(searchBeans.get(0).getDisplayName(), "111");
    }

    @Test
    public void should_vualue_when_cursorToArrayList() {
        Cursor mockCursor = Mockito.mock(Cursor.class);
        when(mockCursor.moveToFirst()).thenReturn(true);
        when(mockCursor.getLong(mockCursor.getColumnIndex(MediaStore.Audio.Media.DURATION))).thenReturn(1l);
        when(mockCursor.getString(mockCursor.getColumnIndex(MediaStore.Audio.Media.DATA))).thenReturn("path");
        when(mockCursor.getString(mockCursor.getColumnIndex(MediaStore.Audio.Media.DISPLAY_NAME))).thenReturn("111");
        when(mockCursor.getLong(mockCursor.getColumnIndex(MediaStore.Audio.Media._ID))).thenReturn(1l);
        ArrayList<ItemSearchViewModel> itemSearchViewModels = SearchSortUtils.cursorToArrayList(mockCursor, "111");
        Assert.assertEquals(itemSearchViewModels.size(), 1);
    }

    @Test
    public void should_vualue_when_getSearchValueIndexWithText() throws Exception {
        ArrayList<Integer> integerArrayList = Whitebox.invokeMethod(new SearchSortUtils(), "getSearchValueIndexWithText", new ItemSearchViewModel(), "", "111");
        Assert.assertEquals(integerArrayList.size(), 0);
        ArrayList<Integer> integerArrayList2 = Whitebox.invokeMethod(new SearchSortUtils(), "getSearchValueIndexWithText", new ItemSearchViewModel(), "1111", "111");
        Assert.assertEquals(integerArrayList2.size(), 2);
        ArrayList<Integer> integerArrayList3 = Whitebox.invokeMethod(new SearchSortUtils(), "getSearchValueIndexWithText", new ItemSearchViewModel(), "11", "1121");
        Assert.assertEquals(integerArrayList3.size(), 2);
    }

    @After
    public void tearDown() {
        searchBeans.clear();
    }
}
