package com.soundrecorder.browsefile.home.view.behavior;

import static com.soundrecorder.browsefile.home.view.behavior.PrimaryTitleBehavior.LOCATION_STATE_ABOVE;
import static com.soundrecorder.browsefile.home.view.behavior.PrimaryTitleBehavior.LOCATION_STATE_BELOW;

import android.os.Build;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.lifecycle.LifecycleOwner;
import androidx.lifecycle.MutableLiveData;
import androidx.recyclerview.widget.COUIRecyclerView;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.test.ext.junit.runners.AndroidJUnit4;

import com.coui.appcompat.toolbar.COUIToolbar;
import com.google.android.material.appbar.AppBarLayout;
import com.soundrecorder.base.utils.WindowType;
import com.soundrecorder.browsefile.BrowseFile;
import com.soundrecorder.browsefile.R;
import com.soundrecorder.browsefile.home.item.BrowseAdapter;
import com.soundrecorder.browsefile.home.item.FastPlayHelper;
import com.soundrecorder.browsefile.home.item.IBrowseViewHolderListener;
import com.soundrecorder.browsefile.home.item.ItemBrowseRecordViewModel;
import com.soundrecorder.browsefile.home.view.SubTitleLayout;
import com.soundrecorder.browsefile.shadows.ShadowFeatureOption;
import com.soundrecorder.browsefile.shadows.ShadowOS12FeatureUtil;
import com.soundrecorder.browsefile.shadows.ShadowOplusUsbEnvironment;
import com.soundrecorder.common.databean.StartPlayModel;

import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.reflect.Whitebox;
import org.robolectric.Robolectric;
import org.robolectric.annotation.Config;

import java.util.ArrayList;

@RunWith(AndroidJUnit4.class)
@Config(sdk = Build.VERSION_CODES.S, shadows = {ShadowOplusUsbEnvironment.class, ShadowOS12FeatureUtil.class, ShadowFeatureOption.class})
public class PrimaryTitleBehaviorTest {
    private static final String FUNC_NAME_GET_CHILD = "getChild";
    private static final String FUNC_NAME_GET_LOCATION = "getLocationYInWindow";

    private BrowseFile mActivity;
    private final IBrowseViewHolderListener mListener = new IBrowseViewHolderListener() {
        @Override
        public void showSummaryTip(@Nullable View anchorView) {
            // do nothing
        }

        @Override
        public void onClickSummaryItem(@NonNull ItemBrowseRecordViewModel data) {
            // do nothing
        }

        @Override
        public void onClickConvertItem(@NonNull ItemBrowseRecordViewModel data) {
            // do nothing
        }

        @Override
        public void onClickSummaryIcon(@NonNull ItemBrowseRecordViewModel data) {
            // do nothing
        }

        @Override
        public void onLongClickItem(@NonNull View view, @NonNull ItemBrowseRecordViewModel data) {
            // do nothing
        }

        @Override
        public boolean canShowAddAnimator(@NonNull ItemBrowseRecordViewModel data) {
            return false;
        }

        @Override
        public boolean isCurrentPlay(@NonNull ItemBrowseRecordViewModel data) {
            return false;
        }

        @Override
        public void onClickItem(@NonNull ItemBrowseRecordViewModel data) {
        }

        @Nullable
        @Override
        public FastPlayHelper getPlayerController() {
            return null;
        }

        @NonNull
        @Override
        public MutableLiveData<WindowType> getWindowType() {
            return new MutableLiveData<>();
        }

        @NonNull
        @Override
        public MutableLiveData<StartPlayModel> getPlayLiveData() {
            return new MutableLiveData<>();
        }
    };

    @Before
    public void setUp() {
        mActivity = Robolectric.buildActivity(BrowseFile.class).get();
    }

    @After
    public void clear() {
        mActivity = null;
    }

    @Test
    public void assert_not_null_when_create() {
        PrimaryTitleBehavior behavior = new PrimaryTitleBehavior(mActivity, null);
        COUIRecyclerView recyclerView = getRecyclerView();
        AppBarLayout appBarLayout = getAppBarLayout();
        behavior.ensureBehaviorView(getCoordinatorLayout(), appBarLayout, recyclerView);
    }

    @Test
    public void assert_not_null_when_addListener() throws Exception {
        PrimaryTitleBehavior behavior = new PrimaryTitleBehavior(mActivity, null);

        COUIRecyclerView recyclerView = getRecyclerView();
        AppBarLayout appBarLayout = getAppBarLayout();
        behavior.ensureBehaviorView(getCoordinatorLayout(), appBarLayout, recyclerView);

        Whitebox.setInternalState(behavior, "isSearch", false);
        Whitebox.invokeMethod(behavior, "addListener");

        Whitebox.setInternalState(behavior, "isSearch", true);
        Whitebox.setInternalState(behavior, "isDoTopMarginAnim", false);
        Whitebox.invokeMethod(behavior, "addListener");

        Whitebox.setInternalState(behavior, "isDoTopMarginAnim", true);
        Whitebox.setInternalState(behavior, "isCanScroll", false);
        Whitebox.invokeMethod(behavior, "addListener");

        Whitebox.setInternalState(behavior, "isCanScroll", true);
        Whitebox.invokeMethod(behavior, "addListener");
    }

    @Test
    public void assert_not_null_when_checkSubTitleHeightChange() throws Exception {
        PrimaryTitleBehavior behavior = new PrimaryTitleBehavior(mActivity, null);

        COUIRecyclerView recyclerView = getRecyclerView();
        AppBarLayout appBarLayout = getAppBarLayout();
        behavior.ensureBehaviorView(getCoordinatorLayout(), appBarLayout, recyclerView);

        Whitebox.invokeMethod(behavior, "checkSubTitleHeightChange");
        SubTitleLayout mSubtitle = Whitebox.getInternalState(behavior, "mSubtitle");
        Assert.assertNotNull(mSubtitle);
        Whitebox.setInternalState(behavior, "mSubtitle", mSubtitle);
        Assert.assertEquals(0, mSubtitle.getHeight());

        Whitebox.invokeMethod(behavior, "checkSubTitleHeightChange");
    }

    @Test
    public void assert_not_null_when_updateView() throws Exception {
        PrimaryTitleBehavior behavior = new PrimaryTitleBehavior(mActivity, null);
        COUIRecyclerView recyclerView = getRecyclerView();
        AppBarLayout appBarLayout = getAppBarLayout();
        behavior.ensureBehaviorView(getCoordinatorLayout(), appBarLayout, recyclerView);
        behavior.onListScroll(false);
        behavior.onListScroll(true);

        int mSubTitleHeight = Whitebox.getInternalState(behavior, "mSubTitleHeight");
        int mSubTitleInitHeight = Whitebox.getInternalState(behavior, "mSubTitleInitHeight");
        int scrollDistance = Whitebox.getInternalState(behavior, "scrollDistance");
        float mRatio = Whitebox.getInternalState(behavior, "mRatio");
        LinearLayout mContent = Whitebox.getInternalState(behavior, "mContent");

        Assert.assertEquals(0, mSubTitleHeight);
        Assert.assertEquals(0, mSubTitleInitHeight);
        Assert.assertEquals(0, scrollDistance);
        Assert.assertEquals(0f, mRatio, 0f);
        Assert.assertNotNull(mContent);

        Whitebox.invokeMethod(behavior, "updateView", false);

        Whitebox.setInternalState(behavior, "mRatio", 1f);
        Assert.assertEquals(1f, (float) Whitebox.getInternalState(behavior, "mRatio"), 0f);
        Whitebox.invokeMethod(behavior, "updateView", false);

        Whitebox.setInternalState(behavior, "mContent", (Object[]) null);
        Whitebox.invokeMethod(behavior, "updateView", false);
        Assert.assertEquals(1, (int) Whitebox.getInternalState(behavior, "scrollDistance"));
    }

    @Test
    public void assert_null_when_release() {
        PrimaryTitleBehavior behavior = new PrimaryTitleBehavior(mActivity, null);
        COUIRecyclerView recyclerView = getRecyclerView();
        AppBarLayout appBarLayout = getAppBarLayout();
        behavior.ensureBehaviorView(getCoordinatorLayout(), appBarLayout, recyclerView);

        Assert.assertNotNull(Whitebox.getInternalState(behavior, "mReboundListener"));
        behavior.release();
        Assert.assertNull(Whitebox.getInternalState(behavior, "mReboundListener"));
    }

    @Test
    public void assert_null_when_getLocationYInWindow() throws Exception {
        PrimaryTitleBehavior behavior = new PrimaryTitleBehavior(mActivity, null);
        LifecycleOwner lifecycleOwner = () -> mActivity.getLifecycle();
        COUIRecyclerView recyclerView = new COUIRecyclerView(mActivity);
        LinearLayoutManager layoutManager = new LinearLayoutManager(mActivity);
        BrowseAdapter adapter = new BrowseAdapter(lifecycleOwner, mActivity, mListener);
        recyclerView.setLayoutManager(layoutManager);
        recyclerView.setAdapter(adapter);
        ArrayList<ItemBrowseRecordViewModel> arrayList = new ArrayList<>();
        ItemBrowseRecordViewModel model = new ItemBrowseRecordViewModel();
        arrayList.add(model);
        AppBarLayout appBarLayout = getAppBarLayout();
        behavior.ensureBehaviorView(getCoordinatorLayout(), appBarLayout, recyclerView);

        Whitebox.setInternalState(behavior, "scrollStartY", 100);

        View childView = Whitebox.invokeMethod(behavior, FUNC_NAME_GET_CHILD);
        Assert.assertEquals(100, (int) Whitebox.invokeMethod(behavior, FUNC_NAME_GET_LOCATION, childView));

        adapter.setData(arrayList);
        childView = Whitebox.invokeMethod(behavior, FUNC_NAME_GET_CHILD);
        Assert.assertEquals(0, (int) Whitebox.invokeMethod(behavior, FUNC_NAME_GET_LOCATION, childView));

        for (int i = 0; i < 10; i++) {
            arrayList.add(model);
        }
        adapter.setData(arrayList);
        childView = Whitebox.invokeMethod(behavior, FUNC_NAME_GET_CHILD);
        Assert.assertEquals(0, (int) Whitebox.invokeMethod(behavior, FUNC_NAME_GET_LOCATION, childView));
    }

    @Test
    public void assert_null_when_getDiffWithSearch() {
        PrimaryTitleBehavior behavior = new PrimaryTitleBehavior(mActivity, null);
        COUIRecyclerView recyclerView = getRecyclerView();
        AppBarLayout appBarLayout = getAppBarLayout();
        behavior.ensureBehaviorView(getCoordinatorLayout(), appBarLayout, recyclerView);

        int systemBarInsetsTop = Whitebox.getInternalState(behavior, "systemBarInsetsTop");
        Assert.assertEquals(systemBarInsetsTop, behavior.getDiffWithSearch());

        behavior.updateSystemBarInsetsTop(100);
        Assert.assertEquals(100, behavior.getDiffWithSearch());
    }

    @Test
    public void assert_null_when_expandSubTitle() {
        PrimaryTitleBehavior behavior = new PrimaryTitleBehavior(mActivity, null);
        COUIRecyclerView recyclerView = getRecyclerView();
        AppBarLayout appBarLayout = getAppBarLayout();
        behavior.ensureBehaviorView(getCoordinatorLayout(), appBarLayout, recyclerView);

        behavior.expandSubTitle();
        behavior.unexpandSubTitle();
        behavior.setCanScroll(true);
        Assert.assertTrue(behavior.getCanScroll());
        behavior.setCanScroll(false);
        Assert.assertFalse(behavior.getCanScroll());
    }

    @Test
    public void assert_null_when_getFirstBrowseViewMoreThanListView() {
        PrimaryTitleBehavior behavior = new PrimaryTitleBehavior(mActivity, null);
        LifecycleOwner lifecycleOwner = () -> mActivity.getLifecycle();
        COUIRecyclerView recyclerView = new COUIRecyclerView(mActivity);
        LinearLayoutManager layoutManager = new LinearLayoutManager(mActivity);
        BrowseAdapter adapter = new BrowseAdapter(lifecycleOwner, mActivity, mListener);
        recyclerView.setLayoutManager(layoutManager);

        Assert.assertEquals(LOCATION_STATE_BELOW, behavior.getFirstBrowseViewMoreThanListView());

        AppBarLayout appBarLayout = getAppBarLayout();

        behavior.ensureBehaviorView(getCoordinatorLayout(), appBarLayout, recyclerView);
        Assert.assertEquals(LOCATION_STATE_BELOW, behavior.getFirstBrowseViewMoreThanListView());

        recyclerView.setAdapter(adapter);
        Assert.assertEquals(LOCATION_STATE_BELOW, behavior.getFirstBrowseViewMoreThanListView());

        ArrayList<ItemBrowseRecordViewModel> arrayList = new ArrayList<>();
        ItemBrowseRecordViewModel model = new ItemBrowseRecordViewModel();
        arrayList.add(model);
        for (int i = 0; i < 10; i++) {
            arrayList.add(model);
        }
        adapter.setData(arrayList);
        Assert.assertEquals(LOCATION_STATE_ABOVE, behavior.getFirstBrowseViewMoreThanListView());
    }

    private CoordinatorLayout getCoordinatorLayout() {
        CoordinatorLayout layout = new CoordinatorLayout(mActivity);
        layout.setLayoutParams(new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT));
        FrameLayout frameLayout = new FrameLayout(mActivity);
        frameLayout.setLayoutParams(new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT));
        frameLayout.setId(R.id.gradientBackground);
        layout.addView(frameLayout);

        View dividerView = new View(mActivity);
        frameLayout.setLayoutParams(new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT));
        frameLayout.setId(R.id.divider_line_record_panel);
        layout.addView(dividerView);
        return layout;
    }

    private AppBarLayout getAppBarLayout() {
        AppBarLayout appBarLayout = new AppBarLayout(mActivity);
        appBarLayout.setLayoutParams(new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT));
        COUIToolbar toolbar = new COUIToolbar(mActivity);
        toolbar.setId(R.id.browse_toolbar);
        appBarLayout.addView(toolbar);

        LinearLayout content = new LinearLayout(mActivity);
        content.setLayoutParams(new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT));
        content.setId(R.id.content);
        appBarLayout.addView(content);

        LinearLayout main_title = new LinearLayout(mActivity);
        main_title.setLayoutParams(new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT));
        main_title.setId(R.id.main_title);
        appBarLayout.addView(main_title);

        TextView toolbar_title = new TextView(mActivity);
        toolbar_title.setLayoutParams(new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT));
        toolbar_title.setId(R.id.toolbar_title);
        appBarLayout.addView(toolbar_title);

        View divider_line = new View(mActivity);
        divider_line.setLayoutParams(new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT));
        divider_line.setId(R.id.divider_line);
        appBarLayout.addView(divider_line);

        SubTitleLayout toolbar_subtitle = new SubTitleLayout(mActivity);
        toolbar_subtitle.setLayoutParams(new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, 100));
        toolbar_subtitle.setId(R.id.toolbar_subtitle);
        appBarLayout.addView(toolbar_subtitle);
        return appBarLayout;
    }

    private COUIRecyclerView getRecyclerView() {
        LifecycleOwner lifecycleOwner = () -> mActivity.getLifecycle();
        COUIRecyclerView recyclerView = new COUIRecyclerView(mActivity);
        LinearLayoutManager layoutManager = new LinearLayoutManager(mActivity);
        BrowseAdapter adapter = new BrowseAdapter(lifecycleOwner, mActivity, mListener);
        recyclerView.setLayoutManager(layoutManager);
        recyclerView.setAdapter(adapter);
        return recyclerView;
    }
}