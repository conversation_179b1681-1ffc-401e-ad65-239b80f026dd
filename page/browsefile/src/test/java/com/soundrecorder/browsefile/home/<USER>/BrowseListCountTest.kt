/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: BrowseListCountTest
 * Description:
 * Version: 1.0
 * Date: 2023/12/26
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2023/12/26 1.0 create
 */

package com.soundrecorder.browsefile.home.load

import android.os.Build
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.browsefile.shadows.ShadowFeatureOption
import com.soundrecorder.browsefile.shadows.ShadowOS12FeatureUtil
import org.junit.Assert
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(
    sdk = [Build.VERSION_CODES.S],
    shadows = [ShadowOS12FeatureUtil::class, ShadowFeatureOption::class]
)
class BrowseListCountTest {

    @Test
    fun should_correct_when_addAllCount() {
        val bean = BrowseListCount()
        bean.addAllCount()
        Assert.assertTrue(bean.allCount > 0)
    }

    @Test
    fun should_correct_when_addStandardCount() {
        val bean = BrowseListCount()
        bean.addStandardCount()
        Assert.assertTrue(bean.standardCount > 0)
    }

    @Test
    fun should_correct_when_addMeetingCount() {
        val bean = BrowseListCount()
        bean.addMeetingCount()
        Assert.assertTrue(bean.meetingCount > 0)
    }

    @Test
    fun should_correct_when_addInterviewCount() {
        val bean = BrowseListCount()
        bean.addInterviewCount()
        Assert.assertTrue(bean.interviewCount > 0)
    }

    @Test
    fun should_correct_when_addCallCount() {
        val bean = BrowseListCount()
        bean.addCallCount()
        Assert.assertTrue(bean.callCount > 0)
    }

    @Test
    fun should_correct_when_addRecentlyCount() {
        val bean = BrowseListCount()
        bean.addCallCount()
        Assert.assertTrue(bean.recycleCount > 0)
    }

    @Test
    fun should_correct_when_release() {
        val bean = BrowseListCount()
        bean.callCount = 20
        bean.standardCount = 40
        bean.meetingCount = 20
        bean.interviewCount = 20
        bean.allCount = 100
        bean.recycleCount = 20

        bean.release()
        Assert.assertEquals(0, bean.recycleCount)
        Assert.assertEquals(0, bean.callCount)
        Assert.assertEquals(0, bean.allCount)
        Assert.assertEquals(0, bean.standardCount)
        Assert.assertEquals(0, bean.meetingCount)
        Assert.assertEquals(0, bean.interviewCount)
    }
}