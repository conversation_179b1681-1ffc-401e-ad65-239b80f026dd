<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.soundrecorder.browsefile">

    <uses-sdk tools:overrideLibrary="com.oplus.sdk.addon.sdk"/>
    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />

    <application>
        <activity
            android:name="com.soundrecorder.browsefile.BrowseFile"
            android:configChanges="locale|keyboardHidden|mcc|mnc|navigation"
            android:exported="true"
            android:launchMode="singleTop"
            android:screenOrientation="behind"
            android:theme="@style/AppBaseTheme.NoActionBar.ActionMode.LocalDirection.CardPage">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
            <intent-filter>
                <!--新增action 锁屏页面跳转到activity-->
                <action android:name="com.soundrecorder.browsefile.LOCK_SCREEN"/>
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <intent-filter>
                <action android:name="oplus.intent.action.provider.start_browse_file_from_smallcard" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>

            <meta-data
                android:name="color.support.UI_OPTIONS"
                android:value="splitActionBarWhenNarrow" />
        </activity>
        <!--兼容老版本incallUI指定了类路径-->
        <activity-alias
            android:name="oppo.multimedia.soundrecorder.filebrowser.BrowseFile"
            android:exported="true"
            android:targetActivity="com.soundrecorder.browsefile.BrowseFile">
            <!--action 由于在teleservice使用的包名+action形式，so 不再现有activity中增加action-->
            <intent-filter>
                <action android:name="oppo.intent.action.BROWSE_FILE" />
                <action android:name="oplus.intent.action.BROWSE_FILE" />
                <action android:name="oplus.intent.action.THREAD_RECORD" />
                <action android:name="oplus.intent.action.BROWSE_OSHARE_FILE" />

                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>

            <intent-filter>
                <action android:name="android.provider.MediaStore.RECORD_SOUND" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity-alias>

        <provider
            android:name="com.soundrecorder.browsefile.search.load.SearchCenterProvider"
            android:authorities="com.oplus.soundrecorder.searchcenter.provider"
            android:exported="true"
            android:permission="com.oppo.permission.safe.PRIVATE" />

        <!--for center search receiver-->
        <receiver
            android:name="com.soundrecorder.browsefile.search.load.center.receiver.CenterDmpPackageReceiver"
            android:exported="true"
            android:permission="oplus.permission.OPLUS_COMPONENT_SAFE">
            <intent-filter>
                <action android:name="android.intent.action.PACKAGE_DATA_CLEARED" />
                <data android:scheme="package" />
            </intent-filter>

            <intent-filter>
                <action android:name="com.oplus.dmp.action.STATUS" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </receiver>

        <!--        是否支持展示互传传输的录音文件，1表示支持-->
        <meta-data
            android:name="support_show_oshare_recording"
            android:value="1"/>
    </application>
</manifest>