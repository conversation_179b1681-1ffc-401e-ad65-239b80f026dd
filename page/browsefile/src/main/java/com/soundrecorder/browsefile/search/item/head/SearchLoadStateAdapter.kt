/*********************************************************************
 * * Copyright (C), 2021, OPlus. All rights reserved.
 * * VENDOR_EDIT
 * * File        :  -
 * * Version     : 1.0
 * * Date        : 2021/8/20
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.browsefile.search.item.head

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.paging.LoadState
import androidx.paging.LoadStateAdapter
import androidx.recyclerview.widget.RecyclerView
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.browsefile.R
import com.soundrecorder.browsefile.databinding.SearchLoadStateItemBinding

class SearchLoadStateAdapter : LoadStateAdapter<SearchLoadStateViewHolder>() {

    override fun onBindViewHolder(
        holder: SearchLoadStateViewHolder,
        loadState: LoadState
    ) = holder.bind(loadState)

    override fun onCreateViewHolder(
        parent: ViewGroup,
        loadState: LoadState
    ) = SearchLoadStateViewHolder.create(parent)

    override fun displayLoadStateAsItem(loadState: LoadState): Boolean {
        return loadState is LoadState.Loading
    }
}

class SearchLoadStateViewHolder(
    private val mBinding: SearchLoadStateItemBinding
) : RecyclerView.ViewHolder(mBinding.root) {

    fun bind(loadState: LoadState) {
        DebugUtil.d("SearchLoadStateAdapter", "onBind, load state is $loadState")
        mBinding.progressBar.isVisible = loadState is LoadState.Loading
    }

    companion object {
        fun create(parent: ViewGroup): SearchLoadStateViewHolder {
            val view = LayoutInflater.from(parent.context)
                .inflate(R.layout.search_load_state_item, parent, false)
            val binding = SearchLoadStateItemBinding.bind(view)
            return SearchLoadStateViewHolder(binding)
        }
    }
}