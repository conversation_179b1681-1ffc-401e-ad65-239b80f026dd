/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: BrowseAnimUtil
 * Description:
 * Version: 1.0
 * Date: 2022/12/27
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2022/12/27 1.0 create
 */

package com.soundrecorder.browsefile.parentchild

import android.view.View
import android.view.ViewGroup
import android.view.animation.PathInterpolator
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.constraintlayout.widget.Guideline
import androidx.core.view.isVisible
import androidx.transition.ChangeBounds
import androidx.transition.Transition
import androidx.transition.TransitionManager
import androidx.transition.TransitionSet
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.browsefile.R

/**
 * 小屏小进退播放页面的动画工具
 */
@Suppress("MagicNumber")
class BrowseAnimUtil(
    private val parentStartLine: Guideline?,
    private val parentEndLine: Guideline?,
    private val childStartLine: Guideline?,
    private val childEndLine: Guideline?,
    private val parentView: ViewGroup?,
    private val childView: ViewGroup?
) {

    companion object {
        const val ANIM_DURATION = 350L

        const val GUIDE_PERCENT_0 = 0F
        const val GUIDE_PERCENT_1 = 1F
        const val GUIDE_PERCENT_NEGATIVE_0_3 = -0.3F
        const val GUIDE_PERCENT_0_7 = 0.7F
        const val GUIDE_PERCENT_2 = 2F

        const val PARENT_ALPHA_0 = 0F
        const val PARENT_ALPHA_0_3 = 0.3F
    }

    private val logTag = "BrowseAnimUtil"
    private var openEnterInterpolator = PathInterpolator(0.3f, 0.1f, 0.3f, 1f)
    private var closeEnterInterpolator = PathInterpolator(0.3f, 0.26f, 0.4f, 1f)

    private var mParentMaskView: View? = null

    /**
     * setCustomAnimations(R.anim.coui_open_slide_enter, R.anim.coui_open_slide_exit)
     */
    fun runShowDetailAnim(animEndCallback: (() -> Unit)) {
        DebugUtil.d(logTag, " runShowDetailAnim")
        ensureAddParentMaskView()
        mParentMaskView?.alpha = PARENT_ALPHA_0

        val transition = TransitionSet()
        transition.addTransition(ChangeBounds())
        transition.interpolator = openEnterInterpolator
        transition.duration = ANIM_DURATION
        transition.addListener(object : Transition.TransitionListener {
            override fun onTransitionStart(transition: Transition) {
            }

            override fun onTransitionEnd(transition: Transition) {
                DebugUtil.i(logTag, "runShowDetailAnim onTransitionEnd")
                animEndCallback.invoke()
                hideParentMaskView()
            }

            override fun onTransitionCancel(transition: Transition) {
            }

            override fun onTransitionPause(transition: Transition) {
            }

            override fun onTransitionResume(transition: Transition) {
            }
        })
        childView?.let {
            transition.excludeChildren(it, true)
            TransitionManager.beginDelayedTransition(it.parent as ViewGroup, transition)
        }

        parentStartLine?.setGuidelinePercent(GUIDE_PERCENT_NEGATIVE_0_3) // -0.3
        parentEndLine?.setGuidelinePercent(GUIDE_PERCENT_0_7) // 0.7
        childStartLine?.setGuidelinePercent(GUIDE_PERCENT_0) // 0
        childEndLine?.setGuidelinePercent(GUIDE_PERCENT_1) // 1
        mParentMaskView?.alpha = PARENT_ALPHA_0_3
    }

    /**
     * setCustomAnimations(R.anim.coui_close_slide_enter, R.anim.coui_close_slide_exit)
     */
    fun runRemoveDetailAnim(startDelay: Long = 0, endFunc: (() -> Unit)) {
        DebugUtil.d(logTag, " runRemoveDetailAnim")
        ensureAddParentMaskView()
        mParentMaskView?.alpha = PARENT_ALPHA_0_3

        val transition = TransitionSet()
        transition.addTransition(ChangeBounds())
        transition.interpolator = closeEnterInterpolator
        transition.duration = ANIM_DURATION
        transition.startDelay = startDelay
        transition.addListener(object : Transition.TransitionListener {
            override fun onTransitionStart(transition: Transition) {
                DebugUtil.d(logTag, "onTransitionStart")
            }

            override fun onTransitionEnd(transition: Transition) {
                DebugUtil.i(logTag, "onTransitionEnd")
                (mParentMaskView?.parent as? ViewGroup)?.removeView(mParentMaskView)
                endFunc.invoke()
            }

            override fun onTransitionCancel(transition: Transition) {
                DebugUtil.d(logTag, "onTransitionCancel")
            }

            override fun onTransitionPause(transition: Transition) {
                DebugUtil.d(logTag, "onTransitionPause")
            }

            override fun onTransitionResume(transition: Transition) {
                DebugUtil.i(logTag, "onTransitionResume")
            }
        })

        TransitionManager.beginDelayedTransition(childView?.parent as ViewGroup, transition)

        parentStartLine?.setGuidelinePercent(GUIDE_PERCENT_0) // 0
        parentEndLine?.setGuidelinePercent(GUIDE_PERCENT_1) // 1
        childStartLine?.setGuidelinePercent(GUIDE_PERCENT_1) // 1
        childEndLine?.setGuidelinePercent(GUIDE_PERCENT_2) // 2
        mParentMaskView?.alpha = PARENT_ALPHA_0
    }

    fun hideParentMaskView() {
        if (mParentMaskView?.parent != null) {
            mParentMaskView?.isVisible = false
        }
    }

    fun release() {
        mParentMaskView = null
    }

    private fun ensureAddParentMaskView() {
        parentView?.let {
            if (mParentMaskView == null) {
                mParentMaskView = View(it.context)
                mParentMaskView?.background = it.context.getDrawable(com.soundrecorder.common.R.color.black_color)
            }
            mParentMaskView?.isVisible = true
            if (mParentMaskView?.parent == null) {
                parentView.addView(mParentMaskView, ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT)
            }
        }
    }
}

fun Guideline?.guidePercent(): Float {
    if (this == null) {
        return -1F
    }
    return (layoutParams as? ConstraintLayout.LayoutParams)?.guidePercent ?: -1F
}