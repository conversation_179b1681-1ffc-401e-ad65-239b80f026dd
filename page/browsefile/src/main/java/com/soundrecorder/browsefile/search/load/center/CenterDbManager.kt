package com.soundrecorder.browsefile.search.load.center

import android.database.Cursor
import android.net.Uri
import android.os.Bundle
import androidx.core.database.getStringOrNull
import androidx.core.os.bundleOf
import com.oplus.recorderlog.util.GsonUtil
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.ext.title
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.FeatureOption
import com.soundrecorder.browsefile.search.load.ItemSearchViewModel
import com.soundrecorder.browsefile.search.load.SearchRepository
import com.soundrecorder.browsefile.search.load.SearchResultWrapper
import com.soundrecorder.browsefile.search.load.center.CenterDbConstant.IndexProvider.SUMMARY_TEXT_FLAG
import com.soundrecorder.browsefile.search.load.center.CenterDbUtils.DMP_ENABLE_STATUS_FATAL
import com.soundrecorder.browsefile.search.load.center.databean.SearchHighLightBean
import com.soundrecorder.browsefile.search.load.center.databean.SearchInsertBean
import com.soundrecorder.browsefile.search.load.center.databean.SearchInsertExtendBean
import com.soundrecorder.browsefile.search.load.center.databean.SearchRequestBean
import com.soundrecorder.browsefile.search.load.center.localsync.CenterLocalStorageManager
import com.soundrecorder.browsefile.search.utils.ForNoteUtil
import com.soundrecorder.common.constant.RecordModeConstant
import com.soundrecorder.common.db.CursorHelper
import com.soundrecorder.common.db.MediaDBUtils
import com.soundrecorder.common.db.NoteDbUtils
import com.soundrecorder.common.utils.ConvertDbUtil
import kotlin.math.ceil
import kotlin.math.min
import org.json.JSONArray
import org.json.JSONObject

/**
 * 中子接口文档：
 * 中子接口文档：https://hio.oppo.com/app/ozone/neutron/gotoOzoneCircleKbItemDetail?source=index&cataLog_id=339667&enc_kbi_id=158086471_157605808&page=ozone&is_more_document=0&folder_id=339657&type=ARTICLE
 */
object CenterDbManager {
    const val TAG = "CenterDbManager"

    private var isSearchServiceInit = false

    // query接口需传递参数,默认为起始版本; max([firstVersion, minSearchSerategyVersion] ∩ [SSVm ... SSVn])
    private var finalSearchStrategyVersion: Int =
        CenterDbConstant.Version.SEARCH_VERSION_DEFAULT_100

    // indexProvider call(add/update/delete)需传递
    // value = min(indexProvider call(info), recorder-support-max-resourceVersion)
    // min {查询中台所得资源版本，资源方（录音）支持的最大资源版本}
    private var resourceVersion: Int = CenterDbConstant.Version.RESOURCE_VERSION_DEFAULT_100

    /**
     * 被删除文件媒体库ID列表
     */
    private val mDeleteMediaIdsList: ArrayList<Long> = arrayListOf()

    fun getDeleteMediaIdList(): ArrayList<Long> {
        return mDeleteMediaIdsList
    }

    fun addDeleteMediaId(mediaId: Long) {
        mDeleteMediaIdsList.add(mediaId)
    }

    fun addDeleteMediaIdList(mediaIdList: MutableList<Long>) {
        mDeleteMediaIdsList.addAll(mediaIdList)
    }

    fun isDeleteMediaListNotEmpty(): Boolean {
        return mDeleteMediaIdsList.isNotEmpty()
    }

    fun clearDeleteMediaIdList() {
        mDeleteMediaIdsList.clear()
    }

    /**
     * 初始化搜索服务 and
     * 获取检索、索引版本- indexprovider和searchprovider 需传参数
     */
    @JvmStatic
    fun getDmpConfigInfo(): Boolean {
        isSearchServiceInit = initSearchProvider() && getDmpResourceVersion()
        DebugUtil.i(TAG, "getDmpConfigInfo isSearchServiceInit $isSearchServiceInit")
        return isSearchServiceInit
    }

    /**
     * Full synchronization
     * notify dmp call record.db.query()
     */
    @JvmStatic
    fun notifyDmpAllSync(): Boolean {
        if (!CenterDbUtils.isCenterSearchUsable()) {
            return false
        }
        DebugUtil.i(TAG, "notifyDmpAllSync")
        return try {
            BaseApplication.getAppContext().contentResolver
                .acquireUnstableContentProviderClient(CenterDbConstant.IndexProvider.INDEX_PROVIDER_AUTHORITY)
                ?.use {
                    parseResultCodeIsSuccess(
                        it.call(
                            CenterDbConstant.IndexProvider.CALL_METHOD_DAILY_TASK,
                            CenterDbConstant.PROVIDER_INDEX,
                            bundleOf(CenterDbConstant.IndexProvider.CALL_PARAM_VERSION to resourceVersion)
                        )
                    )
                } ?: false
        } catch (e: Exception) {
            DebugUtil.e(TAG, "notifyDmpAllSync  exception = $e")
            false
        }
    }

    /**
     * 返回当前中台对应资源的资源版本resourceVersion
     */
    @JvmStatic
    fun getDmpResourceVersion(): Boolean {
        try {
            val resultBundle =
                BaseApplication.getAppContext().contentResolver
                    .acquireUnstableContentProviderClient(CenterDbConstant.IndexProvider.INDEX_PROVIDER_AUTHORITY)
                    ?.use {
                        it.call(
                            CenterDbConstant.IndexProvider.CALL_METHOD_INFO,
                            CenterDbConstant.PROVIDER_INDEX, null
                        )
                    }
            // 当前中台对应资源的资源版本resourceVersion
            return resultBundle?.getInt(
                "resourceVersion",
                CenterDbConstant.Version.RESOURCE_VERSION_DEFAULT_100
            )?.let {
                // 计算最终双方支持的资源版本
                resourceVersion = min(it, CenterDbConstant.Version.RECORDER_RESOURCE_VERSION)
                DebugUtil.i(TAG, "getDmpSupportResVersion is $resourceVersion")
                true
            } ?: false
        } catch (e: Exception) {
            DebugUtil.e(TAG, "getDmpSupportResVersion  error $e")
        }
        return false
    }

    /**
     * call to index-provider to add index
     */
    @JvmStatic
    fun insertOrUpdateDmp(
        syncCenterList: MutableList<SearchInsertBean>?,
        updateLocal: Boolean = true
    ): Boolean {
        if (syncCenterList.isNullOrEmpty()) {
            DebugUtil.i(TAG, "insertOrUpdateDmp data is null or empty")
            return true
        }
        var updateResult = true
        val pageSize = CenterDbConstant.PROVIDER_UPDATE_PAGE_SIZE
        val pageNum = ceil(syncCenterList.size * 1.0 / pageSize).toInt()
        for (i in 1..pageNum) {
            val startLength = (i - 1) * pageSize
            val endLength = if (i == pageNum) syncCenterList.size else (i * pageSize)
            updateResult = updateResult && singleInsertOrUpdateDmp(
                syncCenterList.subList(
                    startLength,
                    endLength
                ), updateLocal
            )
        }

        return updateResult
    }


    @JvmStatic
    private fun singleInsertOrUpdateDmp(
        syncCenterList: MutableList<SearchInsertBean>,
        updateLocal: Boolean = true
    ): Boolean {
        return try {
            val listValue = arrayListOf<String>()
            val isSupportNeutronVersion = ForNoteUtil.isSupportNeutronVersion()
            var extraJson: JSONObject
            syncCenterList.forEach {
                extraJson = JSONObject(GsonUtil.toJson(it))
                if (isSupportNeutronVersion && NoteDbUtils.queryNoteByMediaId(it.id.toString()) != null) {
                    //中子支持的摘要搜索的场景，并且note表有转摘要的项需要将summary_text_flag置为1
                    it.summary_text_flag = 1
                } else {
                    //中子不支持的摘要搜索的场景需要移除SUMMARY_TEXT_FLAG
                    extraJson.remove(SUMMARY_TEXT_FLAG)
                }
                listValue.add(extraJson.toString())
            }
            val bundle = Bundle().apply {
                putStringArray("list", listValue.toTypedArray())
                // 数据资源版本
                putInt(CenterDbConstant.IndexProvider.CALL_PARAM_VERSION, resourceVersion)
            }
            val resultBundle =
                BaseApplication.getAppContext().contentResolver
                    .acquireUnstableContentProviderClient(CenterDbConstant.IndexProvider.INDEX_PROVIDER_AUTHORITY)
                    ?.use {
                        it.call(
                            CenterDbConstant.IndexProvider.CALL_METHOD_UPDATE,
                            CenterDbConstant.PROVIDER_INDEX,
                            bundle
                        )
                    }

            val isSuccess = parseResultCodeIsSuccess(resultBundle)
            DebugUtil.i(TAG, "insertData count= ${syncCenterList.size}  success $isSuccess")

            if (updateLocal && isSuccess) {
                // 接口请求成功更新本地SP
                CenterLocalStorageManager.updateListToLocal(syncCenterList)
            }
            isSuccess
        } catch (e: Exception) {
            DebugUtil.e(TAG, "insertData  exception = $e")
            false
        }
    }

    /**
     * delete the index from dmp
     */
    @JvmStatic
    fun deleteByMediaId(mutableList: MutableList<Long>, updateLocal: Boolean = true): Boolean {
        return try {
            DebugUtil.d(TAG, "deleteByMediaId, mutableList:$mutableList")
            if (mutableList.isNullOrEmpty()) {
                return true
            }
            val listValue = arrayListOf<String>()
            mutableList.forEach {
                listValue.add(it.toString())
            }
            val bundle = Bundle().apply {
                putStringArray("list", listValue.toTypedArray())
                // 数据资源版本
                putInt(CenterDbConstant.IndexProvider.CALL_PARAM_VERSION, resourceVersion)
            }
            val isSuccess =
                BaseApplication.getAppContext().contentResolver
                    .acquireUnstableContentProviderClient(CenterDbConstant.IndexProvider.INDEX_PROVIDER_AUTHORITY)
                    ?.use {
                        parseResultCodeIsSuccess(
                            it.call(
                                CenterDbConstant.IndexProvider.CALL_METHOD_DELETE,
                                CenterDbConstant.PROVIDER_INDEX,
                                bundle
                            )
                        )
                    } ?: false

            if (updateLocal && isSuccess) {
                CenterLocalStorageManager.deleteListToLocal(mutableList)
            }
            isSuccess
        } catch (e: Exception) {
            DebugUtil.e(TAG, "deleteByMediaId  exception = $e")
            false
        }
    }

    /**
     * init searchService, must before do search query
     * 初始化搜索服务，若未初始化，会导致搜索为null
     * 该方法调用后，中子会检测本地是否有录音数据，若无，会触发一次500条录音数据同步
     */
    @JvmStatic
    fun initSearchProvider(): Boolean {
        DebugUtil.i(TAG, "initSearchProvider...")
        try {
            val bundle =
                BaseApplication.getAppContext().contentResolver
                    .acquireUnstableContentProviderClient(CenterDbConstant.SearchProvider.SEARCH_PROVIDER_AUTHORITY)
                    ?.use {
                        it.call(CenterDbConstant.SearchProvider.CALL_METHOD_INFO, null, null)
                    }

            return bundle?.let {
                // init result: true = success; false: failure
                val serviceStatus = it.getBoolean("serviceStatus", false)
                /*Specific initialization resources state*/
                /*Specific initialization resources state*/
                it.getBundle("message")?.let {
                    /*"success"  or “fail"*/
                    val recorderStatus = it.getString("recorder")
                    // if dmpStatus=fail,dmp被禁用
                    val dmpStatus = it.getString("dmp")
                    if ("fail" == dmpStatus) {
                        CenterDbUtils.saveDmpEnableStatus(DMP_ENABLE_STATUS_FATAL)
                    }
                    DebugUtil.i(
                        TAG,
                        "search-call-info message: recorder= $recorderStatus  dmp= $dmpStatus"
                    )
                }
                val indexVersion = it.getBundle("indexVersion")?.getInt("recorder") ?: 0
                val searchVersion = it.getInt("searchStrategyVersion") ?: 0

                finalSearchStrategyVersion = calSearchSerategyVersion(searchVersion, indexVersion)
                DebugUtil.i(
                    TAG,
                    "search.call-info dmp-serviceStatus=$serviceStatus  indexVersion$indexVersion "
                            + " searchVersion$searchVersion cal-finalSearchStrategyVersion=$finalSearchStrategyVersion"
                )
                serviceStatus
            } ?: false
        } catch (e: Exception) {
            DebugUtil.e(TAG, "initSearchProvider exception ${e.message}")
            return false
        }
    }

    /**
     * 计算searchProvider.query 接口传递的version 策略版本号
     * searchVersionFromInfo ：info接口中台支持的最高检索策略版本SearchStrategyVersion
     * indexVersionFrom：info接口获得待检索资源的索引版本IndexVersion
     */
    @JvmStatic
    private fun calSearchSerategyVersion(
        searchVersionFromInfo: Int,
        indexVersionFromInfo: Int
    ): Int {
        // 计算最小版本号： minSearchSerategyVersion = min(ssv0, ssv1)
        // 结合自身版本支持的最大检索策略版本（searchStragetyVersion 标记为ssv0）
        // 中台当前支持的最高检索策略版本 searchStragetyVersion，标记为ssv1
        val minSearchSerategyVersion: Int =
            Integer.min(CenterDbConstant.Version.RECORDER_SEARCH_VERSION, searchVersionFromInfo)

        //获取当前中台已构建索引的版本号iv支持的检索策略版本列表 [SSVm ... SSVn]
        val dmpSupportSearchVersion =
            CenterDbConstant.Version.SEARCH_SUPPORT_INDEX_LIST.filterValues {
                it.contains(indexVersionFromInfo)
            }.keys?.toList()
        // dmpSupportSearchVersion 如果为空，则为 minSearchSerategyVersion）
        // 否则 recorderSupportSearchVersion [firstVersion, minSearchSerategyVersion] ∩(交集)  dmpSupportSearchVersion[SSVm ... SSVn]
        // 交集中最大值即可得到最终协商的检索策略版本finalSearchSerategyVersion
        return if (dmpSupportSearchVersion != null && dmpSupportSearchVersion.isNotEmpty()) {
            // 得到录音支持的检索版本列表[firstVersion .. minSearchSerategyVersion] firstVersion，即初始检索策略版本
            val recorderSupportSearchVersion =
                (CenterDbConstant.Version.SEARCH_VERSION_DEFAULT_100..minSearchSerategyVersion).toList()
            recorderSupportSearchVersion.intersect(dmpSupportSearchVersion).maxOrNull()
                ?: CenterDbConstant.Version.SEARCH_VERSION_DEFAULT_100
        } else {
            minSearchSerategyVersion
        }
    }

    @JvmStatic
    fun doSearch(
        searchValues: MutableMap<String, String>,
        pageNum: Int,
        pageSize: Int,
        queryTaskId: String
    ): SearchResultWrapper {
        if (!isSearchServiceInit && !getDmpConfigInfo()) {
            DebugUtil.i(TAG, "doSearch dmp is not init")
            throw Exception("init searchService failure")
        }

        val keyWord = searchValues[SearchRepository.KEY_SEARCH_WORD]
        val supportFilter = searchValues[SearchRepository.KEY_SUPPORT_FILTER]
        val isFromOtherApp = searchValues[SearchRepository.KEY_FROM_OTHER_APP]

        if (keyWord.isNullOrEmpty()) {
            return SearchResultWrapper()
        }

        try {
            val cursor =
                BaseApplication.getAppContext().contentResolver.query(
                    Uri.parse("content://" + CenterDbConstant.SearchProvider.SEARCH_PROVIDER_AUTHORITY),
                    null,
                    genSearchRequestParam(
                        keyWord,
                        pageNum,
                        pageSize,
                        getRealSupportFilter(supportFilter),
                        isFromOtherApp == "1",
                        queryTaskId
                    ),
                    null
                )

            if (cursor == null) {
                DebugUtil.i(TAG, "doSearch dmp inner error")
                throw Exception("dmp inner error")
            }

            cursor.use {
//            cursor.registerContentObserver(object : ContentObserver(null) {
//                override fun onChange(selfChange: Boolean) {
//                    /*data changed notify*/
//                    Log.e(TAG, "onChanged: ContentObserver")
//                }
//            })
                return parseSearchResult(it, keyWord, pageNum, pageSize)
            }
        } catch (e: Exception) {
            DebugUtil.e(TAG, "dosearch error: $e")
            throw e
        }
    }

    @JvmStatic
    private fun genSearchRequestParam(
        keyWord: String,
        pageNum: Int,
        pageSize: Int,
        realSupportFilter: String?,
        isFromOtherApp: Boolean,
        queryTaskId: String
    ): Bundle {
        val searchRequestBean =
            SearchRequestBean(keyWord, pageNum, pageSize, isFromOtherApp).apply {
                taskID = queryTaskId
                if (!realSupportFilter.isNullOrBlank()) {
                    filterBy =
                        SearchRequestBean.FilterBean(
                            field = CenterDbConstant.IndexProvider.COLUMN_NAME_BUCKET,
                            value = realSupportFilter
                        )
                }
            }

        return bundleOf().apply {
            putString("searchType", "keyword")
            putString("index", CenterDbConstant.PROVIDER_INDEX)
            putInt("version", finalSearchStrategyVersion)
            putString("taskID", queryTaskId)

            putString("request", GsonUtil.toJson(searchRequestBean))
            DebugUtil.i(
                TAG,
                "doSearch:$queryTaskId,$keyWord,pageNum=$pageNum,version=$finalSearchStrategyVersion"
            )
        }
    }

    @JvmStatic
    private fun parseSearchResult(
        cursor: Cursor,
        keyWord: String,
        currentPage: Int,
        pageSize: Int
    ): SearchResultWrapper {
        val searchResultWrapper = SearchResultWrapper()
        val resultCount = cursor.count

        cursor.extras?.let {
            //获取初始索引的状态：-1：未建立 0：建立成功（建立成功后会发送广播） 1：建立失败 2：已创建建立索引的任务，但还未执行 3：正在建立
            var indexStatus = it.getInt("indexStatus", -2)
            var dmpDocCount = it.getInt("docCount", 0)
            DebugUtil.i(
                TAG, "doSearch result count $resultCount ,code is ${it.getInt("code", -1)}" +
                        // docCount:中子数据库已入库录音文件总数量,用于后续搜索问题排查
                        ", docCount $dmpDocCount  indexStatus is $indexStatus" +
                        ", statistic ${it.getString("statistic")}"
            )
            // 中子已建立录音索引文件数量为0，服务降级走录音自有搜索
            if ((resultCount <= 0) && (dmpDocCount <= 0)) {
                DebugUtil.i(TAG, "searchResult and dmpDocCount both is 0")
                throw Exception("dmp index-data-size is zero")
            }

            /*return "[{"totalNum":99}]"*/
            it.getString("statistic")?.let {
                if (it.isNotEmpty()) {
                    val jsonArr = JSONArray(it)
                    /*返回匹配结果总数量*/
                    searchResultWrapper.totalCount = jsonArr.optJSONObject(0)?.optInt("totalNum") ?: -1
                    /*val pageNum = cursor.getExtras().get("pageNum")*/
                    val maxPageCount = ceil(1.0 * searchResultWrapper.totalCount / pageSize)
                    searchResultWrapper.nextPageNo =
                        if (currentPage < maxPageCount) (currentPage + 1) else null
                }
            }
        }
        // 搜索结果为0
        if (resultCount <= 0) {
            return searchResultWrapper
        }
        return searchResult(cursor, searchResultWrapper, keyWord)
    }

    @JvmStatic
    private fun searchResult(
        cursor: Cursor,
        searchResultWrapper: SearchResultWrapper,
        keyWord: String
    ): SearchResultWrapper {
        var singleData: ItemSearchViewModel? = null
        while (cursor.moveToNext()) {
            var mediaId = 0L
            try {
                mediaId =
                    cursor.getLong(cursor.getColumnIndexOrThrow(CenterDbConstant.IndexProvider.COLUMN_NAME_MEDIA_ID))
            } catch (e: IllegalArgumentException) {
                DebugUtil.e(TAG, "exception when get mediaId", e)
            }
            //如果录音已删除，在中子待删除同步列表中，则不展示
            val isBeingDelete = mDeleteMediaIdsList.contains(mediaId)
            DebugUtil.d(TAG, "searchResult, isBeingDelete:$isBeingDelete")
            if (mediaId == null || mediaId == 0L || isBeingDelete) {
                searchResultWrapper.totalCount -= 1
                continue
            }
            singleData = ItemSearchViewModel().apply {
                searchValue = keyWord
                isFromCenter = true
                this.mediaId = mediaId
                convertTextPath =
                    cursor.getStringOrNull(cursor.getColumnIndex(CenterDbConstant.IndexProvider.COLUMN_NAME_CONVERT_PATH))
                convertCompleted = convertTextPath.isNullOrBlank().not()
                data =
                    cursor.getStringOrNull(cursor.getColumnIndex(CenterDbConstant.IndexProvider.COLUMN_NAME_MEDIA_PATH))
                relativePath = CenterDbUtils.getRelativePathFromData(data)
                displayName = getDisplayNameAndTitle(cursor, this).displayName
                title = getDisplayNameAndTitle(cursor, this).title
                dateModified = getModifiedAndDuration(cursor, this).dateModified
                mDuration = getModifiedAndDuration(cursor, this).mDuration
                // parse extend info
                extend = getExtendAndMimeType(cursor, this).extend
                mimeType = getExtendAndMimeType(cursor, this).mimeType
                // parse keywords highlight postion
                titleColorIndex = getTitleColorIndexEtc(cursor, this).titleColorIndex
                contentColorIndex = getTitleColorIndexEtc(cursor, this).contentColorIndex
                convertCompleted = getTitleColorIndexEtc(cursor, this).convertCompleted
                content = getTitleColorIndexEtc(cursor, this).content
            }
            searchResultWrapper.pageData.add(singleData)
        }
        return searchResultWrapper
    }

    @JvmStatic
    private fun getTitleColorIndexEtc(
        cursor: Cursor,
        singleData: ItemSearchViewModel
    ): ItemSearchViewModel {
        try {
            val highlightInfos =
                cursor.getString(cursor.getColumnIndexOrThrow(CenterDbConstant.SearchProvider.COLUMN_NAME_SEARCH_RESULT_HIGHLIGHT))
            GsonUtil.fromJson<SearchHighLightBean>(
                highlightInfos,
                SearchHighLightBean::class.java
            )?.apply {
                display_name?.let {
                    it.position?.split(" ")?.forEach { singleData.titleColorIndex.add(it.toInt()) }
                }
                highlight?.let {
                    singleData.convertCompleted = true
                    it.position?.split(" ")
                        ?.forEach { singleData.contentColorIndex.add(it.toInt()) }
                    singleData.content = it.content
                }
            }
        } catch (e: IllegalArgumentException) {
            DebugUtil.e(TAG, "exception when get highlightInfos: ", e)
        }
        return singleData
    }

    @JvmStatic
    private fun getModifiedAndDuration(
        cursor: Cursor,
        singleData: ItemSearchViewModel
    ): ItemSearchViewModel {
        try {
            singleData.dateModified =
                cursor.getLong(cursor.getColumnIndexOrThrow(CenterDbConstant.IndexProvider.COLUMN_NAME_DATE_MODIFIED))
            singleData.mDuration =
                cursor.getLong(cursor.getColumnIndexOrThrow(CenterDbConstant.IndexProvider.COLUMN_NAME_DURATION))
        } catch (e: IllegalArgumentException) {
            DebugUtil.e(TAG, "exception when get dateModified or mDuration: ", e)
        }
        return singleData
    }

    @JvmStatic
    private fun getExtendAndMimeType(
        cursor: Cursor,
        singleData: ItemSearchViewModel
    ): ItemSearchViewModel {
        try {
            singleData.extend =
                GsonUtil.fromJson(
                    cursor.getStringOrNull(cursor.getColumnIndex(CenterDbConstant.IndexProvider.COLUMN_NAME_EXTEND)),
                    SearchInsertExtendBean::class.java
                )
            singleData.extend?.let {
                singleData.mimeType = it.mimeType
            }
        } catch (e: Exception) {
            DebugUtil.i(TAG, "parse extend to bean error $e")
        }
        return singleData
    }

    @JvmStatic
    private fun getDisplayNameAndTitle(
        cursor: Cursor,
        singleData: ItemSearchViewModel
    ): ItemSearchViewModel {
        singleData.displayName =
            cursor.getStringOrNull(cursor.getColumnIndex(CenterDbConstant.IndexProvider.COLUMN_NAME_DISPLAY_NAME))
        singleData.displayName?.let {
            // 兼容老的索引数据是带后缀的，显示的时候去掉后缀
            singleData.title = if (singleData.data?.endsWith(it) == true) {
                singleData.displayName.title()
            } else {
                singleData.displayName
            }
        }
        return singleData
    }

    @JvmStatic
    private fun getRealSupportFilter(supportFilter: String?): String? {
        val filter = supportFilter?.toIntOrNull() ?: RecordModeConstant.BUCKET_VALUE_ALL
        val realFilter = CursorHelper.getAllSupportRecordForFilter(
            BaseApplication.getAppContext(), filter
        )
        if (!realFilter.isNullOrEmpty()) {
            return realFilter.joinToString(",")
        }
        return null
    }

    @JvmStatic
    private fun getCompleteFlag(): Map<Long, Int> {
        if (FeatureOption.OPLUS_VERSION_EXP) {
            return emptyMap()
        }
        val convertVads = ConvertDbUtil.selectAll()
        val completes = HashMap<Long, Int>()
        for (record in convertVads) {
            var id = record.recordId
            if (id == -1L) {
                id = MediaDBUtils.queryIdByData(record.mediaPath)
                ConvertDbUtil.updateRecordIdByMediaPath(record.mediaPath, id)
            }
            completes[id] = record.completeStatus
        }
        return completes
    }

    @JvmStatic
    private fun parseResultCodeIsSuccess(bundle: Bundle?): Boolean {
        bundle?.apply {
            DebugUtil.i(TAG, "parse result code: ${this.getInt("code", -1)}")
            when (this.getInt("code", -1)) {
                CenterDbConstant.ResultCode.CODE_SUCCESS -> return true
                CenterDbConstant.ResultCode.CODE_ERROR_SLEEP -> return false
                else -> return false
            }
        }
        return true
    }
}