/************************************************************
 * Copyright 2000-2021 OPlus Mobile Comm Corp., Ltd.
 * All rights reserved.
 * FileName      : BrowseFragment.kt
 * Version Number: 1.0
 * Description   :
 * Author        : tianjun
 * Date          : 2021.06.04
 * History       :(ID,  2021.06.04, tianjun, Description)
 */
package com.soundrecorder.browsefile.home

import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.annotation.SuppressLint
import android.app.Activity.RESULT_OK
import android.content.Intent
import android.content.res.Configuration
import android.database.ContentObserver
import android.graphics.Color
import android.graphics.Rect
import android.media.AudioManager
import android.net.Uri
import android.os.Bundle
import android.os.FileObserver
import android.os.Handler
import android.os.Looper
import android.provider.MediaStore
import android.text.Editable
import android.text.TextWatcher
import android.view.Gravity
import android.view.KeyEvent
import android.view.Menu
import android.view.MenuItem
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.view.ViewGroup.MarginLayoutParams
import android.view.accessibility.AccessibilityEvent
import android.view.animation.AlphaAnimation
import android.view.animation.Animation
import android.view.animation.PathInterpolator
import android.view.inputmethod.EditorInfo
import android.widget.AdapterView
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.PopupWindow
import android.widget.ScrollView
import android.widget.TextView
import androidx.annotation.StringRes
import androidx.appcompat.app.AlertDialog
import androidx.coordinatorlayout.widget.CoordinatorLayout
import androidx.core.animation.addListener
import androidx.core.content.ContextCompat
import androidx.core.content.res.ResourcesCompat
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.children
import androidx.core.view.doOnLayout
import androidx.core.view.forEach
import androidx.core.view.isInvisible
import androidx.core.view.isVisible
import androidx.core.view.size
import androidx.core.view.updateLayoutParams
import androidx.core.view.updatePadding
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import androidx.lifecycle.viewModelScope
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import androidx.recyclerview.widget.COUIRecyclerView
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.RecyclerView.AdapterDataObserver
import com.coui.appcompat.bottomnavigation.COUINavigationView
import com.coui.appcompat.dialog.COUIAlertDialogBuilder
import com.coui.appcompat.hapticfeedback.COUIHapticFeedbackConstants
import com.coui.appcompat.material.navigation.NavigationBarView
import com.coui.appcompat.poplist.COUIPopupListWindow
import com.coui.appcompat.searchview.COUISearchBar
import com.coui.appcompat.searchview.COUISearchViewAnimate
import com.coui.appcompat.statusbar.COUIStatusBarResponseUtil
import com.coui.responsiveui.config.ResponsiveUIConfig
import com.coui.responsiveui.config.UIConfig
import com.coui.uikit.demo.navigationview.BottomMarginView
import com.google.gson.Gson
import com.soundrecorder.base.BaseActivity
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.StorageManager
import com.soundrecorder.base.backpressed.OnBackPressedListener
import com.soundrecorder.base.ext.dismissWhenShowing
import com.soundrecorder.base.ext.findFragment
import com.soundrecorder.base.ext.getStringExtraSecure
import com.soundrecorder.base.ext.isFlexibleWindow
import com.soundrecorder.base.ext.isInMultiWindowMode
import com.soundrecorder.base.ext.postValueSafe
import com.soundrecorder.base.ext.removeFragment
import com.soundrecorder.base.ext.replaceFragmentByTag
import com.soundrecorder.base.ext.restoreRingMode
import com.soundrecorder.base.ext.title
import com.soundrecorder.base.refresh.BounceCallBack
import com.soundrecorder.base.refresh.BounceHandler
import com.soundrecorder.base.refresh.DefaultHeader
import com.soundrecorder.base.refresh.EventForwardingHelper
import com.soundrecorder.base.splitwindow.BaseFragment
import com.soundrecorder.base.splitwindow.ISplitWindChangeListener
import com.soundrecorder.base.splitwindow.SplitWindowUtil
import com.soundrecorder.base.splitwindow.WindowLayoutChangeListener
import com.soundrecorder.base.utils.BaseUtil
import com.soundrecorder.base.utils.ClickUtils
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.DensityUtil.px2dp
import com.soundrecorder.base.utils.FeatureOption
import com.soundrecorder.base.utils.MediaDataScanner
import com.soundrecorder.base.utils.NetworkUtils
import com.soundrecorder.base.utils.NumberConstant.NUMBER_L300
import com.soundrecorder.base.utils.NumberConstant.NUM_F0_0
import com.soundrecorder.base.utils.NumberConstant.NUM_F0_1
import com.soundrecorder.base.utils.NumberConstant.NUM_F0_3
import com.soundrecorder.base.utils.NumberConstant.NUM_F1_0
import com.soundrecorder.base.utils.OS12FeatureUtil
import com.soundrecorder.base.utils.OSDKCompatUtils
import com.soundrecorder.base.utils.PrefUtil
import com.soundrecorder.base.utils.PrefUtil.KEY_RECORD_CALL_GROUP_SORT
import com.soundrecorder.base.utils.PrefUtil.KEY_RECORD_GROUP_ID
import com.soundrecorder.base.utils.PrefUtil.KEY_RECORD_GROUP_UUID
import com.soundrecorder.base.utils.ScreenUtil
import com.soundrecorder.base.utils.ToastManager
import com.soundrecorder.base.utils.WindowType
import com.soundrecorder.base.view.DeDuplicateInsetsCallback
import com.soundrecorder.browsefile.BrowseFile
import com.soundrecorder.browsefile.R
import com.soundrecorder.browsefile.databinding.FragmentBrowseFileBinding
import com.soundrecorder.browsefile.home.cloudtips.CloudOperationResponseData
import com.soundrecorder.browsefile.home.dialog.navigation.NavigationViewManager
import com.soundrecorder.browsefile.home.item.BrowseAdapter
import com.soundrecorder.browsefile.home.item.BrowseAdapter.Companion.TYPE_HEADER_CLOUD
import com.soundrecorder.browsefile.home.item.BrowseAdapter.Companion.TYPE_HEADER_CLOUD_CONFIG
import com.soundrecorder.browsefile.home.item.BrowseAdapter.Companion.TYPE_HEADER_QUESTION
import com.soundrecorder.browsefile.home.item.BrowseAdapter.Companion.TYPE_HEADER_RECYCLE
import com.soundrecorder.browsefile.home.item.FastPlayHelper
import com.soundrecorder.browsefile.home.item.GroupHorizonLabelAdapter
import com.soundrecorder.browsefile.home.item.IBrowseViewHolderListener
import com.soundrecorder.browsefile.home.item.ItemBrowseRecordViewModel
import com.soundrecorder.browsefile.home.load.BrowseViewModel
import com.soundrecorder.browsefile.home.load.ConvertingInfo
import com.soundrecorder.browsefile.home.load.ViewStatus
import com.soundrecorder.browsefile.home.view.QuestionnaireGuideTipView
import com.soundrecorder.browsefile.home.view.behavior.PrimaryTitleBehavior
import com.soundrecorder.browsefile.home.view.cloudtip.CloudConfigTipView
import com.soundrecorder.browsefile.home.view.cloudtip.CloudGuideTipView
import com.soundrecorder.browsefile.home.view.cloudtip.TipStatusGuideObserver
import com.soundrecorder.browsefile.home.view.cloudtip.TipStatusObserver
import com.soundrecorder.browsefile.home.view.group.util.GroupViewModel
import com.soundrecorder.browsefile.home.view.group.view.GroupChooseFragment
import com.soundrecorder.browsefile.home.view.group.view.GroupFragment
import com.soundrecorder.browsefile.home.view.recycle.RecycleHeaderView
import com.soundrecorder.browsefile.parentchild.BrowseFileActivityViewModel
import com.soundrecorder.browsefile.parentchild.listener.IBrowseFileActivityListener
import com.soundrecorder.browsefile.search.SearchCacheHolder
import com.soundrecorder.browsefile.search.SearchFragment
import com.soundrecorder.browsefile.search.load.center.filechange.CenterFileChangeObserver
import com.soundrecorder.common.base.PrivacyPolicyBaseActivity
import com.soundrecorder.common.buryingpoint.BuryingPoint
import com.soundrecorder.common.buryingpoint.BuryingPoint.addCallGroupingByContact
import com.soundrecorder.common.buryingpoint.BuryingPoint.addClickGroup
import com.soundrecorder.common.buryingpoint.CloudStaticsUtil
import com.soundrecorder.common.buryingpoint.GroupStateBuryingPointManager
import com.soundrecorder.common.buryingpoint.RecorderUserAction
import com.soundrecorder.common.buryingpoint.SummaryStaticUtil
import com.soundrecorder.common.constant.Constants
import com.soundrecorder.common.constant.RecordConstant.CALL_RECORD_CONTACT_GROUPING
import com.soundrecorder.common.constant.RecordConstant.CALL_RECORD_NOT_GROUPING
import com.soundrecorder.common.databean.GroupInfo
import com.soundrecorder.common.databean.GroupInfo.Companion.INT_DEFAULT_ALL
import com.soundrecorder.common.databean.GroupInfo.Companion.INT_DEFAULT_CALLING
import com.soundrecorder.common.databean.GroupInfo.Companion.INT_DEFAULT_NONE
import com.soundrecorder.common.databean.GroupInfo.Companion.INT_DEFAULT_RECENTLY_DELETED
import com.soundrecorder.common.databean.Record
import com.soundrecorder.common.databean.StartPlayModel
import com.soundrecorder.common.db.GroupInfoDbUtil
import com.soundrecorder.common.db.GroupInfoDbUtil.genDefaultCallGroupInfo
import com.soundrecorder.common.db.MediaDBUtils
import com.soundrecorder.common.db.RecorderDBUtil
import com.soundrecorder.common.dialog.LoadingDialog
import com.soundrecorder.common.fileobserve.OnFileEventListener
import com.soundrecorder.common.fileoperator.CheckOperate
import com.soundrecorder.common.fileoperator.delete.DeleteFileDialog
import com.soundrecorder.common.fileoperator.delete.DeleteFileDialogUtil
import com.soundrecorder.common.fileoperator.recover.RecoverFileDialogUtil
import com.soundrecorder.common.flexible.FollowDialogRestoreUtils
import com.soundrecorder.common.flexible.FollowRestoreCallBack
import com.soundrecorder.common.permission.PermissionActivity.Companion.showRequestNotificationPermissionSnackBar
import com.soundrecorder.common.permission.PermissionDialogUtils
import com.soundrecorder.common.permission.PermissionUtils
import com.soundrecorder.common.permission.PermissionUtils.SHOULD_SHOW_USER_NOTICE
import com.soundrecorder.common.permission.PermissionUtils.isNetWorkGranted
import com.soundrecorder.common.share.ShareTypeLink
import com.soundrecorder.common.share.ShareTypeNote
import com.soundrecorder.common.sync.RecordDataSyncHelper
import com.soundrecorder.common.task.ActivityTaskUtils
import com.soundrecorder.common.task.RecordRouterManager
import com.soundrecorder.common.utils.CoroutineUtils
import com.soundrecorder.common.utils.FileDealUtil
import com.soundrecorder.common.utils.FunctionOption
import com.soundrecorder.common.utils.LandScapeUtil
import com.soundrecorder.common.utils.RecordFileChangeNotify
import com.soundrecorder.common.utils.RecordModeUtil
import com.soundrecorder.common.utils.RecordModeUtil.isFromCall
import com.soundrecorder.common.utils.RecordModeUtil.isFromOther
import com.soundrecorder.common.utils.RecordModeUtil.isThreeRecordJumpToCall
import com.soundrecorder.common.utils.SendSetUtil
import com.soundrecorder.common.utils.TipUtil
import com.soundrecorder.common.utils.VibrateUtils
import com.soundrecorder.common.utils.ViewUtils
import com.soundrecorder.common.utils.ViewUtils.getUnDisplayViewHeight
import com.soundrecorder.common.utils.ViewUtils.onRelease
import com.soundrecorder.common.utils.click
import com.soundrecorder.common.utils.taskbar.TaskBarUtil
import com.soundrecorder.common.utils.visible
import com.soundrecorder.common.widget.OSImageView
import com.soundrecorder.common.widget.TransitionUtils
import com.soundrecorder.modulerouter.FeedBackInterface
import com.soundrecorder.modulerouter.SettingInterface
import com.soundrecorder.modulerouter.cloudkit.CloudKitInterface
import com.soundrecorder.modulerouter.cloudkit.CloudSwitchState
import com.soundrecorder.modulerouter.cloudkit.CloudTipManagerAction
import com.soundrecorder.modulerouter.cloudkit.ICloudGlobalStateCallBack
import com.soundrecorder.modulerouter.cloudkit.SYNC_TYPE_RECOVERY_START_APP
import com.soundrecorder.modulerouter.cloudkit.VerifyCallBack
import com.soundrecorder.modulerouter.convertService.ConvertSupportAction
import com.soundrecorder.modulerouter.playback.KEY_NOTIFY_CONVERT_STATUS
import com.soundrecorder.modulerouter.playback.KEY_NOTIFY_RECORD_ID
import com.soundrecorder.modulerouter.playback.KEY_NOTIFY_SMART_NAME_CONVERT_COMPLETE
import com.soundrecorder.modulerouter.playback.KEY_NOTIFY_SMART_NAME_NAME_TEXT
import com.soundrecorder.modulerouter.playback.KEY_NOTIFY_SMART_NAME_STATUS
import com.soundrecorder.modulerouter.playback.NOTIFY_CONVERT_STATUS_UPDATE
import com.soundrecorder.modulerouter.playback.NOTIFY_SMART_NAME_STATUS_UPDATE
import com.soundrecorder.modulerouter.playback.PlayBackInterface
import com.soundrecorder.modulerouter.privacyPolicy.PrivacyPolicyConstant
import com.soundrecorder.modulerouter.questionnaire.ACTION_CARD_IGNORED
import com.soundrecorder.modulerouter.questionnaire.ACTION_CARD_SHOWN
import com.soundrecorder.modulerouter.questionnaire.ACTION_DATA_VALID
import com.soundrecorder.modulerouter.questionnaire.ACTION_NO_DATA
import com.soundrecorder.modulerouter.questionnaire.ACTION_SURVEY_SUBMITTED
import com.soundrecorder.modulerouter.questionnaire.QuestionCDPCallback
import com.soundrecorder.modulerouter.recorder.RecorderDataConstant
import com.soundrecorder.modulerouter.recorder.RecorderServiceInterface
import com.soundrecorder.modulerouter.share.ShareAction
import com.soundrecorder.modulerouter.smartname.ISmartNameManager
import com.soundrecorder.modulerouter.smartname.SmartNameAction
import com.soundrecorder.modulerouter.summary.ACTION_SUMMARY_STATE_CHANGED
import com.soundrecorder.modulerouter.summary.BUNDLE_CALL_ID
import com.soundrecorder.modulerouter.summary.BUNDLE_FROM_WHERE
import com.soundrecorder.modulerouter.summary.BUNDLE_MEDIA_ID
import com.soundrecorder.modulerouter.summary.BUNDLE_NOTE_ID
import com.soundrecorder.modulerouter.summary.SummaryInterface
import com.soundrecorder.modulerouter.translate.AIAsrManagerAction
import com.soundrecorder.modulerouter.utils.Injector
import com.soundrecorder.player.speaker.SpeakerModeController
import com.soundrecorder.player.speaker.SpeakerModeController.Companion.PLAYER_COMMAND_PAUSE
import com.soundrecorder.player.speaker.SpeakerReceiver
import com.soundrecorder.player.speaker.SpeakerStateManager
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.lang.ref.WeakReference
import kotlin.collections.set

class BrowseFragment : BaseFragment<FragmentBrowseFileBinding>(),
    View.OnClickListener,
    COUIStatusBarResponseUtil.StatusBarClickListener,
    COUISearchViewAnimate.OnCancelButtonClickListener,
    TextWatcher,
    OnBackPressedListener,
    ReceiverUtils.IReceiverOnReceive,
    TextView.OnEditorActionListener {

    companion object {
        const val REQUEST_CODE_SYS_RENAME_AUTH = 111
        const val REQUEST_CODE_SYS_DELETE_AUTH = 112
        const val REQUEST_CODE_START_NOTE = 113
        const val REQUEST_CODE_SYS_RECOVER_AUTH = 114
        const val REQUEST_CODE_SYS_DELETE_ALL_AUTH = 115

        private const val TAG = "BrowseFragment"
        private const val DELAY_TIME: Long = 200
        private const val DELAY_TIME_100 = 100L
        private const val DELAY_TIME_300 = 300L
        private const val DELAY_WINDOW_STATE_CHANGED_TIME = 1000L
        private const val MENU_PRESS_DELAY_TIME: Long = 150
        private const val CLOUD_SHOW_STATE = 1
        private const val QUESTION_SHOW_STATE = 2

        //0  大于450的状态  非搜索
        private const val SET_BEHAVIOR_STATUS_CODE_0 = 0

        //1  小于450的状态(横屏)   非搜索
        private const val SET_BEHAVIOR_STATUS_CODE_1 = 1

        //2  大于450的状态  搜索
        private const val SET_BEHAVIOR_STATUS_CODE_2 = 2

        //3  小于450的状态(横屏)  搜索
        private const val SET_BEHAVIOR_STATUS_CODE_3 = 3

        private const val ONE_DAY_TIME_MILLIS = 24 * 60 * 60 * 1000L

        private const val NAV_ANIM_DURATION = 250L
        private const val NAV_ANIM_PROPERTY_NAME = "bottomMargin"
        private const val TOOL_NAV_DISMISS_ANIM_DURATION = 230L
        private const val TOOL_NAV_SHOW_ANIM_DURATION = 300L
        private const val SCREEN_TYPE_LARGE = 840
        private const val SCREEN_TYPE_MIDDLE = 600
        private val toolNavDismissAnimInterpolator = PathInterpolator(0.33f, 0f, 0.83f, 0.83f)
        private val toolNaShowAnimInterpolator = PathInterpolator(0.17f, 0.17f, 0.67f, 1f)

        private const val MENU_SIZE_NUM_1 = 1
        private const val MENU_SIZE_NUM_2 = 2
        private const val MENU_SIZE_NUM_5 = 5
        private const val FLAG_COUNT = 5

    }

    private var mOptionsMenu: Menu? = null
    private var mBehavior: PrimaryTitleBehavior? = null
    private var mLimitDialog: AlertDialog? = null

    private var mNavigationViewManager: NavigationViewManager? = null
    private var mNavigationLayout: View? = null
    private var mBottomNavigationView: COUINavigationView? = null
    private var mReceiverUtils: ReceiverUtils? = null

    //loading动画
    private var mLoadingViewLayout: View? = null
    private var mLoadingView: View? = null

    //无权限动画
    private var mPermissionScrollview: ScrollView? = null
    private var mBtnPermissionOperate: TextView? = null

    //空页面动画
    private var mEmptyScrollview: ScrollView? = null
    private var mEmptyOSImageView: OSImageView? = null
    private var mTvbottle: TextView? = null
    private var mTvRecentlyBottle: TextView? = null

    private var mSpeakerMenuItem: MenuItem? = null

    private var mStatusBarResponse: COUIStatusBarResponseUtil? = null

    private lateinit var mAdapter: BrowseAdapter
    private lateinit var mBrowseViewModel: BrowseViewModel
    private var questionGuideTipView: QuestionnaireGuideTipView? = null
    private val mBrowseFileActivityViewModel: BrowseFileActivityViewModel by activityViewModels()
    private val mGroupViewModel: GroupViewModel by activityViewModels()
    private val mCacheHolder = SearchCacheHolder()
    private var mSearchFragment: SearchFragment? = null
    private var taskId: Int = 0
    var isDeviceSecureDelete = false
    private var mQuestionShowState = -1

    private var recordAnimationSet: AnimatorSet? = null
    private var mLastCurrentSplitWindowParameter: ISplitWindChangeListener.SplitWindowParameter? = null

    private var mTipStatusObserver: TipStatusObserver? = null
    private var mIsNoticeDialogShowing = true
    private var nagivationHeight: Int = 0
    private var itemDragDelegate: RecordItemDragDelegate? = null
    private var dragViewReference: WeakReference<View>? = null
    var clickedItemSummaryNoteId: String? = null
    private var dragJob: Job? = null
    private var disableDialog: AlertDialog? = null

    private var mCloudGuideTipView: CloudGuideTipView? = null
    private var mIsInitCloudState = false
    private var mRecycleHeaderView: RecycleHeaderView? = null

    private var mDeleteDialog: DeleteFileDialog? = null
    private var needClearPlayData: Boolean = false

    private var mNavigationAnim: ObjectAnimator? = null
    private var mBottomMarginView: BottomMarginView? = null
    private var mMusicMediaStoreContentObserver: ContentObserver? = null
    private var mToolbarOverflowPopupWindow: COUIPopupListWindow? = null
    private var mSubMenuCheckedPosition = 0
    private var mOnDismissListener: PopupWindow.OnDismissListener? = null

    private var mCurrentGroupUuId: String? = null
    private var mCurrentGroup: GroupInfo? = null

    private var mSmartNameMangerImpl: ISmartNameManager? = null
    private var mSupportSmartName: Boolean = false
    private var isOpenSwitch = false

    private var mFilePermissionDialog: AlertDialog? = null

    private val refreshRunnable = {
        DebugUtil.i(logTag, "call refresh, from refreshRunnable", true)
        mBrowseViewModel.refresh()
    }
    private val smoothToTopRunnable = Runnable {
        mBinding.mListView.smoothScrollToPosition(0)
    }

    //分享txt，转文本文件超过50M的时候，显示“请稍后...”dialog
    private var shareWaitingDialog: LoadingDialog? = null

    private val cloudKitApi by lazy {
        Injector.injectFactory<CloudKitInterface>()
    }

    private val cloudTipManager by lazy {
        Injector.injectFactory<CloudTipManagerAction>()
    }

    private val settingApi by lazy {
        Injector.injectFactory<SettingInterface>()
    }

    private val feedBackApi by lazy {
        Injector.injectFactory<FeedBackInterface>()
    }

    private val recorderViewModelApi by lazy {
        Injector.injectFactory<RecorderServiceInterface>()
    }

    private val summaryApi by lazy {
        Injector.injectFactory<SummaryInterface>()
    }

    private val playbackApi by lazy {
        Injector.injectFactory<PlayBackInterface>()
    }

    private val smartNameAction by lazy {
        Injector.injectFactory<SmartNameAction>()
    }

    private val aiAsrManagerAction by lazy {
        Injector.injectFactory<AIAsrManagerAction>()
    }

    private val convertSupportAction by lazy {
        Injector.injectFactory<ConvertSupportAction>()
    }

    private val recordOptionCompletedListener =
        object : NavigationViewManager.OnOptionCompletedListener {
            override fun onOptionCompleted(
                option: Int,
                selectedMediaIdList: ArrayList<String>?,
                deleteRecordList: ArrayList<Record>?,
                isDeleteAll: Boolean
            ): Long {
                DebugUtil.i(logTag, "onOptionCompleted > $option")
                var delayTime = 0L
                if (recordFilterIsRecycle()) {
                    mBrowseFileActivityViewModel.isDialogDeleteAll.postValueSafe(isDeleteAll)
                }
                mBrowseViewModel.exitEditMode()
                if (option == CheckOperate.OPERATE_DELETE || option == CheckOperate.OPERATE_RECOVER) {
                    resetRecycleDialogShowing()
                    mBrowseViewModel.clearSmartNameCacheIds(selectedMediaIdList)
                    if (mAdapter.isContentNotEmpty()) {
                        if (deleteRecordList?.size == mBrowseViewModel.getShowRecordCount()) {
                            startAlphaAnimation()
                        } else {
                            if (isGroupingByContact()) {
                                delayTime = mAdapter.deleteGroupReocrdItems(selectedMediaIdList, refreshRunnable) ?: 0L
                            } else {
                                delayTime = mAdapter.deleteItems(selectedMediaIdList, refreshRunnable)
                            }
                        }
                    } else {
                        refreshRunnable.invoke()
                    }
                    activity?.let {
                        VibrateUtils.vibrate(it)
                    }
                    whetherClearCurrentPlayRecord(deleteRecordList)
                } else {
                    refreshRunnable.invoke()
                }
                isDeviceSecureDelete = false
                DebugUtil.i(logTag, "delayTime > $delayTime")
                return delayTime
            }

            override fun onOptionSmartName(option: Int, selectedMediaIdList: MutableList<Long>?) {
                if (selectedMediaIdList.isNullOrEmpty()) {
                    return
                }
                DebugUtil.i(logTag, "onOptionSmartName > $option")
                mBrowseViewModel.exitEditMode()
                mNavigationViewManager?.resetContinueOperator()
                if (!PermissionUtils.hasAllFilePermission()) {
                    DebugUtil.d(TAG, "startConvertAndSmartName, !hasAllFilePermission")
                    showPermissionAllFileDialog(true, selectedMediaIdList)
                    return
                }
                isOpenSwitch = false
                startConvertAndSmartName(selectedMediaIdList, false)
            }
        }

    private val onMoveGroupListener =
        object : NavigationViewManager.OnMoveGroupListener {
            override fun onMoveGroup(selectedRecordList: ArrayList<Record>) {
                mGroupViewModel.mutableSelectRecordings.value = selectedRecordList
                showGroupChooseFragment()
                mGroupViewModel.onMoveGroupCallBack.value =
                    object : GroupChooseFragment.OnMoveGroupCallBack {
                        override fun onPreMoveGroup() {
                            mNavigationViewManager?.showMovingGroupProgress()
                        }

                        override fun onMovedGroup(showWaitingDialog: Boolean) {
                            CoroutineUtils.doInMain {
                                mBrowseViewModel.exitEditMode()
                                //退出编辑模式，状态重置，否则可能导致新增记录列表无法刷新
                                mNavigationViewManager?.resetContinueOperator()
                                if (showWaitingDialog) {
                                    DebugUtil.d(
                                        TAG,
                                        "Force refresh data after moving group for records not exist in db"
                                    )
                                    refreshData()
                                    mNavigationViewManager?.dismissMovingGroupProcess()
                                }
                            }
                        }
                    }
            }
        }

    /**
     * 如果删除或恢复时当前录音正在播放，清除当前播放录音
     */
    private fun whetherClearCurrentPlayRecord(deleteRecordList: ArrayList<Record>?) {
        if (deleteRecordList?.isNotEmpty() == true
            && mBrowseFileActivityViewModel.mCurrentPlayRecordData.value != null
        ) {
            deleteRecordList.forEach { record ->
                if (isCurrentPlayRecord(record)) {
                    mBrowseFileActivityViewModel.clearPlayRecordData()
                    return
                }
            }
        }
    }

    private fun isCurrentPlayRecord(record: Record): Boolean {
        return if (recordFilterIsRecycle()) {
            mBrowseFileActivityViewModel.mCurrentPlayRecordData.value?.playPath == record.recycleFilePath
        } else {
            mBrowseFileActivityViewModel.mCurrentPlayRecordData.value?.mediaId == record.id
        }
    }

    private fun startAlphaAnimation() {
        val alphaAnimation = AlphaAnimation(NUM_F1_0, NUM_F0_0).apply {
            duration = NUMBER_L300
            interpolator = PathInterpolator(NUM_F0_3, NUM_F0_0, NUM_F0_1, NUM_F1_0)
            setAnimationListener(object : Animation.AnimationListener {
                override fun onAnimationStart(animation: Animation?) {}

                override fun onAnimationEnd(animation: Animation?) {
                    mBinding.mListView.isVisible = false
                    refreshRunnable.invoke()
                }

                override fun onAnimationRepeat(animation: Animation?) {}
            })
        }
        mBinding.mListView.startAnimation(alphaAnimation)
    }

    override fun layoutId(): Int = R.layout.fragment_browse_file

    override var logTag: String = "BrowseFragment"

    /**
     * 列表页面权限获取成功
     * @param isFirst 是否是清除数据后首次启动有权限回调
     */
    fun onPermissionGranted(isFirst: Boolean) {
        if (!isFirst) {
            return
        }
        if (!isDeviceSecureDelete) {
            // 刷新数据，便于清除数据后处理完用户须知弹窗后获取问卷数据
            refreshGroups()
        }
        initCloudState()

        showTipsSame()
    }

    /**
     * 重新启动后，更新信息
     */
    fun onNewIntent() {
        checkRecordGroupView(true)
    }

    private fun checkShowCloudCard(): Boolean {
        context?.let { context ->
            val headerEmpty = !mAdapter.isHeaderNotEmpty()
            val needShowGloudGuide = cloudTipManager?.isNeedShowCloudGuide() ?: false
            val isMainSystemUser = BaseApplication.sIsMainSystem
            val showCloudCard = headerEmpty && needShowGloudGuide && isMainSystemUser
            mCloudGuideTipView = mAdapter.getHeadeView(TYPE_HEADER_CLOUD) as? CloudGuideTipView
            if (showCloudCard && mCloudGuideTipView == null) {
                mCloudGuideTipView = CloudGuideTipView(context)
                mCloudGuideTipView?.let {
                    it.bindAdapter(mAdapter)
                    setRefreshEnable(false)
                    mAdapter.setHeader(TYPE_HEADER_CLOUD, it)
                    mQuestionShowState = CLOUD_SHOW_STATE
                    CloudStaticsUtil.addCloudTipsCardPopEvent()
                    it.setOnCloudGuideTipListener(object : CloudGuideTipView.OnCloudGuideTipListener {
                        override fun onCloudTipOpen(view: View) {
                            if (!BaseUtil.isEXP() && cloudKitApi?.isStatementCloudGranted(context) == false) {
                                (activity as? PrivacyPolicyBaseActivity)?.getPrivacyPolicyDelegate()
                                    ?.resumeShowDialog(
                                        PrivacyPolicyConstant.TYPE_PERMISSION_CLOUD, true,
                                        PrivacyPolicyConstant.PAGE_FROM_BROWSE
                                    )
                            } else {
                                switchCloudOn()
                            }
                        }
                    })
                }
            } else if (!needShowGloudGuide && mCloudGuideTipView != null) {
                mAdapter.setHeader(TYPE_HEADER_CLOUD, null)
                mCloudGuideTipView?.release()
                mCloudGuideTipView = null
            }

            DebugUtil.i(
                TAG,
                "showCloudCard headerEmpty $headerEmpty, " +
                        "needShowGloudGuide $needShowGloudGuide, " +
                        "isMainSystem $isMainSystemUser, " +
                        "showCloudCard $showCloudCard"
            )
            return showCloudCard
        }
        return false
    }

    /**
     * 已授权云同步权限，则打开云同步
     */
    private fun switchCloudOn() {
        val requireContext = context ?: return

        if (NetworkUtils.isNetworkInvalid(requireContext.applicationContext)) {
            ToastManager.showShortToast(context, com.soundrecorder.common.R.string.network_disconnect)
            return
        }

        if (cloudKitApi?.hasCloudRequiredPermissions() == false) {
            val it = Intent("com.oplus.soundrecorder.openCloudSwitch")
            it.setPackage(requireContext.packageName)
            it.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            startActivity(it)
            return
        }
        lifecycleScope.launch(Dispatchers.IO) {
            if (false == cloudTipManager?.isLoginFromCache()) {
                cloudTipManager?.accountRequestLogin(requireContext.applicationContext) {
                    if (it) {
                        openCloudSwitchOn()
                    }
                }
            } else {
                openCloudSwitchOn()
            }
        }
    }

    private fun doOpenSyncFun() {
        if (BaseUtil.isEXP()) {
            //外销不做云同步二次校验
            doOpenSyncFunAfterVerify()
        } else {
            if (cloudKitApi?.checkAccountIsVerified() == true) {
                val callback = object : VerifyCallBack {
                    override fun onSuccess() {
                        //校验成功
                        doOpenSyncFunAfterVerify()
                    }

                    override fun onFail() {
                        DebugUtil.v(TAG, "doOpenSyncFun openSync onFail")
                    }
                }
                activity?.let { cloudKitApi?.checkLoginAndVerify(it, callback) }
            } else {
                doOpenSyncFunAfterVerify()
            }
        }
    }

    @SuppressLint("NewApi")
    private fun openCloudSwitchOn() {
        var loadingDialog: AlertDialog? = null
        var showLoadingDialog = true

        lifecycleScope.launch(Dispatchers.Main) {
            launch(Dispatchers.IO) {
                cloudKitApi?.getCloudGlobalState(true, object : ICloudGlobalStateCallBack {
                    override fun onSuccess(changed: Boolean, support: Boolean, state: String?) {
                        showLoadingDialog = false
                        if (support) {
                            doOpenSyncFun()
                        } else {
                            lifecycleScope.launch(Dispatchers.Main) {
                                val activity = activity ?: return@launch
                                cloudKitApi?.showGlobalDisableDialog(activity = activity, state = state, buttonListener = null)
                            }
                        }
                        lifecycleScope.launch(Dispatchers.Main) {
                            loadingDialog?.dismiss()
                        }
                    }

                    override fun onFailure() {
                        showLoadingDialog = false
                        lifecycleScope.launch(Dispatchers.Main) {
                            loadingDialog?.dismiss()
                        }
                    }
                })
            }
            /*300ms内获取到结果不显示loading*/
            delay(NUMBER_L300)
            if (showLoadingDialog) {
                loadingDialog = cloudKitApi?.showGlobalLoadingDialog(activity)
            }
        }
    }

    private fun openCloudSwitchOnAfterVerify() {
        val isSuccess = cloudKitApi?.setSyncSwitch(CloudSwitchState.OPEN_ONLY_WIFI) ?: false
        DebugUtil.d(TAG, "openCloudSwitchOnAfterVerify, isSuccess:$isSuccess")
        if (isSuccess) {
            context?.let {
                cloudKitApi?.setCloudGrantedStatus(it)
                cloudKitApi?.clearLastUserData(it)

                val switchStateOn = cloudKitApi?.queryCloudSwitchState(false) ?: -1 > CloudSwitchState.CLOSE
                if (switchStateOn) {
                    lifecycleScope.launch(Dispatchers.Main) {
                        startSettingCloudPage()
                        refreshData()
                    }
                }
            }
        }
    }

    /**
     * 云同步全球一体化新增
     */
    private fun doOpenSyncFunAfterVerify() {
        val isSuccess = cloudKitApi?.setSyncSwitch(CloudSwitchState.OPEN_ONLY_WIFI) ?: false
        DebugUtil.d(TAG, "openCloudSwitchOn, isSuccess:$isSuccess")
        if (isSuccess) {
            context?.let {
                cloudKitApi?.setCloudGrantedStatus(it)
                cloudKitApi?.clearLastUserData(it)

                lifecycleScope.launch(Dispatchers.Main) {
                    startSettingCloudPage()
                    refreshData()
                }
            }
        }
    }

    private fun startSettingCloudPage() {
        cloudTipManager?.launchCloudSettingPage(context)
        CloudStaticsUtil.addCloudTipsClickOpenEvent()
    }

    fun onPrivacyPolicySuccess(type: Int, pageFrom: Int?) {
        DebugUtil.d(TAG, "onPrivacyPolicySuccess, type:$type")
        when (type) {
            PrivacyPolicyConstant.TYPE_PERMISSION_CLOUD -> {
                //startSettingCloudPage(true)
                switchCloudOn()
            }

            PrivacyPolicyConstant.TYPE_PERMISSION_CONVERT -> {
                initSmartNameManagerImpl()
                mSmartNameMangerImpl?.doClickPermissionConvertOK(activity, mBrowseViewModel.needSmartNameMediaList, pageFrom)
                isOpenSwitch = false
                mBrowseViewModel.clearNeedSmartNameMedias()
            }
        }
    }

    fun onPrivacyPolicyFail(type: Int) {
        DebugUtil.d(TAG, "onPrivacyPolicyFail, type:$type")
        if (type == PrivacyPolicyConstant.TYPE_PERMISSION_CONVERT) {
            isOpenSwitch = false
        }
    }

    private fun onDataChanged(data: List<ItemBrowseRecordViewModel>) {
        if (DebugUtil.isLogOpen()) {
            DebugUtil.i(TAG, "onDataChanged ${data.size}")
        }
        context?.let {
            mAdapter.setData(data, isGroupingByContact())
            initCloudState()
            if (recordFilterIsRecycle()) {
                checkRecycleHeadTips(data)
            } else {
                checkQuestionCardView()
            }
            //浮窗时，数据加载完成再初始化Behavior
            if (isFlexibleWindow(activity)) {
                attachBehavior()
            }
            if (data.isEmpty()) {
                DebugUtil.d(TAG, "onDataChanged, expandSubTitle")
                mBehavior?.expandSubTitle()
            }
            // 录音再编辑模式下，去其他入口或文管删除了数据，会通过fileobserve更新selectMap，
            // 切回录音不会走livedata observe，导致勾选数据不正确，在此更新一下
            updateSelectMap()

            checkSupportSmartName()
        }
    }

    private fun recordFilterIsRecycle(): Boolean {
        return mBrowseViewModel.getCurrentGroup().isRecentlyDeleteGroup()
    }

    private fun checkRecycleHeadTips(data: List<ItemBrowseRecordViewModel>) {
        /*最近删除分组：隐藏问卷、云同步(配置、开关、下线公告)卡片*/
        if (questionGuideTipView != null) {
            releaseQuestionView()
            mAdapter.setHeader(TYPE_HEADER_QUESTION, null)
        }
        /*云同步开关、下线公告共用一个header type*/
        mAdapter.setHeader(TYPE_HEADER_CLOUD, null)
        if (mCloudGuideTipView != null) {
            mCloudGuideTipView?.release()
            mCloudGuideTipView = null
        }
        mAdapter.setHeader(TYPE_HEADER_CLOUD_CONFIG, null)

        if (data.isEmpty()) {
            DebugUtil.d(TAG, "checkRecycleHeadTips, data is empty")
            return
        }
        val headerEmpty = mAdapter.isHeaderEmpty()
        val isMainSystemUser = BaseApplication.sIsMainSystem
        val showRecycleHeader = headerEmpty && isMainSystemUser

        mRecycleHeaderView = mAdapter.getHeadeView(TYPE_HEADER_RECYCLE) as? RecycleHeaderView
        if (mRecycleHeaderView == null && showRecycleHeader) {
            context?.let {
                mRecycleHeaderView = RecycleHeaderView(it)
                mAdapter.setHeader(TYPE_HEADER_RECYCLE, mRecycleHeaderView)
            }
        }
        DebugUtil.i(
            TAG,
            "checkRecycleHeadTips headerEmpty $headerEmpty, " +
                    "isMainSystem $isMainSystemUser, " +
                    "showRecycleHeader $showRecycleHeader"
        )
    }

    /**
     * 电话本点击跳转进入录音查看通话录音需要滚动到对应位置并高亮提示
     */
    private fun checkScrollPositionFromPhoneBook(itemBrowseRecordViewModels: List<ItemBrowseRecordViewModel>) {
        val position = mBrowseViewModel.findPositionByFileNameAddPath(
            ItemBrowseRecordViewModel.addScrollAndTipsDisplayName,
            ItemBrowseRecordViewModel.addScrollAndTipsFilePath
        )
        if (position != -1) {
            mBinding.mListView.doOnLayout {
                (mBinding.mListView.layoutManager as? LinearLayoutManager)?.let {
                    scrollToPosition(position, itemBrowseRecordViewModels, it)
                }
            }
        }
    }

    private fun scrollToPosition(
        position: Int,
        itemBrowseRecordViewModels: List<ItemBrowseRecordViewModel>,
        layoutManager: LinearLayoutManager
    ) {
        val lastCompletelyVisibleItemPosition =
            layoutManager.findLastCompletelyVisibleItemPosition()
        val tipsPosition = position + mAdapter.getHeaderSize() + 1
        DebugUtil.d(
            TAG,
            "lastCompletelyVisibleItemPosition = $lastCompletelyVisibleItemPosition ,position = $position ,tipsPosition = $tipsPosition"
        )
        if (tipsPosition > lastCompletelyVisibleItemPosition) {
            mBinding.mListView.post {
                (mBinding.mListView.layoutManager as LinearLayoutManager).scrollToPositionWithOffset(
                    tipsPosition, mBinding.browseToolbar.height
                )
                if (isGroupingByContact()) {
                    val viewHolder = mBinding.mListView.findViewHolderForAdapterPosition(position)
                    val rvChildList = viewHolder?.itemView?.findViewById<COUIRecyclerView>(R.id.rv_call_child_list)
                    val childPosition = mBrowseViewModel.findChildPositionByFileNameAddPath(
                        ItemBrowseRecordViewModel.addScrollAndTipsDisplayName,
                        ItemBrowseRecordViewModel.addScrollAndTipsFilePath
                    )
                    DebugUtil.d(TAG, "scrollToPosition, childPosition:$childPosition, rvChildList:$rvChildList")
                    if (childPosition != -1) {
                        (rvChildList?.layoutManager as? LinearLayoutManager)?.scrollToPositionWithOffset(
                            childPosition,
                            mBinding.browseToolbar.height
                        )
                    }
                }
            }
        }
        //中大屏表现是滚动到指定位置选中该条录音
        if (!mBrowseFileActivityViewModel.isSmallWindow()) {
            val data = if (isGroupingByContact()) {
                getCallGroupChildRecordModel(itemBrowseRecordViewModels)
            } else {
                itemBrowseRecordViewModels[position]
            }
            mBrowseFileActivityViewModel.mCurrentPlayRecordData.value =
                data?.toStartPlayModel(mBrowseFileActivityViewModel.isFromOtherApp, isRecycle = recordFilterIsRecycle())
                    ?.apply {
                        autoPlay = false
                    }
            //中大屏不执行高亮动效，选中后就置空
            ItemBrowseRecordViewModel.addScrollAndTipsDisplayName = null
            ItemBrowseRecordViewModel.addScrollAndTipsFilePath = null
        }
    }

    private fun getCallGroupChildRecordModel(itemBrowseRecordViewModels: List<ItemBrowseRecordViewModel>): ItemBrowseRecordViewModel? {
        itemBrowseRecordViewModels.forEach { itemGroup ->
            itemGroup.recordList?.forEach {
                if (it.displayName == ItemBrowseRecordViewModel.addScrollAndTipsDisplayName
                    && it.relativePath == ItemBrowseRecordViewModel.addScrollAndTipsFilePath
                ) {
                    return it
                }
            }
        }
        return null
    }

    /**
     * 问卷卡片优先级高于云同步卡片,先判断是否显示问卷，再判断是否显示云同步卡片
     */
    private fun checkQuestionCardView() {
        val act = activity ?: return
        if (mRecycleHeaderView != null) {
            mAdapter.setHeader(TYPE_HEADER_RECYCLE, null)
            mRecycleHeaderView = null
        }
        //内容等于null
        if (!mAdapter.isContentNotEmpty()) {
            return
        }

        if (BaseUtil.isEXP()) {
            // 外销不支持问卷
            onQuestionCardCallBack(false, null)
            return
        }
        //未同意联网不获取问卷
        if (isNetWorkGranted(act)) {
            checkQuestionGuideShow(act)
        } else {
            val showCloudCard = checkShowCloudCard()
            if (!showCloudCard && cloudKitApi?.isNetWorkGranted(act) == true) {
                //同意了联网授权，才能获取云同步配置项
                getCloudConfigTips()
            }
        }
    }

    private fun checkQuestionGuideShow(act: FragmentActivity) {
        DebugUtil.i(TAG, "checkQuestionGuideShow")
        val oldQAView = mAdapter.getHeadeView(TYPE_HEADER_QUESTION) as? QuestionnaireGuideTipView
        if (oldQAView != null) {
            DebugUtil.i(TAG, "checkQuestionCardView,oldQAView not null")
            oldQAView.refreshViewData()
            return
        }
        //没有网络，就不显示问卷
        if (!NetworkUtils.isNetworkInvalid(context)) {
            DebugUtil.d(TAG, "check questionGuideTipView")
            questionGuideTipView?.release() // 避免callback未回来前又走了一遍获取数据逻辑
            questionGuideTipView =
                QuestionnaireGuideTipView(act, this.lifecycleScope, object : QuestionCDPCallback {
                    override fun callback(code: Int, msg: String, data: String?) {
                        when (code) {
                            ACTION_CARD_IGNORED -> {
                                DebugUtil.d(TAG, "ACTION_CARD_IGNORED ->$code\t$msg")
                                mAdapter.hideQuestionHeader() {
                                    releaseQuestionView()
                                }
                            }

                            ACTION_CARD_SHOWN -> {
                                DebugUtil.d(TAG, "ACTION_CARD_SHOWN ->$code\t$msg")
                                //当cdpView正在显示，不需要再重新加载，否则屏幕可能会闪
                                onQuestionCardCallBack(true, questionGuideTipView)
                            }

                            ACTION_SURVEY_SUBMITTED -> {
                                DebugUtil.d(TAG, "ACTION_SURVEY_SUBMITTED ->$code\t$msg")
                                onQuestionCardCallBack(false, null)
                            }

                            ACTION_NO_DATA -> {
                                DebugUtil.d(TAG, "ACTION_NO_DATA ->$code\t$msg")
                                onQuestionCardCallBack(false, null)
                            }

                            ACTION_DATA_VALID -> DebugUtil.d(TAG, "ACTION_DATA_VALID ->$code\t$msg")

                            else -> {
                                DebugUtil.d(TAG, "else -> $code\t$msg")
                                onQuestionCardCallBack(false, null)
                            }
                        }
                    }
                }, null)
        } else {
            onQuestionCardCallBack(false, null)
        }
    }

    /**
     * 问卷不显示时，（可能Adapater中已经新增了Question的相关卡片，但是在访问网络时，发现不需要显示Question的卡片）
     * 需要mAdapter中的header置空
     */
    private fun onQuestionCardCallBack(show: Boolean, cardView: QuestionnaireGuideTipView?) {
        if (!show) {
            mAdapter.setHeader(TYPE_HEADER_QUESTION, null)
            mAdapter.hideQuestionHeader() {
                releaseQuestionView()
            }
            val showCloudCard = checkShowCloudCard()
            val isNetWorkGranted = activity?.let { cloudKitApi?.isNetWorkGranted(it) } ?: false
            if (!showCloudCard && isNetWorkGranted) {
                getCloudConfigTips()
            }
        } else {
            DebugUtil.i(TAG, "set QuestionCardView ${cardView?.getChildAt(0)} ${cardView?.childCount}")
            //问卷显示
            mAdapter.setHeader(TYPE_HEADER_QUESTION, cardView)
            mQuestionShowState = QUESTION_SHOW_STATE
        }
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        if (mBrowseViewModel.showSearch.value == false) {
            when (item.itemId) {
                R.id.item_speaker -> toggleSpeaker()
                R.id.item_search -> {
                    BuryingPoint.addSearch(RecorderUserAction.VALUE_SEARCH_CLICK)
                    showSearch()
                }

                R.id.item_edit_big,
                R.id.item_edit -> {
                    BuryingPoint.addClickMoreEdit()
                    mBrowseViewModel.toggleEditMode(recordFilterIsRecycle())
                }

                R.id.item_setting_big,
                R.id.item_setting -> {
                    BuryingPoint.addClickMoreSetting()
                    BuryingPoint.addEnterSettingOpen()
                    mBrowseViewModel.onClickSetting(this)
                    /*小屏,进入设置页面，释放快捷播放播放器*/
                    if (mBrowseFileActivityViewModel.isSmallWindow()) {
                        mBrowseViewModel.releasePlayer()
                    }
                }

                R.id.item_select_all -> mBrowseViewModel.selectAllOrNone()
                R.id.item_cancel -> {
                    mBinding.browseToolbar.postDelayed({
                        mBrowseViewModel.exitEditMode()
                        //退出编辑模式，状态重置，否则可能导致新增记录列表无法刷新
                        mNavigationViewManager?.resetContinueOperator()
                    }, MENU_PRESS_DELAY_TIME)
                }
            }
        }
        return true
    }

    private fun callGroupBySort() {
        DebugUtil.d(TAG, "groupBySort, subMenuCheckedPosition:$mSubMenuCheckedPosition")
        if (::mAdapter.isInitialized && mBinding.mListView.scrollState == RecyclerView.SCROLL_STATE_IDLE) {
            mAdapter.clear()
            mAdapter.notifyDataSetChanged()
        }
        var isGrouping: Boolean = false
        when (mSubMenuCheckedPosition) {
            CALL_RECORD_NOT_GROUPING -> isGrouping = false

            CALL_RECORD_CONTACT_GROUPING -> isGrouping = true
        }
        needClearPlayData = true
        resetRecycleDialogShowing()
        mAdapter.setGroupingByContact(isGrouping)
        if (isGrouping) {
            mBrowseViewModel.changeCallGrouping()
        } else {
            refreshData()
        }
        ItemBrowseRecordViewModel.liveAddFooter[taskId]?.value = 0
        if (isGrouping) {
            addCallGroupingByContact()
        }
    }

    private fun toggleSpeaker() {
        mBrowseFileActivityViewModel.mSpeakerModeController.performSpeakerMenuItemClick(SpeakerModeController.PLAYER_SPEAKER_BROWSE)
    }

    private fun setSearchView(menu: Menu) {
        val searchItem = menu.findItem(R.id.item_search)
        val searchAnimView = searchItem?.actionView as? COUISearchBar
        val paddingStart = resources.getDimensionPixelOffset(com.soundrecorder.common.R.dimen.dp12)
        searchAnimView?.apply {
            setPaddingRelative(paddingStart, 0, paddingStart, 0)
            functionalButton?.setOnClickListener {
                mSearchFragment?.onClickCancel()
            }
            findViewById<ImageView>(com.support.toolbar.R.id.animated_search_icon)?.contentDescription =
                getString(com.soundrecorder.base.R.string.search)
            searchEditText?.addTextChangedListener(this@BrowseFragment)
            searchEditText?.setOnEditorActionListener(this@BrowseFragment)
            setAtBehindToolBar(mBinding.browseToolbar, Gravity.TOP, searchItem)
            mCacheHolder.mSearchAnimView = this
        }
        mCacheHolder.toolbar = mBinding.browseToolbar
        mCacheHolder.appBarLayout = mBinding.abl
        mCacheHolder.searchBox = mBinding.flSearchBox
        mCacheHolder.subTitleView = mBinding.content
        mCacheHolder.mainTitleLayout = mBinding.mainTitle
        mCacheHolder.subTitleLayout = mBinding.toolbarSubtitle
        //mCacheHolder.rotateView = mBinding.folderNameRotateView
        mCacheHolder.browseFileList = mBinding.mListView
        mCacheHolder.subTitleViewMaxHeight = mBinding.content.getUnDisplayViewHeight()
    }

    fun onSearchHistoryClick(content: String) {
        mCacheHolder.mSearchAnimView?.searchEditText?.setText(content)
        mCacheHolder.mSearchAnimView?.searchEditText?.setSelection(content.length)
    }

    fun getSearchKey(): String? {
        return mCacheHolder.mSearchAnimView?.searchEditText?.text?.toString()
    }

    private fun initiateWindowInsets() {
        if (mBinding.root != null) {
            val callback = object : DeDuplicateInsetsCallback() {
                override fun onApplyInsets(v: View, insets: WindowInsetsCompat) {
                    //使用statusBars的高度    systemBars的高度不对
                    val stableStatusBarInsets =
                        insets.getInsetsIgnoringVisibility(WindowInsetsCompat.Type.systemBars())
                    //底部面板bottom在onLayoutChange中动态设置高度nagivationHeight
                    nagivationHeight = stableStatusBarInsets.bottom
                    // view 顶部statusBar高度
                    mBinding.rootView.updatePadding(
                        top = stableStatusBarInsets.top,
                        left = stableStatusBarInsets.left, right = stableStatusBarInsets.right
                    )
                    mBehavior?.updateSystemBarInsetsTop(stableStatusBarInsets.top)
                    // view 底部navigationBar高度
                    mBinding.mListView.updatePadding(bottom = nagivationHeight)
                    if (mCurrentGroup != null) {
                        mAdapter.setFooter(calBlankFooterHeight())
                    }

                    mNavigationLayout?.updatePadding(bottom = nagivationHeight)
                    setRecorderPanelHeight()
                    setNavigationColor()
                }
            }
            ViewCompat.setOnApplyWindowInsetsListener(mBinding.root, callback)
        }
    }

    private fun setNavigationColor() {
        TaskBarUtil.setNavigationColorOnSupportTaskBar(
            navigationHeight = nagivationHeight,
            activity = activity,
            defaultNoTaskBarColor = navigationBarColor(),
            taskBarColor()
        )
    }

    private fun navigationBarColor(): Int? {
        return if (ItemBrowseRecordViewModel.liveEditMode[taskId]?.value == true) {
            // 编辑模式工具栏颜色同背景色不一致，所以需要单独设置
            com.support.appcompat.R.color.coui_color_bottom_bar
        } else {
            (activity as? BaseActivity)?.navigationBarColor()
        }
    }

    /**
     * taskbar颜色设置，编辑模式下，为默认taskbar颜色，非编辑模式下，同录制面板颜色一致
     */
    private fun taskBarColor(): Int {
        return if (ItemBrowseRecordViewModel.liveEditMode[taskId]?.value == true) {
            // 编辑模式下，同底部navigationView颜色一致
            com.support.appcompat.R.color.coui_color_bottom_bar
        } else {
            com.soundrecorder.base.R.color.common_background_color
        }
    }

    /**
     * 孔雀大小屏切换不会重建
     * 重命名弹窗需要dismiss后再弹出，否则会显示错乱
     */
    private fun initUiStatusObsers() {
        ResponsiveUIConfig.getDefault(this.context).uiStatus.observe(viewLifecycleOwner,
            Observer<UIConfig.Status> {
                DebugUtil.d(TAG, "onUI config change to $it")
                if (mNavigationViewManager?.isRenameDialogShowing() == true) {
                    mNavigationViewManager?.editDisplayName = mNavigationViewManager?.editDisplayName()
                    mBrowseViewModel.renameContent = mNavigationViewManager?.editDisplayName
                    mNavigationViewManager?.releaseRenameDialog()
                    mNavigationViewManager?.showRenameDialog()
                }
                if (mCurrentGroup != null) {
                    mAdapter.notifyDataSetChanged()
                }
                //折叠屏切换时，liveData非空导致注册的observer逻辑执行，当前选中的分组tab被切换
                mBrowseFileActivityViewModel.currentGroup.value = null
                mBrowseFileActivityViewModel.liveDataGroupList.value = null
            })
    }

    private fun showSearch() {
        if (ClickUtils.isFastDoubleClick()) {
            DebugUtil.i(TAG, "showSearch isFastDoubleClick ")
            return
        }
        if (mBrowseViewModel.showSearch.value == true) {
            DebugUtil.i(TAG, "showSearch is showing")
            return
        }
        mBrowseViewModel.releasePlayer()
        mSearchFragment = SearchFragment().also {
            it.searchCacheHolder = mCacheHolder
            it.browseViewModel = mBrowseViewModel
        }
        replaceFragmentByTag(
            R.id.fl_search_box,
            mSearchFragment,
            SearchFragment.TAG
        )
        mBrowseViewModel.showSearch.value = true

        mBinding.root.postDelayed({
            mBinding.browseToolbar.dismissPopupMenus()
        }, 300)
    }

    /**
     * 搜索页面打开时关闭文件权限，此时activity强制重建，同时会重建activity+BrowseFragment+SearchFragment,
     * 此时SearchFragment在最上层阻碍所有的BrowseFragment的点击事件传递
     */
    private fun removeSearchFragmentIfNecessary() {
        if ((mBrowseViewModel.showSearch.value != true) || canRemoveSearchFragmentWhenSmallWindow()) {
            DebugUtil.d(
                logTag,
                "removeSearchFragmentIfNecessary: showSearchValue is ${mBrowseViewModel.showSearch.value}"
            )
            findFragment<SearchFragment>(SearchFragment.TAG)?.let { searchFragment ->
                removeFragment(searchFragment)
            }
            if (mBrowseViewModel.showSearch.value == true) {
                mBrowseViewModel.showSearch.value = false
            }
        }
    }

    private fun canRemoveSearchFragmentWhenSmallWindow(): Boolean {
        /*在有播放页面情况下，切到小屏，有搜索且搜索关键词为null，则退出搜索*/
        return (mBrowseViewModel.showSearch.value == true) && (ScreenUtil.isSmallScreen(context))
                && (mBrowseViewModel.searchValue.value.isNullOrBlank())
                && (mBrowseFileActivityViewModel.hasPlayPageData())
    }

    override fun onClickCancel(): Boolean {
        mSearchFragment?.onClickCancel()
        return true
    }

    private fun findSearchFragment() {
        if ((mSearchFragment == null) && isAdded) {
            mSearchFragment = findFragment(SearchFragment.TAG)
            mSearchFragment?.searchCacheHolder = mCacheHolder
            mSearchFragment?.browseViewModel = mBrowseViewModel
        }
    }

    private fun showRecorderAndTitle(group: GroupInfo) {
        val isEditMode = ItemBrowseRecordViewModel.liveEditMode[taskId]?.value ?: false
        DebugUtil.d(TAG, "showRecorderAndTitle, mode:${group.mGroupName}, isEditMode:$isEditMode")
        startRecordLayoutAnim(isEditMode, group)
        mBinding.toolbarTitle.text = getString(com.soundrecorder.common.R.string.app_name_main)
        mBinding.browseToolbar.title = getString(com.soundrecorder.common.R.string.app_name_main)
        when (group.mGroupType) {
            INT_DEFAULT_CALLING -> {
                mBinding.toolbarSubtitle.setRecycleSubtitleVisible(false)

                mBottomMarginView?.setDisplay(isEditMode)
            }

            INT_DEFAULT_RECENTLY_DELETED -> {
                mBinding.toolbarSubtitle.setRecycleSubtitleVisible(true)

                ensureNavigationView()
                switchToNavigationMenu(
                    ItemBrowseRecordViewModel.liveEditMode[taskId]?.value,
                    true
                )
            }

            else -> {
                mBinding.toolbarSubtitle.setRecycleSubtitleVisible(false)

                mBottomMarginView?.setDisplay(isEditMode)
            }
        }
    }

    override fun onViewCreated(savedInstanceState: Bundle?) {
        DebugUtil.i(TAG, "onViewCreated")
        setBrowseViewModel()
        removeSearchFragmentIfNecessary()
        checkJumpRecorderActivity()
        observe()
        initView()
        registerReceiver()
        loadSpeakerMode()
        checkTipForUpgrade()
        initExportRecords()
        initRecycleFile()
        initFeedbackAction()
        initConvertConfig()
        buryPointGroupState()
    }

    /**
     * 初始化文本转写云控，用于判断是否支持分享文本到WPS
     */
    private fun initConvertConfig() {
        val act = activity ?: return
        if (PermissionUtils.isStatementConvertGranted(act) && isNetWorkGranted(act)) {
            playbackApi?.updateConvertConfig()
        }
    }

    /**
     * 帮助与反馈回调信息列表
     */
    private fun initFeedbackAction() {
        if (!BaseUtil.isEXP()) {
            feedBackApi?.getFeedbackRequestData {
                mBrowseViewModel.addFeedbackRequestData(it)
            }
        }
    }

    /**
     * 初始化外销上初始化媒体库表
     */
    private fun initExportRecords() {
        if (!BaseUtil.isEXP()) {
            return
        }
        //外销上初始化媒体库表
        RecordDataSyncHelper.doMediaFullCompare()
    }

    /**
     * 初始化回收站文件目录
     */
    private fun initRecycleFile() {
        val flag = PrefUtil.getBoolean(context, PermissionUtils.FIRST_START_APP, true)
        if (!flag) {
            return
        }
        DebugUtil.d(TAG, "initRecycleFile success")
        PrefUtil.putBoolean(context, PermissionUtils.FIRST_START_APP, false)

        //清空回收站，理论上首次启动带回收站需求的版本时，不会存在回收站目录
        FileDealUtil.initRecycleFile()
    }

    override fun onDestroyView() {
        releaseQuestionView()
        mBottomNavigationView?.menu?.clear()
        mBottomNavigationView?.setOnAnimatorListener(null)
        mCloudGuideTipView?.release()
        mRecycleHeaderView = null
        mBinding.mListView.removeCallbacks(smoothToTopRunnable)
        mBinding.rootView.removeOnLayoutChangeListener(mLayoutChangeListener)
        mBinding.browseToolbar.removeCallbacks(updateToolBarMenuRunnable)
        mBinding.mListView.removeCallbacks(updateFooterRunnable)
        mBinding.mListView.removeCallbacks(refreshRunnable)

        unRegisterAudioMediaContentObserver()

        mEmptyOSImageView?.onRelease()
        //cancel record animation when fragment is finishing
        recordAnimationSet?.cancel()
        recordAnimationSet = null
        mBinding.mListView.animation?.let {
            it.setAnimationListener(null)
            it.cancel()
        }

        FollowDialogRestoreUtils.releaseFollowDialogRunnable(activity?.window?.decorView)
        disableDialog.dismissWhenShowing()
        mFilePermissionDialog.dismissWhenShowing()
        super.onDestroyView()
        itemDragDelegate = null
        dragViewReference = null
        disableDialog = null
        mBrowseViewModel.stopObserver()
    }

    /**
     * 分组状态埋点，一周上报一次
     * 调用全局埋点管理器执行埋点操作
     */
    private fun buryPointGroupState() {
        // 使用全局埋点管理器执行埋点操作，避免与 Fragment 生命周期绑定
        GroupStateBuryingPointManager.checkAndReportGroupState()
    }

    override fun onDestroy() {
        super.onDestroy()
        release()
        unregisterReceiver()
        mTipStatusObserver = null
        dismissShareWaitingDialog()
        mBrowseViewModel.clearLifecycle(this)
        mSmartNameMangerImpl?.release()
        mSmartNameMangerImpl = null
    }

    private fun releaseQuestionView() {
        questionGuideTipView?.release()
        questionGuideTipView = null
    }

    private fun onCreateOptionsMenuCheckRestoreWindow() {
        DebugUtil.d(TAG, "onCreateOptionsMenuCheckRestoreWindow")
        checkRestoreShowDeleteDialog()
        checkRestoreShowRenameDialog()
        checkRestoreShowLimitDialog()
        checkRestoreShowRecoverDialog()
        checkRestoreShowDeleteAllDialog()
        checkRestoreShowShareDialog()
        checkRestoreShowShareTextDialog()
    }

    private fun checkRestoreShowDeleteAllDialog() {
        if (mBrowseViewModel.isRecycleDialogAllShowing) {
            FollowDialogRestoreUtils.followDialogRestore(
                activity?.window?.decorView, true,
                callBack = object : FollowRestoreCallBack {
                    override fun restoreCallBack() {
                        val itemDeleteAll = mBottomNavigationView?.menu?.findItem(R.id.item_delete_all)
                        onItemSelected(itemDeleteAll)
                    }
                }
            )
        }
    }

    private fun checkRestoreShowRecoverDialog() {
        if (mBrowseViewModel.isRecoverDialogShowing) {
            FollowDialogRestoreUtils.followDialogRestore(
                activity?.window?.decorView, true,
                callBack = object : FollowRestoreCallBack {
                    override fun restoreCallBack() {
                        val itemRecover = mBottomNavigationView?.menu?.findItem(R.id.item_recycle_recover)
                        onItemSelected(itemRecover)
                    }
                }
            )
        }
    }

    private fun checkRestoreShowLimitDialog() {
        if (mBrowseViewModel.isLimitDialogShowing) {
            showLimitDialog()
        }
    }

    private fun checkRestoreShowDeleteDialog() {
        if (mBrowseViewModel.isDeleteDialogShowing) {
            FollowDialogRestoreUtils.followDialogRestore(
                activity?.window?.decorView, true,
                callBack = object : FollowRestoreCallBack {
                    override fun restoreCallBack() {
                        val itemDelete = if (recordFilterIsRecycle()) {
                            mBottomNavigationView?.menu?.findItem(R.id.item_recycle_delete)
                        } else {
                            mBottomNavigationView?.menu?.findItem(R.id.item_delete)
                        }
                        onItemSelected(itemDelete)
                    }
                }
            )
        }
    }

    private fun checkRestoreShowRenameDialog() {
        if (mBrowseViewModel.isRenameDialogShowing) {
            val itemRename = mBottomNavigationView?.menu?.findItem(R.id.item_rename)
            onItemSelected(itemRename)
        }
    }

    private fun checkRestoreShowShareDialog() {
        if (mBrowseViewModel.isShareDialogShowing) {
            val itemShare = mBottomNavigationView?.menu?.findItem(R.id.item_send)
            onItemSelected(itemShare)
            mBrowseViewModel.isShareDialogShowing = false
        }
    }

    private fun checkRestoreShowShareTextDialog() {
        if (mBrowseViewModel.isShareTextDialogShowing) {
            val itemShareText = mBottomNavigationView?.menu?.findItem(R.id.item_send)
            onItemSelected(itemShareText)
            mBrowseViewModel.isShareTextDialogShowing = false
        }
    }

    private fun release() {
        mIsInitCloudState = false
        cloudTipManager?.releaseAccountReqCallback()
        if (activity?.isFinishing == true) {
            ItemBrowseRecordViewModel.run {
                liveEditMode[taskId]?.value = false
                liveDragMode[taskId]?.value = false
                liveSelectedMap[taskId]?.value?.clear()
                liveSelectedMap[taskId]?.value = null
                liveSelectedMap.remove(taskId)
                liveEditMode.remove(taskId)
                liveDragMode.remove(taskId)
                liveAddFooter.remove(taskId)
            }
            if (::mBrowseViewModel.isInitialized) {
                mBrowseViewModel.releasePlayer()
            }
            if (taskId == ActivityTaskUtils.getMainTaskId()) {
                cloudKitApi?.setIgnoreHighTemperature(false)
            }
        }
        releaseBehavior()
        TransitionUtils.release()
        activity?.restoreRingMode()
        if (::mBrowseViewModel.isInitialized) {
            cacheWindowShowing()
            mBrowseViewModel.stopObserver()
        }
        releaseLimitDialog()
        releaseDeleteDialog()
        mNavigationViewManager?.release()
        if (::mAdapter.isInitialized) {
            mAdapter.release()
        }
        releaseGroup()
    }

    private fun releaseGroup() {
        mCurrentGroup = null
        mCurrentGroupUuId = null
    }

    private fun releaseBehavior() {
        mBehavior?.release()
        mBehavior = null
    }

    private fun releaseDeleteDialog() {
        if (mDeleteDialog?.isShowing() == true) {
            mDeleteDialog?.dismiss()
        }
        mDeleteDialog = null
    }

    private fun cacheWindowShowing() {
        mBrowseViewModel.isDeleteDialogShowing =
            mNavigationViewManager?.isDeleteDialogShowing() == true
        mBrowseViewModel.isRenameDialogShowing =
            mNavigationViewManager?.isRenameDialogShowing() == true
        mBrowseViewModel.isLimitDialogShowing = mLimitDialog?.isShowing == true
//        mBrowseViewModel.isSingleDialogShowing = mSingleModelDialog?.isShowing() == true
        mBrowseViewModel.isRecoverDialogShowing =
            mNavigationViewManager?.isRecoverDialogShowing() == true
        mBrowseViewModel.isRecycleDialogAllShowing =
            mNavigationViewManager?.isDeleteAllDialogShowing() == true
        mBrowseViewModel.isShareDialogShowing =
            mNavigationViewManager?.isShareDialogShowing() == true
        mBrowseViewModel.isShareTextDialogShowing =
            mNavigationViewManager?.isShareTextDialogShowing() == true
    }

    override fun onClick(v: View?) {
        when (v?.id) {
            R.id.red_circle_icon -> {
                if (ClickUtils.isFastDoubleClick()) {
                    DebugUtil.i(TAG, "isFastDoubleClick return")
                    return
                }
                val toRecordSuccess = mBrowseViewModel.onClickStartRecorderActivity(
                    this, isSmallWindowHeight(), false,
                    checkIsCall = true
                )
                // 进入录制页面，停止播放，且清除通知栏播放通知
                if (toRecordSuccess) {
                    mBrowseFileActivityViewModel.clickedToRecord = true
                    (activity as? BrowseFile)?.pausePlayDetail(true)
                }
            }
        }
    }

    private fun initView() {
        initiateWindowInsets()
        configToolbar()
        fixGradient()
        setGroupLabelAdapter()
        setListViewAdapter()
        if (!isFlexibleWindow(activity)) {
            attachBehavior()
        }
        mBinding.rootView.addOnLayoutChangeListener(mLayoutChangeListener)
        if (!ItemBrowseRecordViewModel.addScrollAndTipsDisplayName.isNullOrEmpty()
            && !ItemBrowseRecordViewModel.addScrollAndTipsFilePath.isNullOrEmpty()
            && ItemBrowseRecordViewModel.livePhoneBookMode[taskId] == true
        ) {
            mBinding.mListView.adapter?.registerAdapterDataObserver(object : AdapterDataObserver() {
                override fun onChanged() {
                    super.onChanged()
                    (mBinding.mListView.adapter as BrowseAdapter).getData()?.let {
                        mBinding.mListView.adapter?.unregisterAdapterDataObserver(this)
                        checkScrollPositionFromPhoneBook(it)
                    }
                }
            })
        }
        changeBehaviorCurrentStatusByScreenHeight(mBrowseViewModel.showSearch.value)
        setClickListener()
        initDragDelegate()
        initSmartNameManagerImpl()

        refreshGroups()
    }

    private val labelAdapter by lazy {
        activity?.let { GroupHorizonLabelAdapter(it) }
    }

    private fun setGroupLabelAdapter() {
        mCurrentGroupUuId = PrefUtil.getString(BaseApplication.getAppContext(), KEY_RECORD_GROUP_UUID, "")
        DebugUtil.d(TAG, "setGroupLabelAdapter, mCurrentGroupUuId:$mCurrentGroupUuId")
        labelAdapter?.setOnGroupLabelClickListener(object : GroupHorizonLabelAdapter.OnGroupLabelClickListener {

            override fun onItemClick(position: Int, view: View, currentItem: GroupInfo) {
                if (mBinding.groupLabelContainer.rvLabelList.scrollState != RecyclerView.SCROLL_STATE_IDLE || ClickUtils.isQuickClick()) {
                    return
                }
                if (mBrowseViewModel.currentGroup.value?.mUuId != currentItem.mUuId) {
                    if (::mAdapter.isInitialized && mBinding.mListView.scrollState == RecyclerView.SCROLL_STATE_IDLE) {
                        mAdapter.clear()
                        mAdapter.notifyDataSetChanged()
                    }
                    mBrowseViewModel.currentGroup.value = currentItem
                    mBrowseViewModel.releasePlayer()
                    addClickGroup(currentItem)
                }
            }
        })
        mBinding.groupLabelContainer.rvLabelList.apply {
            val manager = LinearLayoutManager(activity, LinearLayoutManager.HORIZONTAL, false)
            isUseNativeOverScroll = true
            layoutManager = manager
            labelAdapter?.layoutManager = manager
            adapter = labelAdapter
            //scrollToPosition(0)
        }
        mBinding.groupLabelContainer.chipEntrance.apply {
            isChecked = false
            isCheckable = false
            click {
                if (!ClickUtils.isQuickClick()) {
                    showGroupListFragment(it)
                }
            }
        }
    }

    private fun updateCurrentDefaultGroup(groupInfos: MutableList<GroupInfo>) {
        val isShowCall = activity?.intent.isFromCall() || activity?.intent.isThreeRecordJumpToCall()
        DebugUtil.d(TAG, "updateCurrentDefaultGroup, isShowCall:$isShowCall")
        mCurrentGroup = if (isShowCall && mBrowseViewModel.currentGroup.value == null) {
            getLiveDataCallGroup(groupInfos)
        } else {
            if (!mCurrentGroupUuId.isNullOrEmpty()) {
                getGroupByGroupUuid(groupInfos)
            } else {
                groupInfos[0]
            }
        }
        mCurrentGroup?.let {
            mCurrentGroupUuId = it.mUuId
        }
        /* 当前分组信息有所改变时，才更新，以减少分组列表和录音列表的刷新。
        这里加上判断，也可以解决新增录音项的动效和带进度条的布局被云同步后二次刷新覆盖
         */
        if (mBrowseViewModel.currentGroup.value?.mUuId != mCurrentGroup?.mUuId || mGroupViewModel.isMovedGroup.value == true) {
            DebugUtil.d(TAG, "updateCurrentGroup, currentGroup:${mCurrentGroup?.mId}")
            mBrowseViewModel.currentGroup.value = mCurrentGroup
            mGroupViewModel.isMovedGroup.postValueSafe(false)
        }
    }

    private fun showGroupListFragment(view: View) {
        //VibrateUtils.vibrate(BaseApplication.getAppContext())
        view.performHapticFeedback(COUIHapticFeedbackConstants.GRANULAR_SHORT_VIBRATE_SYNC)
        val fm = activity?.supportFragmentManager
        fm?.apply {
            mBrowseFileActivityViewModel.isGroupListShow.value = true
            GroupFragment.show(R.id.fl_left_container, this, GroupFragment.TAG)
        }
    }

    private fun initDragDelegate() {
        if (itemDragDelegate == null) {
            itemDragDelegate = RecordItemDragDelegate(context, taskId)
        }
        itemDragDelegate?.setReceiveDragListener(activity?.window?.decorView)
    }

    override fun onReceive(intent: Intent?) {
        DebugUtil.i(TAG, "onReceive action:" + intent?.action)
        when (intent?.action) {
            Intent.ACTION_MEDIA_EJECT,
            Intent.ACTION_MEDIA_UNMOUNTED,
            Intent.ACTION_MEDIA_REMOVED,
            Intent.ACTION_MEDIA_MOUNTED -> whenMediaChangeRelease()
            NOTIFY_CONVERT_STATUS_UPDATE -> notifyConvertStatusUpdate(intent)
            NOTIFY_SMART_NAME_STATUS_UPDATE -> notifySmartNameStatusUpdate(intent)
            RecordFileChangeNotify.FILE_UPDATE_ACTION -> {
                val isRefresh = intent.getBooleanExtra(Constants.FRESH_FLAG, false)
                //云同步时，如果处于回收站，不刷新数据
                if (isRefresh || !recordFilterIsRecycle()) {
                    //云同步时，刷新数据前如果有新增录音记录在执行动画，会导致列表跳动。所以置空该变量避免动画执行
                    ItemBrowseRecordViewModel.addAnimatorDisplayName = null
                    refreshData()
                }
            }

            Intent.ACTION_USER_BACKGROUND -> activity?.finish()
            RecordFileChangeNotify.BRENO_FRONT_TO_RECORD -> {
                //此广播只有自己响应
                if (taskId == ActivityTaskUtils.getMainTaskId()) {
                    mBrowseViewModel.onClickStartRecorderActivity(
                        this,
                        isSmallWindowHeight(),
                        true,
                        checkIsCall = false
                    )
                }
            }

            ACTION_SUMMARY_STATE_CHANGED -> notifySummaryStateChanged(intent)
            RecordFileChangeNotify.CUBE_CLEAR_PLAY_RECORD_DATA -> mBrowseFileActivityViewModel.clearPlayRecordData()
            PermissionUtils.ACTION_FILE_PERMISSION -> showPermissionAllFileDialog()
            RecorderDataConstant.ACTION_UPDATE_CALLER_NAME_AVATAR_COLOR -> {
                val flag = intent.getIntExtra(
                    RecorderDataConstant.UPDATE_CALLER_NAME_AVATAR_COLOR_VALUE,
                    RecorderDataConstant.STOP_UPDATE_CALLER_NAME_AVATAR_COLOR
                )
                when (flag) {
                    RecorderDataConstant.START_UPDATE_CALLER_NAME_AVATAR_COLOR -> {
                        mBrowseViewModel.retryToUpdateFailedRecordsCallerNameAvatarColor()
                    }
                }
            }
            RecorderDataConstant.SMART_NAME_FILE_PERMISSION_ACTION -> {
                val mediaId = intent.getLongExtra(RecorderDataConstant.RECORD_MEDIA_ID, -1L)
                val mediaIdList = mutableListOf<Long>().apply {
                    add(mediaId)
                }
                showPermissionAllFileDialog(true, mediaIdList)
            }
        }
    }

    private fun notifySmartNameStatusUpdate(intent: Intent?) {
        ConvertingInfo.onSmartNameStatusChanged(intent)
        val mediaId = intent?.getLongExtra(KEY_NOTIFY_RECORD_ID, -1L) ?: 0
        val display = intent?.getBooleanExtra(KEY_NOTIFY_SMART_NAME_STATUS, false) ?: false
        val resultName = intent?.getStringExtra(KEY_NOTIFY_SMART_NAME_NAME_TEXT)
        val convertComplete = intent?.getBooleanExtra(KEY_NOTIFY_SMART_NAME_CONVERT_COMPLETE, false) ?: false
        DebugUtil.d(TAG, "notifySmartNameStatusUpdate, display:$display, resultname:$resultName")
        if (!resultName.isNullOrBlank()) {
            ConvertingInfo.addSmartNameResult(mediaId, resultName)
        }
        kotlin.runCatching {
            if (isGroupingByContact()) {
                mAdapter.getData()?.forEachIndexed { index, itemBrowseRecord ->
                    itemBrowseRecord.recordList?.forEach { childRecord ->
                        if (mediaId == childRecord.mediaId) {
                            childRecord.converting = false
                            childRecord.convertCompleted = convertComplete
                            childRecord.smartNaming = display
                            childRecord.showSmartTextAnim = !display
                            mAdapter.notifyItemChanged(index + 1 + mAdapter.getHeaderSize())
                            return@forEach
                        }
                    }
                }
            } else {
                mAdapter.getData()?.forEachIndexed { index, itemBrowseRecord ->
                    if (mediaId == itemBrowseRecord.mediaId) {
                        itemBrowseRecord.converting = false
                        itemBrowseRecord.convertCompleted = convertComplete
                        itemBrowseRecord.smartNaming = display
                        itemBrowseRecord.showSmartTextAnim = !display
                        val pos = index + 1 + mAdapter.getHeaderSize()
                        DebugUtil.i(TAG, "notifySmartNameStatusUpdate pos=$pos")
                        notifyItemChangeNoAnimator(pos)
                        return@forEachIndexed
                    }
                }
            }
        }.onFailure {
            DebugUtil.e(TAG, "notifySmartNameStatusUpdate, error:$it")
        }
    }

    /**
     * 解决使用原生动画刷新引起闪烁问题
     */
    private fun notifyItemChangeNoAnimator(pos: Int) {
        val preAnimator = mBinding.mListView.itemAnimator
        mBinding.mListView.apply {
            itemAnimator = null
            adapter?.notifyItemChanged(pos)
            itemAnimator = preAnimator
        }
    }

    private fun showPermissionAllFileDialog(needSmartName: Boolean = false, selectedMediaIdList: MutableList<Long>? = null) {
        if (ClickUtils.isFastDoubleClick()) {
            return
        }
        activity?.let {
            mFilePermissionDialog = PermissionDialogUtils.showPermissionAllFileAccessDialog(
                it,
                object : PermissionDialogUtils.PermissionDialogListener {
                    override fun onClick(alertType: Int, isOk: Boolean, permissions: ArrayList<String>?) {
                        DebugUtil.d(TAG, "showPermissionAllFileDialog, isOk:$isOk, permissions:$permissions")
                        if (isOk) {
                            if (needSmartName) {
                                mBrowseViewModel.addNeedSmartNameMedias(selectedMediaIdList)
                            }
                            PermissionUtils.goToAppAllFileAccessConfigurePermissions(it)
                        }
                    }
                })
        }
    }

    private fun onFileObserver(event: Int, path: String?, allPath: String?) {
        val playPath = mBrowseViewModel.fastPlayHelper.itemBrowseRecordViewModel?.displayName
        val dataPath = mBrowseViewModel.fastPlayHelper.itemBrowseRecordViewModel?.data
        when (event) {
            FileObserver.DELETE -> {
                mBrowseViewModel.release(playPath, dataPath, path, allPath)
                mBrowseViewModel.deleteMuteCache(allPath)
                mBrowseViewModel.deleteOShareDBData(allPath)
                mBrowseFileActivityViewModel.isNeedRefresh.postValueSafe(true)
                mBrowseFileActivityViewModel.isFileChanged = true
            }

            FileObserver.MOVED_FROM -> {
                mBrowseViewModel.release(playPath, dataPath, path, allPath)
                mBrowseViewModel.deleteOShareDBData(allPath)
                mBrowseFileActivityViewModel.isNeedRefresh.postValueSafe(true)
                mBrowseFileActivityViewModel.isFileChanged = true
            }

            FileObserver.DELETE_SELF -> {
                mBrowseViewModel.release(playPath, dataPath, path, allPath)
                mBrowseViewModel.deleteMuteCache(allPath)
                mBrowseViewModel.deleteAllOShareDBData(allPath)
                mBrowseFileActivityViewModel.isNeedRefresh.postValueSafe(true)
                mBrowseFileActivityViewModel.isFileChanged = true
            }

            FileObserver.MOVE_SELF -> {
                mBrowseViewModel.deleteAllOShareDBData(allPath)
                mBrowseFileActivityViewModel.isNeedRefresh.postValueSafe(true)
                mBrowseFileActivityViewModel.isFileChanged = true
            }

            FileObserver.ATTRIB,
            FileObserver.MOVED_TO,
            FileObserver.CREATE -> {
                mBrowseFileActivityViewModel.isNeedRefresh.postValueSafe(true)
                mBrowseFileActivityViewModel.isFileChanged = true
            }
        }

        CenterFileChangeObserver.onFileChange(event, path, allPath ?: "")
    }

    private fun notifyConvertStatusUpdate(intent: Intent?) {
        DebugUtil.d(TAG, "notifyConvertStatusUpdate")
        ConvertingInfo.onConvertStatusChanged(intent)
        /*
        较长时间的录音文件在开启转文本任务时，延迟一下列表的更新。否则在大屏模式下，从“音频”页面点击“转文本”切换到 “文本”页面时，出现明显卡顿
         */
        val bStartConvert = intent?.getBooleanExtra(KEY_NOTIFY_CONVERT_STATUS, false) ?: false
        if (bStartConvert && view != null) {
            viewLifecycleOwnerLiveData.value?.let {
                it.lifecycleScope.launch(Dispatchers.Default) {
                    delay(DELAY_TIME_300)
                    refreshData()
                }
            }
        } else {
            refreshData()
        }
    }

    private fun whenMediaChangeRelease() {
        val path = StorageManager.getInstance(context).getExternalPath(context)
        DebugUtil.d(TAG, "whenMediaChangeRelease externalPath:$path")
        path?.let {
            if (mBrowseViewModel.fastPlayHelper.itemBrowseRecordViewModel?.data?.contains(it) == true) {
                mBrowseViewModel.releasePlayer()
            }
        }
        mNavigationViewManager?.dismissDeleteDialog()
        mNavigationViewManager?.dismissRecoverDialog()
        mNavigationViewManager?.dismissDeleteAllDialog()
    }

    private fun registerReceiver() {
        if (mReceiverUtils == null) {
            mReceiverUtils = activity?.let { ReceiverUtils(it.applicationContext, mBrowseFileActivityViewModel.isFromOtherApp) }
            mReceiverUtils?.registerAllBrowseFileReceiver()
            mReceiverUtils?.setOnReceive(this)
        }
    }

    private fun unregisterReceiver() {
        mReceiverUtils?.unRegisterAllBrowseFileReceiver()
        mReceiverUtils = null
    }

    /**
     * When record filter is the default value,
     * if you enter the call choose call filter, choose all filter
     */
    private fun setBrowseViewModel() {
        createViewModel()
        taskId = activity?.taskId ?: 0
        DebugUtil.i(TAG, "taskId : $taskId")
        mBrowseViewModel.taskId = taskId
        ensureLiveDataParams()
        //initRecordGroupView()

        mSubMenuCheckedPosition = PrefUtil.getInt(activity, KEY_RECORD_CALL_GROUP_SORT, CALL_RECORD_CONTACT_GROUPING)
        mBrowseViewModel.mCallRecordGroupPosition = mSubMenuCheckedPosition
    }

    private fun initRecordGroupView() {
        when (mBrowseViewModel.getCurrentGroup().mGroupType) {
            INT_DEFAULT_NONE -> checkRecordGroupView(false)

            INT_DEFAULT_CALLING, INT_DEFAULT_RECENTLY_DELETED -> mBinding.gradientBackground.isVisible = false
            INT_DEFAULT_ALL -> {
                mBinding.gradientBackground.isVisible = true
                checkRecordGroupView(false)
            }

            else -> mBinding.gradientBackground.isVisible = false
        }
    }

    private fun getLiveDataCallGroup(groupInfos: MutableList<GroupInfo>?): GroupInfo? {
        groupInfos?.forEach {
            if (it.isCallingGroup()) {
                return it
            }
        }
        return genDefaultCallGroupInfo()
    }

    private fun getGroupByGroupUuid(groupInfos: MutableList<GroupInfo>?): GroupInfo? {
        groupInfos?.forEach {
            if (it.mUuId == mCurrentGroupUuId) {
                return it
            }
        }
        return GroupInfoDbUtil.genDefaultAllGroupInfo()
    }

    /**
     * 更新下录音分组的逻辑
     */
    private fun checkRecordGroupView(isNewIntent: Boolean) {
        DebugUtil.d(TAG, "checkRecordGroupView:$isNewIntent")
        val isShowCall = activity?.intent.isFromCall() || activity?.intent.isThreeRecordJumpToCall()
        val groupList = mBrowseViewModel.liveDataGroupList.value
        val group = if (isShowCall) {
            getLiveDataCallGroup(groupList)
        } else {
            if (isNewIntent) {
                if (mBrowseViewModel.currentGroup.value != null) {
                    mBrowseViewModel.getCurrentGroup()
                } else {
                    getGroupByGroupUuid(groupList)
                }
            } else {
                getGroupByGroupUuid(groupList)
            }
        }
        val gradientVisible = if (ItemBrowseRecordViewModel.liveEditMode[taskId]?.value == true
            || (group?.isCallingGroup() == true) || (group?.isRecentlyDeleteGroup() == true)
        ) {
            false
        } else {
            !isShowCall && !isRecordFilterCall(group)
        }
        //mBrowseViewModel.recordFilter.value = recordFilter
        mBrowseViewModel.currentGroup.value = group
        mBinding.gradientBackground.isVisible = gradientVisible
        ItemBrowseRecordViewModel.liveAddFooter[taskId]?.value = 0
    }

    private fun isRecordFilterCall(group: GroupInfo?): Boolean {
        return group?.isCallingGroup() ?: false
    }

    private fun createViewModel() {
        mBrowseViewModel = ViewModelProvider(this)[BrowseViewModel::class.java]
        mBrowseViewModel.initLifecycle(this)
    }

    private fun ensureLiveDataParams() {
        if (ItemBrowseRecordViewModel.liveEditMode[taskId] == null) {
            ItemBrowseRecordViewModel.liveEditMode[taskId] = MutableLiveData<Boolean>()
        }
        if (ItemBrowseRecordViewModel.liveDragMode[taskId] == null) {
            ItemBrowseRecordViewModel.liveDragMode[taskId] = MutableLiveData<Boolean>()
        }
        if (ItemBrowseRecordViewModel.liveSelectedMap[taskId] == null) {
            ItemBrowseRecordViewModel.liveSelectedMap[taskId] = MutableLiveData()
        }
        if (ItemBrowseRecordViewModel.liveAddFooter[taskId] == null) {
            ItemBrowseRecordViewModel.liveAddFooter[taskId] = MutableLiveData<Int>()
        }
    }

    private fun refreshGroups() {
        activity?.let {
            val nextAction = PermissionUtils.getNextAction()
            DebugUtil.e(TAG, "refreshGroups nextAction: $nextAction")
            if (nextAction > SHOULD_SHOW_USER_NOTICE) {
                mBrowseViewModel.queryAllGroups(it)
            }
        }
    }

    private fun observe() {
        mBrowseViewModel.liveDataList.observe(viewLifecycleOwner, Observer {
            onDataChanged(it)
            // onDataChanged执行后，再给allRecordCount赋值，通知右侧页面刷新，避免无数据新录制音频回来右侧由未选中录音-->音频详情
            mBrowseFileActivityViewModel.allRecordCount.postValueSafe(mBrowseViewModel.getShowRecordCount())
            if (needClearPlayData) {
                /*切换分组时，需更新allRecordCount后，再清除PlayData*/
                needClearPlayData = false
                mBrowseFileActivityViewModel.clearPlayRecordData()
            }
        })

        mBrowseViewModel.startFileObserver(BrowseFileEventListener(this))
        ItemBrowseRecordViewModel.liveEditMode[taskId]?.observe(
            viewLifecycleOwner,
            Observer { isEditMode: Boolean? ->
                updateEditModeChange(isEditMode)
                mBehavior?.onListScroll()
                mBrowseViewModel.refreshEnable(mBinding.bounceLayout)
                setNavigationColor()
            })

        ItemBrowseRecordViewModel.liveSelectedMap[taskId]?.observe(
            viewLifecycleOwner,
            Observer {
                updateSelectMap()
            })
        mBrowseViewModel.browseModel.liveViewStatus.observe(
            viewLifecycleOwner,
            Observer { viewStatus: ViewStatus? ->
                updateViewStatus(viewStatus)
            })
        mBrowseViewModel.needShareWaitingDialog.observe(viewLifecycleOwner) {
            if (it.first) {
                when (it.second) {
                    is ShareTypeNote -> showShareWaitingDialog(com.soundrecorder.common.R.string.is_saving)
                    is ShareTypeLink -> showShareWaitingDialog(com.soundrecorder.common.R.string.generating)
                    else -> showShareWaitingDialog()
                }
            } else {
                dismissShareWaitingDialog()
            }
        }

        mBrowseViewModel.showShareLinkPanel.observe(viewLifecycleOwner) {
            DebugUtil.d(TAG, "observe showShareLinkPanel $it ${mBrowseViewModel.currentLifecycleState}")
            if (it?.isNotEmpty() == true && mBrowseViewModel.currentLifecycleState == Lifecycle.Event.ON_RESUME) {
                activity?.let { activity ->
                    Injector.injectFactory<ShareAction>()?.showShareLinkPanel(activity, it, null)
                    mBrowseViewModel.showShareLinkPanel.value = null
                }
            }
        }

        mBrowseViewModel.showShareToast.observe(viewLifecycleOwner) {
            DebugUtil.d(TAG, "observe showShareToast $it ${mBrowseViewModel.currentLifecycleState}")
            if (it != null && mBrowseViewModel.currentLifecycleState == Lifecycle.Event.ON_RESUME) {
                ToastManager.showShortToast(context, it)
                mBrowseViewModel.showShareToast.value = null
            }
        }

        initSerachObservers()
        initOtherObservers()
        initGroupObservers()
        initSpeakerMenuObservers()
        initBrowseFileActivityObservers()
        initAudioMediaContentObserver()
        initUiStatusObsers()
    }

    private fun initAudioMediaContentObserver() {
        if (mMusicMediaStoreContentObserver == null) {
            mMusicMediaStoreContentObserver = MediaStoreContentObserver(this)
            BaseApplication.getAppContext().contentResolver?.registerContentObserver(
                MediaStore.Audio.Media.EXTERNAL_CONTENT_URI,
                true,
                mMusicMediaStoreContentObserver as ContentObserver
            )
        }
    }

    private fun unRegisterAudioMediaContentObserver() {
        mMusicMediaStoreContentObserver?.let {
            BaseApplication.getAppContext().contentResolver?.unregisterContentObserver(it)
            mMusicMediaStoreContentObserver = null
        }
    }

    private fun initGroupObservers() {
        mBrowseViewModel.liveDataGroupList.observe(viewLifecycleOwner, Observer {
            if (it.isNullOrEmpty()) {
                DebugUtil.e(TAG, " mBrowseViewModel.liveDataGroupList.observe liveDataGroupList is :$it")
            } else {
                labelAdapter?.refresh(it)
                updateCurrentDefaultGroup(it)
            }
        })
        mBrowseViewModel.currentGroup.observe(viewLifecycleOwner, Observer { currentGroup ->
            mGroupViewModel.currentGroup.value = currentGroup
            checkCurrentGroup(currentGroup)
        })
        mBrowseFileActivityViewModel.currentGroup.observe(viewLifecycleOwner) {
            if (it != null) {
                mBrowseViewModel.currentGroup.value = it
            }
        }
        mBrowseFileActivityViewModel.liveDataGroupList.observe(viewLifecycleOwner) {
            if (it != null) {
                mBrowseViewModel.liveDataGroupList.value = it
            }
        }
        mBrowseFileActivityViewModel.isGroupListShow.observe(viewLifecycleOwner) {
            if (it) {
                setBrowseFragmentAccessibility(View.IMPORTANT_FOR_ACCESSIBILITY_NO_HIDE_DESCENDANTS)
            } else {
                setBrowseFragmentAccessibility(View.IMPORTANT_FOR_ACCESSIBILITY_YES)
            }
        }
        mBrowseFileActivityViewModel.mCallGroupMoreData.observe(viewLifecycleOwner) {
            if (it != null) {
                setBrowseFragmentAccessibility(View.IMPORTANT_FOR_ACCESSIBILITY_NO_HIDE_DESCENDANTS)
            } else {
                setBrowseFragmentAccessibility(View.IMPORTANT_FOR_ACCESSIBILITY_YES)
            }
        }
    }

    private fun initOtherObservers() {
        ItemBrowseRecordViewModel.liveAddFooter[taskId]?.observe(viewLifecycleOwner, Observer {
            mBinding.mListView.removeCallbacks(updateFooterRunnable)
            //虽然addFooter后不用notifyDataChange, 还是采用了postDelay的方式，因为冷启动后直接进编辑模式，navigationView的measureHeight拿不到导致最后一条被遮住一部分
            mBinding.mListView.postDelayed(updateFooterRunnable, it.toLong())
        })

        mBrowseViewModel.behaviorCurrentStatus.observe(viewLifecycleOwner, Observer { status ->
            if ((mBrowseViewModel.lastSetBehaviorStatus != status)) {
                changeBehaviorSubTitle(status)
            }
        })
    }

    private fun initSerachObservers() {
        mBrowseViewModel.showSearch.observe(viewLifecycleOwner, Observer { showSearch ->
            mBinding.browseToolbar.post {
                if (showSearch) {
                    findSearchFragment()
                    if (mBrowseViewModel.showSearchUseMaxHeight) {
                        mSearchFragment?.inSearch(true)
                    } else {
                        mSearchFragment?.inSearch()
                    }
                } else {
                    if (::mAdapter.isInitialized) {
                        mAdapter.notifyDataSetChanged()
                    }
                    mBrowseViewModel.showSearchUseMaxHeight = false
                }
                changeBehaviorCurrentStatusByScreenHeight(showSearch)
            }
            if (!showSearch) {
                showTipsSame()
                mSearchFragment = null
            }
            (activity as? IBrowseFileActivityListener)?.onRecordSearchStateChanged(
                showSearch,
                !mBrowseViewModel.searchValue.value.isNullOrBlank()
            ) {
                onClickCancel()
            }
        })
        mBrowseViewModel.searchValue.observe(viewLifecycleOwner, Observer { searchValue ->
            mBinding.browseToolbar.post {
                findSearchFragment()
                mSearchFragment?.onQueryTextChange(searchValue)
            }
            (activity as? IBrowseFileActivityListener)?.onRecordSearchStateChanged(
                mBrowseViewModel.showSearch.value ?: false,
                !searchValue.isNullOrBlank()
            ) {
                onClickCancel()
            }
        })
    }

    /**
     * 是否选中通话录音，且按联系人分组显示
     */
    private fun isGroupingByContact(): Boolean {
        return mCurrentGroup?.isCallingGroup() == true && (mSubMenuCheckedPosition == CALL_RECORD_CONTACT_GROUPING)
    }

    private fun initBrowseFileActivityObservers() {
        mBrowseFileActivityViewModel.windowType.observe(viewLifecycleOwner) {
            updateToolBarMenu()
            onWindowTypeChanged(it)
        }
        mBrowseFileActivityViewModel.mCurrentPlayRecordData.observe(viewLifecycleOwner) {
            handleSearchViewFocus()
        }
        mBrowseFileActivityViewModel.beDeleteSummaryNoteId.observe(viewLifecycleOwner) {
            mBrowseViewModel.updateDataLiveDataByClearNoteId(it)
        }
        mBrowseFileActivityViewModel.isCallingRecordEditRefresh.observe(viewLifecycleOwner, Observer {
            DebugUtil.d(TAG, "isCallingRecordEditRefresh:$it")
            if (it) {
                refreshData()
            }
        })
        mBrowseFileActivityViewModel.isShowLoadingWithRefresh.observe(viewLifecycleOwner, Observer {
            DebugUtil.d(TAG, "isShowLoadingWithRefresh:$it")
            if (::mAdapter.isInitialized && mBinding.mListView.scrollState == RecyclerView.SCROLL_STATE_IDLE) {
                mAdapter.clear()
                mAdapter.notifyDataSetChanged()
            }
            if (it) {
                refreshData()
            }
        })
    }

    private fun checkCurrentGroup(currentGroup: GroupInfo) {
        if (mCurrentGroup?.mUuId != currentGroup.mUuId) {
            needClearPlayData = true
        }
        mCurrentGroup = currentGroup
        mCurrentGroupUuId = currentGroup.mUuId
        DebugUtil.d(
            TAG, "checkCurrentGroup, groupType:${currentGroup.mGroupType}," +
                    " groupName:${currentGroup.mGroupName}"
        )
        PrefUtil.putInt(BaseApplication.getAppContext(), KEY_RECORD_GROUP_ID, currentGroup.mId)
        PrefUtil.putString(BaseApplication.getAppContext(), KEY_RECORD_GROUP_UUID, currentGroup.mUuId)
        labelAdapter?.setCurrentGroup(currentGroup)
        labelAdapter?.scrollToCheckedPosition()
        showRecorderAndTitle(currentGroup)
        mTipStatusObserver?.mCurrentGroup = currentGroup
        when (currentGroup.mGroupType) {
            //通话录音
            INT_DEFAULT_CALLING -> showCallGroupMenu()
            else -> hideCallGroupMenu()
        }
        resetRecycleDialogShowing()
        ItemBrowseRecordViewModel.liveAddFooter[taskId]?.value = 0
        mBrowseViewModel.selectGroup()
    }

    private fun onWindowTypeChanged(windowType: WindowType) {
        when (windowType) {
            WindowType.SMALL -> {
                // 小屏，若有播放内容，则退出编辑模式，取消各种弹窗
                exitEditModeAndDialogInSmallWindow(windowType)
            }

            WindowType.MIDDLE, WindowType.LARGE -> {
                // 小屏切中大屏，快捷播放接续
                mBrowseViewModel.fastPlayHelper.itemBrowseRecordViewModel?.let {
                    if (it.isNeedShowSeekBarArea(mBrowseViewModel.fastPlayHelper.getPlayerState())) {
                        mBrowseFileActivityViewModel.mCurrentPlayRecordData.value = it.toStartPlayModel(
                            mBrowseFileActivityViewModel.isFromOtherApp,
                            mBrowseViewModel.fastPlayHelper.getCurrentPlayerTime(),
                            mBrowseViewModel.fastPlayHelper.mediaPlayerManager.isWholePlaying(),
                            recordFilterIsRecycle()
                        )
                        mBrowseViewModel.releasePlayer()
                    }
                }
            }
        }
        /*小屏，有播放页面，若首页搜索存在且关键词为空，则退出搜索页面*/
        if (canRemoveSearchFragmentWhenSmallWindow()) {
            findSearchFragment()
            mSearchFragment?.onClickCancel()
        }
        handleSearchViewFocus()
    }

    /**
     * 处理搜索view的焦点问题，避免小屏，列表-搜索-详情，重建页面时再详情页弹出了软键盘
     */
    private fun handleSearchViewFocus() {
        /**
         * 小屏 + 有播放,通过enable方式收起软键盘
         * 小屏 + 无播放，恢复enable状态
         * 中大 + 有播放  恢复enable状态*/
        val searchView = mCacheHolder.mSearchAnimView
        val searchEdit = searchView?.searchEditText
        if ((mBrowseViewModel.showSearch.value == true) && (mBrowseFileActivityViewModel.hasPlayPageData())
            && (ScreenUtil.isSmallScreen(context))
        ) {
            searchEdit?.isEnabled = false
        } else if (searchEdit?.isEnabled == false) {
            searchEdit.isEnabled = true
            if (mBrowseViewModel.showSearch.value == true) {
                searchEdit.setSelection(searchEdit.text.length)
                if (searchView.height == 0) {
                    // 确保布局完成后调整高度
                    mBinding.browseToolbar.post {
                        searchView.updateLayoutParams<ViewGroup.LayoutParams> {
                            height = mBinding.browseToolbar.measuredHeight - mBinding.browseToolbar.paddingTop
                        }
                    }
                }
            }

            // 若有搜索页面，需要弹出软键盘
            if (mBrowseViewModel.showSearch.value == true) {
                mCacheHolder.mSearchAnimView?.openSoftInput(true)
            }
        } else { // do nothing
        }
    }

    private fun exitEditModeAndDialogInSmallWindow(windowType: WindowType?) {
        val needClearDialog = (mBrowseFileActivityViewModel.hasPlayPageData()) && (windowType == WindowType.SMALL)
        val exitEditMode = needClearDialog && (ItemBrowseRecordViewModel.liveEditMode[taskId]?.value == true)
        activity?.let {
            if (ActivityTaskUtils.isTaskEmptyExceptActivity(taskId, it)) {
                if (needClearDialog) {
                    //全部录音弹窗
                    mBrowseViewModel.isSingleDialogShowing = false
                    mTipStatusObserver?.releaseDialog()
                }

                if (exitEditMode) {
                    // 销毁设为铃声、分享三方activity
                    activity?.finishActivity(SendSetUtil.REQUEST_CODE_SET_RING)
                    activity?.finishActivity(FileDealUtil.REQUEST_CODE_SHARE)
                    //重命名弹窗
                    mNavigationViewManager?.releaseRenameDialog()
                    mBrowseViewModel.isRenameDialogShowing = false
                    // 删除弹窗
                    mNavigationViewManager?.dismissDeleteDialog()
                    mBrowseViewModel.isDeleteDialogShowing = false
                    mNavigationViewManager?.dismissRecoverDialog()
                    mBrowseViewModel.isRecoverDialogShowing = false
                    mBrowseViewModel.isRecycleDialogAllShowing = false
                    //分享弹窗
                    mNavigationViewManager?.dismissShareDialog()
                    mBrowseViewModel.isShareDialogShowing = false
                    //文本分享弹窗
                    mNavigationViewManager?.dismissShareTextDialog()
                    mBrowseViewModel.isShareTextDialogShowing = false
                    disableDialog.dismissWhenShowing()
                    // 退出编辑模式
                    mBrowseViewModel.exitEditMode()
                }
            }
        }
    }

    fun scrollToPlayItemPosition() {
        if (!mBrowseFileActivityViewModel.isSmallWindow() && (mBrowseFileActivityViewModel.mCurrentPlayRecordData.value?.isFromSearch == true)) {
            val position = mBrowseViewModel.findPositionByMediaId(mBrowseFileActivityViewModel.mCurrentPlayRecordData.value?.mediaId)
            DebugUtil.i(TAG, "scrollToPlayItemPosition position $position")
            if (position != -1) {
                mBinding.mListView.post {
                    (mBinding.mListView.layoutManager as LinearLayoutManager).scrollToPositionWithOffset(
                        (position + 1), mBinding.browseToolbar.height
                    )
                }
            }
        }
    }

    private val updateFooterRunnable = Runnable {
        addBlankFooter()
    }

    private fun scrollToTop() {
        mBinding.mListView.post {
            mBinding.mListView.scrollToPosition(0)
        }
    }

    private fun updateSelectMap() {
        if (ItemBrowseRecordViewModel.liveEditMode[taskId]?.value == true) {
            val selectedCount = ItemBrowseRecordViewModel.liveSelectedMap[taskId]?.value?.size ?: 0
            updateNavigationItemEnable(selectedCount)
            updateSelectAllMenu(selectedCount)

            /* 修复bug:
             * 因分组label的布局，导致录音列表第一屏数据最后一个item未显示在屏幕内； 在每次进入录音或者录音列表刷新后，
             * 此时进入编辑状态，如果设置为全选时，第一屏数据最后一个item滚动到可见屏幕区域时，checkbox显示为非选中状态
             * 解决方案：更新列表选中状态时，如果列表显示的是第一屏数据，则查找到第一屏的最后一个Item并再更新一次
             */
            mBinding.mListView.layoutManager?.let {
                val linearLayoutManager = it as LinearLayoutManager
                val firstVisibleItemPosition = linearLayoutManager.findFirstVisibleItemPosition()
                if (firstVisibleItemPosition == 0) {
                    //firstVisibleItemPosition == 0 表示列表显示为第一屏数据，此时才查找第一屏的最后一个Item
                    val lastVisibleItemPosition = linearLayoutManager.findLastVisibleItemPosition()
                    val viewType = mAdapter.getItemViewType(lastVisibleItemPosition)
                    if (lastVisibleItemPosition > 0 && (viewType == BrowseAdapter.TYPE_BROWSE || viewType == BrowseAdapter.TYPE_CALL_CONTACT_GROUP)) {
                        /*BrowseAdapter position = 0 时， viewType 为 TYPE_PLACEHOLDER，
                         所以lastVisibleItemPosition + 1 才是列表第一屏最后一个item的index
                         */
                        val itemIndex = lastVisibleItemPosition + 1
                        mAdapter.notifyItemChanged(itemIndex)
                    }
                }
            }
        }
    }

    private fun updateEditModeChange(isEditMode: Boolean?) {
        DebugUtil.i(logTag, "updateEditModeChange >> $isEditMode")
        val isEdit = isEditMode ?: false
        updateToolBarMenu()
        updateTitleEditModeChange(isEdit)
        resetRecycleDialogShowing(isEdit)
        ensureNavigationView()
        startRecordLayoutAnim(
            isEditMode, mBrowseViewModel.getCurrentGroup()
        )

        switchToNavigationMenu(isEditMode)
        //showTrigon()
        updateChipEditModeChange(isEdit)
    }

    private fun updateChipEditModeChange(isEdit: Boolean) {
        labelAdapter?.setEdit(isEdit)
        val chipIconResId = com.soundrecorder.common.R.drawable.group_label_default_icon
        mBinding.groupLabelContainer.chipEntrance.apply {
            isEnabled = !isEdit
            chipIcon = activity?.let {
                ContextCompat.getDrawable(
                    it,
                    chipIconResId
                )
            }
        }
    }

    private fun resetRecycleDialogShowing(isEdit: Boolean) {
        if (!isEdit) {
            mBrowseViewModel.isRecoverDialogShowing = false
            mBrowseViewModel.isDeleteDialogShowing = false
        }
    }

    private fun updateTitleEditModeChange(isEditMode: Boolean?) {
        if (isEditMode == true) {
            mBinding.browseToolbar.title = resources.getString(com.soundrecorder.common.R.string.choose_item)
            mBinding.toolbarTitle.setText(com.soundrecorder.common.R.string.choose_item)
            mBinding.toolbarSubtitle.visibility = View.INVISIBLE
            mBinding.toolbarTitle.isClickable = false
        } else {
            mBinding.toolbarTitle.isClickable = true
            mBinding.toolbarTitle.setText(com.soundrecorder.common.R.string.app_name_main)
            mBinding.browseToolbar.title =
                resources.getString(com.soundrecorder.common.R.string.app_name_main)
            mBinding.toolbarSubtitle.visibility = View.VISIBLE
        }
        mBehavior?.isEditMode = isEditMode ?: false
    }

    private fun updateToolBarMenu(delayTime: Long = 0L) {
        mBinding.browseToolbar.removeCallbacks(updateToolBarMenuRunnable)
        if (delayTime <= 0) {
            updateToolBarMenuRunnable.run()
        } else { //延迟消失，防止点击menu进入编辑模式后，menu未消失前item为空
            mBinding.browseToolbar.postDelayed(updateToolBarMenuRunnable, delayTime)
        }
        updateSelectMap()
    }

    private val updateToolBarMenuRunnable = Runnable {
        setMenuSearchVisible()
        setMenuSpeakModeVisible()
        //setMenuSettingMoreVisible setMenuSelectAllVisible调整调用顺序，
        // 进入编辑模式时先隐藏普通菜单，否则“取消”菜单显示异常（距左无间距）
        setMenuSettingMoreVisible()
        setMenuSelectAllVisible()
        mBinding.browseToolbar.isTitleCenterStyle =
            ItemBrowseRecordViewModel.liveEditMode[taskId]?.value == true

        setCallGroupMenuVisible()
    }

    private fun setCallGroupMenuVisible() {
        val isEditMode = ItemBrowseRecordViewModel.liveEditMode[taskId]?.value ?: false
        if (isEditMode) {
            mOptionsMenu?.setGroupVisible(R.id.group_1, false)
        } else {
            if (mCurrentGroup?.isCallingGroup() == true) {
                mOptionsMenu?.setGroupVisible(R.id.group_1, true)
            } else {
                mOptionsMenu?.setGroupVisible(R.id.group_1, false)
            }
        }
    }

    /**
     * 标题菜单栏-搜索图标
     */
    private fun setMenuSearchVisible() {
        val isEditMode = ItemBrowseRecordViewModel.liveEditMode[taskId]?.value ?: false
        val bVisible = !isEditMode && PermissionUtils.hasReadAudioPermission()
        mOptionsMenu?.findItem(R.id.item_search)?.isVisible = bVisible
    }

    /**
     * 标题菜单栏-听筒、扬声器切换图标
     */
    private fun setMenuSpeakModeVisible() {
        val isEditMode = ItemBrowseRecordViewModel.liveEditMode[taskId]?.value ?: false
        //听筒、扬声器图标显示条件：只要非回收站分组有数据，就需要显示
        val canShowMenu =
            !FeatureOption.IS_PAD && !isEditMode && PermissionUtils.hasReadAudioPermission()
        mOptionsMenu?.findItem(R.id.item_speaker)?.isVisible = canShowMenu && (mBrowseFileActivityViewModel.isSmallWindow())
    }

    /**
     * 标题菜单栏-选中全部
     */
    private fun setMenuSelectAllVisible() {
        val isEditMode = ItemBrowseRecordViewModel.liveEditMode[taskId]?.value ?: false
        val height = mBinding.browseToolbar.measuredHeight
        //进入编辑模式时menu显示为“取消”、“全选”纯文本时Toolbar高度会比之前低，会导致“全部录音”文案向上跳动
        mBinding.browseToolbar.minimumHeight = if (isEditMode && height > 0) {
            height
        } else {
            BaseApplication.getAppContext().resources.getDimensionPixelOffset(com.soundrecorder.common.R.dimen.recorder_toolbar_height)
        }
        mOptionsMenu?.findItem(R.id.item_select_all)?.isVisible = isEditMode
        mOptionsMenu?.findItem(R.id.item_cancel)?.isVisible = isEditMode
    }

    /**
     * 标题菜单栏-设置更多
     */
    private fun setMenuSettingMoreVisible() {
        val isEditMode = ItemBrowseRecordViewModel.liveEditMode[taskId]?.value ?: false
        val canShowMenu = !isEditMode && PermissionUtils.hasReadAudioPermission() && mBrowseViewModel.getShowRecordCount() > 0
        if (ScreenUtil.isSmallScreen(context)) {
            mOptionsMenu?.findItem(R.id.item_setting_big)?.isVisible = false
            mOptionsMenu?.findItem(R.id.item_edit_big)?.isVisible = false
            mOptionsMenu?.findItem(R.id.item_setting)?.isVisible = !isEditMode
            mOptionsMenu?.findItem(R.id.item_edit)?.isVisible = canShowMenu
        } else {
            mOptionsMenu?.findItem(R.id.item_setting)?.isVisible = false
            mOptionsMenu?.findItem(R.id.item_edit)?.isVisible = false
            mOptionsMenu?.findItem(R.id.item_setting_big)?.isVisible = !isEditMode
            mOptionsMenu?.findItem(R.id.item_edit_big)?.isVisible = canShowMenu
        }
    }

    private fun updateSelectAllMenu(selectedCount: Int) {
        val totalCount = mBrowseViewModel.getShowRecordCount()
        DebugUtil.i(TAG, "updateSelectAllMenu totalCount = $totalCount, selectedCount=$selectedCount")
        val isSelectAll = selectedCount >= totalCount
        val title = if (selectedCount == 0) {
            getString(com.soundrecorder.common.R.string.choose_item)
        } else if (isSelectAll) {
            getString(com.soundrecorder.common.R.string.selected_all_item)
        } else {
            getString(com.soundrecorder.common.R.string.item_select, selectedCount)
        }
        mBinding.toolbarTitle.text = title
        mBinding.browseToolbar.title = title

        val selectAllMenu = mOptionsMenu?.findItem(R.id.item_select_all)
        selectAllMenu?.let {
            it?.isEnabled = totalCount > 0
            if (isSelectAll) {
                it.setTitle(com.soundrecorder.common.R.string.record_delete_all_cancel)
            } else {
                it.setTitle(com.soundrecorder.common.R.string.select_all)
            }
        }
    }

    private fun switchToNavigationMenu(isEditMode: Boolean?, isShowing: Boolean = true) {
        val menuSize = mBottomNavigationView?.menu?.size ?: 0
        DebugUtil.d(TAG, "switchToNavigationMenu, menuSize:$menuSize,")
        var isSwitchToMenu = false
        if (recordFilterIsRecycle()) {
            if (isEditMode == true) {
                if (menuSize != MENU_SIZE_NUM_2) {
                    isSwitchToMenu = true
                    mBottomNavigationView?.switchToMenu(R.menu.bottom_navigation_recycle_menu, false)
                }
            } else {
                if (menuSize != MENU_SIZE_NUM_1 && mBrowseFileActivityViewModel.isDialogDeleteAll.value != true) {
                    isSwitchToMenu = true
                    mBottomNavigationView?.switchToMenu(R.menu.bottom_navigation_delete_all_menu, false)
                }
            }
        } else {
            if (menuSize != MENU_SIZE_NUM_5) {
                isSwitchToMenu = true
                mBottomNavigationView?.switchToMenu(R.menu.bottom_navigation_menu, false)
            }
            if (isShowing) {
                mBottomMarginView?.setDisplay(isEditMode == true)
            }
        }
        DebugUtil.d(TAG, "switchToNavigationMenu, isSwitchToMenu:$isSwitchToMenu")
        if (isSwitchToMenu) {
            mBrowseFileActivityViewModel.isDialogDeleteAll.value = false
        }
        onCreateOptionsMenuCheckRestoreWindow()

        val selectedCount = ItemBrowseRecordViewModel.liveSelectedMap[taskId]?.value?.size ?: 0
        DebugUtil.d(TAG, "switchToNavigationMenu, selectedCount:$selectedCount")
        updateNavigationItemEnable(selectedCount)
    }

    private fun setBottomNavigationHeight() {
        val lp = mNavigationLayout?.layoutParams as? FrameLayout.LayoutParams
        lp?.height = resources.getDimensionPixelSize(com.soundrecorder.common.R.dimen.bottom_tool_navigation_height)
    }

    private fun BottomMarginView.setDisplay(isDisplay: Boolean) {
        if (isDisplay && mNavigationLayout?.visibility == View.VISIBLE) {
            DebugUtil.d(TAG, "BottomMarginView, setDisplay is visible")
            return
        }
        DebugUtil.d(TAG, "BottomMarginView.setDisplay:$isDisplay")
        mNavigationAnim?.end()

        val marginBottomValue = -1 * resources.getDimensionPixelSize(com.soundrecorder.common.R.dimen.tool_navigation_translation)
        DebugUtil.d(TAG, "setDisplay, marginBottomValue:$marginBottomValue")
        mNavigationAnim = ObjectAnimator.ofInt(
            this,
            NAV_ANIM_PROPERTY_NAME,
            if (isDisplay) marginBottomValue else 0,
            if (isDisplay) 0 else marginBottomValue
        ).apply {
            duration = if (isDisplay) TOOL_NAV_SHOW_ANIM_DURATION else TOOL_NAV_DISMISS_ANIM_DURATION
            interpolator = if (isDisplay) toolNaShowAnimInterpolator else toolNavDismissAnimInterpolator
        }

        AnimatorSet().apply {
            playTogether(mNavigationAnim)
            addListener(
                onEnd = {
                    if (!isDisplay) mNavigationLayout?.visibility = View.GONE
                }, onStart = {
                    if (isDisplay) mNavigationLayout?.visibility = View.VISIBLE
                })
            start()
        }
    }

    private fun ensureNavigationView() {
        if ((mNavigationLayout == null) && (!mBinding.mViewStub.isInflated)) {
            mNavigationLayout = mBinding.mViewStub.viewStub?.inflate()
            mBottomNavigationView = mNavigationLayout?.findViewById(R.id.navi_menu_tool)
            //setBottomNavigationHeight()
            mNavigationLayout?.apply {
                visibility = View.GONE
            }
            mNavigationLayout?.updatePadding(bottom = nagivationHeight)
            fixTalkBackFocus()

            mBottomNavigationView?.setItemLayoutType(COUINavigationView.VERTICAL_ITEM_LAYOUT_TYPE)
            mBottomMarginView = BottomMarginView().addView(mNavigationLayout!!)

            mBottomNavigationView?.setOnItemSelectedListener(
                NavigationBarView.OnItemSelectedListener OnItemSelectedListener@{ item ->
                    if (ClickUtils.isFastDoubleClick()) {
                        DebugUtil.i(logTag, "isFastDoubleClick return")
                        return@OnItemSelectedListener true
                    }
                    DebugUtil.i(TAG, "item >> $item")
                    onItemSelected(item)
                    true
                })
            val selectedCount = ItemBrowseRecordViewModel.liveSelectedMap[taskId]?.value?.size ?: 0
            updateNavigationItemEnable(selectedCount)
        }
    }

    private fun adjustItemNumber() {
        //val maxCount = mBinding.naviMenuTool.getMaxItemCount(mContainerWidth)
        val menuSize = mBottomNavigationView?.menu?.size ?: 0
        val selectedCount = ItemBrowseRecordViewModel.liveSelectedMap[taskId]?.value?.size ?: 0
        DebugUtil.d(TAG, "adjustItemNumber, menuSize:$menuSize, selectedCount:$selectedCount")
        updateNavigationItemEnable(selectedCount)
    }

    private fun updateNavigationItemEnable(selectedCount: Int) {
        var enable: Boolean
        mBottomNavigationView?.menu?.forEach {
            when (it.itemId) {
                R.id.item_delete,
                R.id.item_recycle_recover,
                R.id.item_recycle_delete,
                R.id.item_move,
                R.id.item_send -> {
                    enable = (selectedCount > 0)
                    if (enable != it.isEnabled) {
                        it.isEnabled = enable
                    }
                }

                R.id.set_as,
                R.id.item_rename -> {
                    enable = (selectedCount == 1)
                    if (enable != it.isEnabled) {
                        it.isEnabled = enable
                    }
                }
            }
        }
    }

    private fun startRecordLayoutAnim(isEditMode: Boolean?, group: GroupInfo) {
        DebugUtil.i(logTag, "startRecordLayoutAnim >> $isEditMode")
        if (recordAnimationSet?.isRunning == true) {
            recordAnimationSet?.cancel()
        }
        val context = BaseApplication.getAppContext()
        if (isEditMode == true || group.isCallingGroup()
            || group.isRecentlyDeleteGroup()
        ) {
            if (mBinding.gradientBackground.visibility == View.VISIBLE) {
                recordAnimationSet = ItemAnimationUtil.recordLayoutDismissAnimator(
                    mBinding.middleControl,
                    mBinding.gradientBackground,
                    context
                )
            }
        } else {
            DebugUtil.d(
                TAG, "startRecordLayoutAnim: mBinding.gradientBackground.visibility2 = "
                        + "${mBinding.gradientBackground.visibility}", true
            )
            if (mBinding.gradientBackground.visibility == View.GONE) {
                recordAnimationSet = ItemAnimationUtil.recordLayoutAppearAnimator(
                    mBinding.middleControl,
                    mBinding.gradientBackground,
                    context,
                    isSmallWindowHeight()
                )
            }
        }
    }

    private fun checkSupportSmartName() {
        activity?.let {
            lifecycleScope.launch(Dispatchers.IO) {
                val convertSupport = convertSupportAction?.isSupportConvert(!mBrowseFileActivityViewModel.isFromOtherApp)
                mSupportSmartName = smartNameAction?.checkSupportSmartName(it, convertSupport) ?: false
                if (!mSupportSmartName && BaseUtil.isOtherBrand() && smartNameAction?.isSmartNameSwitchOpen(it) == true) {
                    smartNameAction?.setSmartNameSwitchStatus(it, isOpen = false, needStatistics = false)
                }
                DebugUtil.d(TAG, "initSupportSmartName, supportSmartName:$mSupportSmartName")
                withContext(Dispatchers.Main) {
                    mBrowseFileActivityViewModel.updateSupportSmartName(mSupportSmartName)
                }
            }
        }
    }

    private fun onItemSelected(item: MenuItem?) {
        if (mNavigationViewManager == null) {
            mNavigationViewManager = NavigationViewManager(activity, mBottomNavigationView)
        }
        if (mBrowseViewModel.isRenameDialogShowing) {
            mNavigationViewManager?.editDisplayName = mBrowseViewModel.renameContent
            mBrowseViewModel.renameContent = null
            mBrowseViewModel.isRenameDialogShowing = false
        }
        mNavigationViewManager?.supportSmartName = mSupportSmartName
        mNavigationViewManager?.showRecordCount = mBrowseViewModel.getShowRecordCount()
        mNavigationViewManager?.setGroupInfo(mBrowseViewModel.getCurrentGroup())
        if (recordFilterIsRecycle()) {
            mNavigationViewManager?.setAllRecordList(mBrowseViewModel.getRecycleAllRecordList())
        }
        mNavigationViewManager?.onMoveGroupListener = this.onMoveGroupListener
        //isShareTextDialogShowing是true时是重建恢复分享文本弹窗
        if (item?.itemId == R.id.item_send && mBrowseViewModel.isShareTextDialogShowing) {
            mNavigationViewManager?.restoreShareTextDialog(
                item, mBrowseViewModel.viewModelScope, mBrowseViewModel, childFragmentManager
            )
        } else {
            mNavigationViewManager?.click(
                item,
                mBrowseViewModel.viewModelScope,
                mBrowseViewModel,
                mBrowseViewModel,
                childFragmentManager
            )
        }
        mNavigationViewManager?.onOptionCompletedListener = recordOptionCompletedListener
    }

    private fun fixTalkBackFocus() {
        var firstItem: View? = null
        if ((mBottomNavigationView?.childCount ?: 0) > 0) {
            firstItem = mBottomNavigationView?.getChildAt(0)
        }
        firstItem?.contentDescription = getString(com.soundrecorder.common.R.string.send)
    }

    private fun updateViewStatus(viewStatus: ViewStatus?) {
        when (viewStatus) {
            ViewStatus.NO_PERMISSION -> showNoPermissionView()
            ViewStatus.QUERYING -> showQueryingView()
            ViewStatus.EMPTY -> {
                /*if data are all clear,exit edit mode in editMode*/
                mBrowseViewModel.exitEditMode()
                /*show empty view*/
                showEmptyView()
                setRefreshEnable(false)
                updateToolBarMenu()
            }

            ViewStatus.SHOW_CONTENT -> showContentView()
            else -> {}
        }
        //showTrigon()
    }

    private fun showContentView() {
        DebugUtil.i(logTag, "showContentView")
        mBrowseViewModel.refreshEnable(mBinding.bounceLayout)
        mBinding.mListView.visible()

        mPermissionScrollview?.visibility = View.GONE
        this.mEmptyScrollview?.visibility = View.GONE
        mLoadingViewLayout?.visibility = View.GONE
        setMenuSpeakModeVisible()
        setMenuSettingMoreVisible()
        setMenuSearchVisible()

        val isEditMode = (ItemBrowseRecordViewModel.liveEditMode[taskId]?.value ?: false)
        mBinding.toolbarSubtitle.isInvisible = isEditMode

        val showCount = if (isGroupingByContact()) {
            mBrowseViewModel.getShowCallTotalCount()
        } else {
            mBrowseViewModel.getShowRecordCount()
        }
        if (recordFilterIsRecycle()) {
            /*回收站只显示数量*/
            mBinding.toolbarSubtitle.setRecycleSubTitleText(
                resources.getQuantityString(
                    com.soundrecorder.common.R.plurals.count_record,
                    showCount, showCount
                )
            )
            mBottomMarginView?.setDisplay(true)
        } else {
            if (false == cloudTipManager?.isRefreshUIOfSync()) {
                val textPluralsId = getSubtitlePluralsResId()
                mBinding.toolbarSubtitle.setTitleText(resources.getQuantityString(textPluralsId, showCount, showCount))
            }
        }
        mTipStatusObserver?.mShowRecordCount = showCount
    }

    private fun getSubtitlePluralsResId(): Int {
        val textPluralsId = when (mBrowseViewModel.getCurrentGroup().mGroupType) {
            INT_DEFAULT_CALLING -> com.soundrecorder.common.R.plurals.count_call_record
            else -> com.soundrecorder.common.R.plurals.count_record
        }
        return textPluralsId
    }

    private fun showEmptyView() {
        DebugUtil.i(logTag, "showEmptyView")
        val act = activity ?: return
        ensureEmptyLayout()
        mEmptyScrollview?.visibility = View.VISIBLE
        if (recordFilterIsRecycle()) {
            mTvbottle?.setTextColor(ContextCompat.getColor(act, com.support.appcompat.R.color.coui_color_primary_neutral))
            mTvRecentlyBottle?.visibility = View.VISIBLE
        } else {
            mTvbottle?.setTextColor(ContextCompat.getColor(act, com.support.appcompat.R.color.coui_color_primary_neutral))
            mTvRecentlyBottle?.visibility = View.GONE
        }
        //mNavigationLayout?.visibility = View.GONE
        mBottomMarginView?.setDisplay(false)

        mPermissionScrollview?.visibility = View.GONE
        mBinding.mListView.visibility = View.GONE
        mLoadingViewLayout?.visibility = View.GONE
        mBinding.toolbarSubtitle.visibility = View.INVISIBLE

        mOptionsMenu?.findItem(R.id.item_edit)?.isVisible = false
        if (!ScreenUtil.isSmallScreen(context)) {
            mOptionsMenu?.findItem(R.id.item_edit_big)?.isVisible = false
        }

        mEmptyOSImageView?.initImageResource()
        //showEmptyView在layoutChange之后调用，所以需要再次更新图片大小
        setOtherViewBottomMargin()
    }

    private fun showQueryingView() {
        DebugUtil.i(logTag, "showQueryingView")
        setRefreshEnable(false)
        if ((::mAdapter.isInitialized && mAdapter.isContentNotEmpty())) {
            DebugUtil.i(TAG, "if list view content is not empty or empty visible, don't show loading view.")
            return
        }
        ensureLoadingView()
        mLoadingViewLayout?.visibility = View.VISIBLE
        mPermissionScrollview?.visibility = View.GONE
        this.mEmptyScrollview?.visibility = View.GONE
        mBinding.mListView.visibility = View.GONE
    }

    private fun showNoPermissionView() {
        DebugUtil.i(logTag, "showNoPermissionView")
        ensurePermissionDeniedStub()
        mPermissionScrollview?.visibility = View.VISIBLE
        mBinding.mListView.visibility = View.GONE
        mLoadingViewLayout?.visibility = View.GONE
        mEmptyScrollview?.visibility = View.GONE
        setRefreshEnable(false)
        setOtherViewBottomMargin()
    }

    private fun ensureLoadingView() {
        if ((mLoadingViewLayout == null) && (!mBinding.mLoadingStub.isInflated)) {
            mLoadingViewLayout = mBinding.mLoadingStub.viewStub?.inflate()
            mLoadingView = mLoadingViewLayout?.findViewById(com.soundrecorder.common.R.id.loadingView)
        }
    }

    private fun ensureEmptyLayout() {
        DebugUtil.i(TAG, "ensureEmptyLayout--------")
        if ((this.mEmptyScrollview == null) && (!mBinding.mEmptyViewStub.isInflated)) {
            this.mEmptyScrollview = mBinding.mEmptyViewStub.viewStub?.inflate() as? ScrollView
            mEmptyOSImageView = this.mEmptyScrollview?.findViewById(R.id.mEmptyImage)
            mTvbottle = this.mEmptyScrollview?.findViewById(R.id.bottle)
            mTvRecentlyBottle = this.mEmptyScrollview?.findViewById(R.id.tv_recently_bottle)
        }
    }

    /**
     * 设置emptyView/无权限到底部的间距
     * 分屏模式且通话录音的模式下，设置bottomMargin = 10dp
     * 其他情况设置为底部面板的高度
     */
    private fun setOtherViewBottomMargin() {
        if (this.mEmptyScrollview?.isVisible == true) {
            updateTipsViewTopMargin(this.mEmptyScrollview, mEmptyOSImageView)
        }
        if (this.mPermissionScrollview?.isVisible == true) {
            updateTipsViewTopMargin(this.mPermissionScrollview, null)
        }
    }

    private fun updateTipsViewTopMargin(rootView: ScrollView?, osImageView: OSImageView?) {
        DebugUtil.i(TAG, "setTipsViewBottomMargin ")
        if (rootView == null) {
            return
        }
        activity?.let {
            rootView.postDelayed({
                val topMargin = if ((FunctionOption.PHONE_NEED_HORIZONTAL) && (LandScapeUtil.spitWindowHeightLessThanForPlay450(it))) {
                    mBinding.browseToolbar.height
                } else {
                    mBinding.abl.height
                }
                DebugUtil.d(TAG, "updateTipsViewTopMargin, topMargin:$topMargin")
                val bottomMargin = it.resources.getDimension(R.dimen.recorder_height).toInt()

                (rootView.layoutParams as? MarginLayoutParams)?.let { params ->
                    if (params.topMargin != topMargin || params.bottomMargin != bottomMargin) {
                        params.topMargin = topMargin
                        params.bottomMargin = bottomMargin
                        rootView.layoutParams = params
                    }
                }
                osImageView?.run {
                    val windowWith = it.px2dp(rootView.width).toInt()
                    val windowHeight = SplitWindowUtil.getCurrentSplitWindowParameter(it).windowHeight
                    this.setScaleByEmptySize(windowWith, windowHeight - it.px2dp(topMargin + bottomMargin).toInt(), "BrowseFragment")
                }

                rootView.fullScroll(ScrollView.FOCUS_DOWN)
            }, 100)
        }
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        activity?.let {
            showTipsSame()
            val currentSplitWindowParameter = SplitWindowUtil.getCurrentSplitWindowParameter(it)
            if (mLastCurrentSplitWindowParameter != currentSplitWindowParameter) {
                DebugUtil.e(TAG, "mLastCurrentSplitWindowParameter != currentSplitWindowParameter")
                mTipStatusObserver?.getCloudStatusDialog()?.updateLayoutOnConfig(newConfig)
            }
            mLastCurrentSplitWindowParameter = currentSplitWindowParameter

            changeBehaviorCurrentStatusByScreenHeight(mBrowseViewModel.showSearch.value)
            setOtherViewBottomMargin()
        }
    }

    private fun ensurePermissionDeniedStub() {
        if ((mPermissionScrollview == null) && (!mBinding.mPermissionDeniedStub.isInflated)) {
            mPermissionScrollview = mBinding.mPermissionDeniedStub.viewStub?.inflate() as ScrollView
            mBtnPermissionOperate = mPermissionScrollview?.findViewById(R.id.btn_permission_operate)
            (mPermissionScrollview?.findViewById(R.id.tips_permission_denied) as? TextView)?.setText(
                if (BaseUtil.isAndroidTOrLater) {
                    com.soundrecorder.common.R.string.permission_open_read_audio_dialog_title_v2
                } else {
                    com.soundrecorder.common.R.string.storage_permission_denied
                }
            )
            (mPermissionScrollview?.findViewById(R.id.tips_hint_permission_denied) as? TextView)?.setText(
                if (BaseUtil.isAndroidTOrLater) {
                    com.soundrecorder.common.R.string.permission_open_read_audio_empty_desc_v2
                } else {
                    com.soundrecorder.common.R.string.storage_permission_describe_v2
                }
            )
        }

        activity?.let { act: FragmentActivity ->
            mBtnPermissionOperate?.run {
                if (PermissionUtils.hasReadAudioPermissionRationale(act)) {
                    text = BaseApplication.getAppContext().getString(com.soundrecorder.common.R.string.permission_open_dialog)
                    setOnClickListener {
                        DebugUtil.e(
                            TAG,
                            "onPermissionsDeniedWithTick BROWSE_FILE_SCENES, click PERMISSION_STUB_OPEN"
                        )
                        PermissionUtils.requestReadAudioPermissionForBrowseFile(act)
                    }
                } else {
                    text = BaseApplication.getAppContext().getString(com.soundrecorder.common.R.string.app_name_settings)
                    setOnClickListener {
                        DebugUtil.e(
                            TAG,
                            "onPermissionsDeniedWithTick BROWSE_FILE_SCENES, click PERMISSION_STUB_SETTING"
                        )
                        PermissionUtils.goToAppSettingConfigurePermissions(act, arrayListOf(PermissionUtils.READ_AUDIO_PERMISSION()))
                    }
                }
            }
        }
    }

    private fun setListViewAdapter() {
        context?.let {
            mAdapter = BrowseAdapter(viewLifecycleOwner, it, object : IBrowseViewHolderListener {
                override fun showSummaryTip(anchorView: View?) {
                    DebugUtil.i(TAG, "showSummaryTip")
                    TipUtil.checkShow({ anchorView }, TipUtil.TYPE_SUMMARY_TIPS, null, lifecycle, false, {
                        ItemBrowseRecordViewModel.summaryTipMediaId[taskId] = null
                    })
                }

                override fun getPlayerController(): FastPlayHelper {
                    return mBrowseViewModel.fastPlayHelper
                }

                override fun onClickItem(data: ItemBrowseRecordViewModel) {
                    onRecordItemClick(data, isCurrentPlay(data))
                }

                override fun onLongClickItem(view: View, data: ItemBrowseRecordViewModel) {
                    onItemLongClick(view, data)
                }

                override fun isCurrentPlay(data: ItemBrowseRecordViewModel): Boolean {
                    return getPlayerController().itemBrowseRecordViewModel?.mediaId == data.mediaId
                }

                override fun getWindowType(): MutableLiveData<WindowType> {
                    return mBrowseFileActivityViewModel.windowType
                }

                override fun getPlayLiveData(): MutableLiveData<StartPlayModel?> {
                    return mBrowseFileActivityViewModel.mCurrentPlayRecordData
                }

                override fun canShowAddAnimator(data: ItemBrowseRecordViewModel): Boolean {
                    //父子级录制新的音频回来右侧加载对应的详情，规则同原来展开逻辑一致（新录制且pos=0）
                    if (getWindowType().value != WindowType.SMALL) {
                        val currentPlayRecordData = data.toStartPlayModel(
                            mBrowseFileActivityViewModel.isFromOtherApp,
                            isRecycle = recordFilterIsRecycle()
                        )
                        if (currentPlayRecordData.mediaId.toInt() != 0) {
                            mBrowseFileActivityViewModel.mCurrentPlayRecordData.value =
                                currentPlayRecordData
                        }
                        ItemBrowseRecordViewModel.addAnimatorDisplayName = null
                        DebugUtil.i(TAG, "canShowAddAnimator false")
                        return false
                    }
                    DebugUtil.i(TAG, "canShowAddAnimator true")
                    return true
                }

                override fun onClickSummaryIcon(data: ItemBrowseRecordViewModel) {
                    clickSummaryIcon(data.recordUUID, data.noteId)
                }

                override fun onCallGroupMoreClick(view: View, data: ItemBrowseRecordViewModel) {
                    if (data.isGroupingParent) {
                        mBinding.browseToolbar.post {
                            mBrowseViewModel.exitEditMode()
                            //退出编辑模式，状态重置，否则可能导致新增记录列表无法刷新
                            mNavigationViewManager?.resetContinueOperator()
                        }
                        mBrowseFileActivityViewModel.mCallGroupMoreData.value =
                            data.toCallGroupModel(mBrowseViewModel.currentGroup.value, data.callerName, data.avatarColor)
                    }
                }

                override fun onGroupRecordDeleteSuccess(delay: Long, needRefresh: Boolean) {
                    if (needRefresh) {
                        refreshData()
                    }
                }
            })
            mAdapter.setGroupingByContact(isGroupingByContact())
            mAdapter.setPlaceHolder(mBinding.abl.getUnDisplayViewHeight())
            mAdapter.stateRestorationPolicy = RecyclerView.Adapter.StateRestorationPolicy.PREVENT
            mBinding.mListView.apply {
                layoutManager = object : LinearLayoutManager(context) {
                    override fun canScrollVertically(): Boolean {
                        return super.canScrollVertically()
                    }
                }
                adapter = mAdapter
                isVerticalFadingEdgeEnabled = false
                addOnItemTouchListener(object : RecyclerView.SimpleOnItemTouchListener() {
                    override fun onInterceptTouchEvent(rv: RecyclerView, e: MotionEvent): Boolean {
                        when (e.action) {
                            MotionEvent.ACTION_CANCEL, MotionEvent.ACTION_OUTSIDE, MotionEvent.ACTION_UP -> {
                                dragJob?.let { job ->
                                    if (!job.isCompleted) {
                                        DebugUtil.i(logTag, "item touch cancel drag. action=${e.action}")
                                        job.cancel()
                                    }
                                }
                            }
                        }
                        return false
                    }
                })
            }
            addCloudTip()
        }

        initRefresh()
    }

    private fun onRecordItemClick(data: ItemBrowseRecordViewModel, isCurrentPlay: Boolean) {
        DebugUtil.d(TAG, "onClickItem, data:${data.mediaId}")
        if (ItemBrowseRecordViewModel.liveEditMode[taskId]?.value == true) {
            DebugUtil.d(TAG, "onClickItem, onClickCheckBox")
            data.onClickCheckBox()
            BuryingPoint.addClickBrowseRecordFile(RecorderUserAction.VALUE_SELECT_RECORD_FILE)
            return
        }
        if (data.isRecycle && !PermissionUtils.hasAllFilePermission()) {
            DebugUtil.d(TAG, "onClickItem, data:${data.mediaId}, no permission!")
            //回收站如果没有文管权限，点击需要授权
            activity?.let { it1 ->
                PermissionDialogUtils.showPermissionAllFileAccessDialog(
                    it1,
                    object : PermissionDialogUtils.PermissionDialogListener {
                        override fun onClick(
                            alertType: Int,
                            isOk: Boolean,
                            permissions: ArrayList<String>?
                        ) {
                            if (isOk) {
                                PermissionUtils.goToAppAllFileAccessConfigurePermissions(it1)
                            }
                        }
                    })
            }
            return
        }

        val currentPlayId = mBrowseFileActivityViewModel.mCurrentPlayRecordData.value?.mediaId
        val playerState = mBrowseViewModel.fastPlayHelper.getPlayerState()
        if (data.mediaId != currentPlayId && data.fileIsExists()) {
            val currentIsPlay = isCurrentPlay && data.isNeedShowSeekBarArea(playerState)
            mBrowseFileActivityViewModel.mCurrentPlayRecordData.value =
                data.toStartPlayModel(
                    mBrowseFileActivityViewModel.isFromOtherApp,
                    isRecycle = recordFilterIsRecycle()
                ).apply {
                    DebugUtil.d(TAG, "onClickItem, mediaId:$mediaId, currentIsPlay:$currentIsPlay")
                    if (currentIsPlay) {
                        seekToMill = mBrowseViewModel.fastPlayHelper.getCurrentPlayerTime()
                    }
                    autoPlay = if (mBrowseFileActivityViewModel.isSmallWindow()) {
                        true // 小屏同原有逻辑，自动开始播放
                    } else if (currentIsPlay) {
                        mBrowseViewModel.fastPlayHelper.mediaPlayerManager.isPlaying()
                    } else {
                        false
                    }
                }
            mBrowseViewModel.releasePlayer()
            BuryingPoint.addPlayFromAndDuration(
                RecorderUserAction.VALUE_PLAY_FROM_PLAYBACK,
                data.recordType().toString(),
                data.mDuration
            )
            BuryingPoint.addClickBrowseRecordFile(RecorderUserAction.VALUE_NORMAL_RECORD_FILE)
        }
    }

    private fun onItemLongClick(view: View, data: ItemBrowseRecordViewModel) {
        mBrowseViewModel.releasePlayer()
        val isEditMode = ItemBrowseRecordViewModel.liveEditMode[taskId]?.value
        if (isEditMode == true) {
            val delayTime = if (data.checkSelectItemOnLongClick()) {
                RecordItemDragDelegate.DURATION_CHECKBOX_ANIM
            } else {
                RecordItemDragDelegate.DURATION_DRAG_DELAY_DEFAULT
            }
            tryItemStartDrag(view, false, delayTime)
            return
        }
        data.onLongClick()
        // 等动效执行完成，view绘制好了，再触发拖拽，这样拖拽view的UI才显示正常
        tryItemStartDrag(view, true, RecordItemDragDelegate.DURATION_ENTER_EDIT_MODE_ANIM)
    }

    private fun tryItemStartDrag(view: View, vibrate: Boolean, delayTime: Long = 0) {
        DebugUtil.d(logTag, "tryItemStartDrag delayTime=$delayTime")
        dragViewReference = WeakReference(view)
        dragJob?.cancel()
        dragJob = lifecycleScope.launch {
            delay(delayTime)
            if (isActive) {
                withContext(Dispatchers.Main) {
                    itemDragDelegate?.tryStartDrag(dragViewReference?.get(), vibrate) {
                        if (it) {
                            DebugUtil.i(logTag, "call refresh, from tryItemStartDrag", true)
                            mBrowseViewModel.refresh()
                        }
                    }
                }
            }
        }
    }

    private fun initRefresh() {
        mBinding.bounceLayout.apply {
            mBinding.bounceLayout.setRefreshEnable(true)
            setBounceHandler(BounceHandler(), mBinding.bounceLayout.getChildAt(0))
            activity?.let {
                setHeaderView(DefaultHeader(it), mBinding.pullDownRefreshRoot)
            }
            setEventForwardingHelper(object : EventForwardingHelper {
                override fun notForwarding(downX: Float, downY: Float, moveX: Float, moveY: Float): Boolean {
                    return true
                }
            })
            setBounceCallBack(object : BounceCallBack {
                override fun startRefresh() {
                    val fullRecovery = cloudTipManager?.checkNeedSyncFullRecovery() ?: false
                    if (!fullRecovery) {
                        DebugUtil.i(TAG, "refresh startRefresh")
                        cloudKitApi?.trigCloudSync(context, SYNC_TYPE_RECOVERY_START_APP)
                    }
                    CloudStaticsUtil.addCloudLog(TAG, "initRefresh,startRefresh, fullRecovery=$fullRecovery")
                }

                override fun refreshCompleted() {
                    //刷新完成
                    DebugUtil.i(TAG, "refresh refreshCompleted")
                }

                override fun startLoadingMore() {
                }

                override fun touchEventCallBack(ev: MotionEvent) {
                }
            })
            updatePullRefreshRoot()
        }
    }

    private fun updatePullRefreshRoot() {
        mBinding.bounceLayout.post {
            if (!isAdded) {
                DebugUtil.i(TAG, "updatePullRefreshRoot but fragment is not add")
                return@post
            }
            mBinding.bounceLayout.clipToPadding = false
            val lp = mBinding.pullDownRefreshRoot.layoutParams as MarginLayoutParams
            val resources = BaseApplication.getAppContext().resources
            val headerViewHeight = resources.getDimensionPixelSize(com.soundrecorder.base.R.dimen.default_height)
            val headerTopPadding = resources.getDimensionPixelOffset(com.soundrecorder.common.R.dimen.dp12)
            val headerBottomPadding = resources.getDimensionPixelOffset(com.soundrecorder.common.R.dimen.dp24)
            val listMaxTop = resources.getDimensionPixelOffset(R.dimen.dp150)
            /* In different layouts, the loading animation (LottieView) is hidden (hidden by other views) by setting
               the topMargin of the root layout, and is 24dp above the listView or recyclerView, This value is up to
               the business side according to the needs. */
            val headerTopMargin = headerViewHeight + headerTopPadding
            lp.topMargin = if (LandScapeUtil.spitWindowHeightLessThanForPlay450(activity)) {
                -headerTopMargin
            } else {
                -headerTopMargin - headerBottomPadding
            }
            DebugUtil.i(TAG, "topMargin == ${-headerTopMargin - headerBottomPadding}")

            /* Drag threshold. When the drag distance is greater than or equal to this value, it means that it will enter
               the loading state after letting go. The reason for this calculation is to make the loading animation (LottieView)
               24dp from the top (the View that covered it before), and also 24dp from the listView or recyclerView below
               (including the topPadding of the listView), so the specific value is up to the business party according to the
               needs up to you. */
            mBinding.bounceLayout.mDragDistanceThreshold =
                headerViewHeight + headerTopPadding + headerBottomPadding
            /* The maximum distance that bounceLayout can be dragged down. */
            mBinding.bounceLayout.mMaxDragDistance = listMaxTop

            updateRefreshPaddingTop()
        }
    }

    /**
     * 决定刷新图标所在位置
     * 需要动态更新，云同步中提示语会有图标，分屏下横屏下等都会刷新，确保显示正常
     */
    private fun updateRefreshPaddingTop() {
        DebugUtil.i(TAG, "updateRefreshPaddingTop")
        mBinding.pullDownRefreshRoot.post {
            if (!isAdded) {
                DebugUtil.i(TAG, "updatePullRefreshRoot but fragment is not add")
                return@post
            }
            val paddingTop = mBinding.abl.getUnDisplayViewHeight()
            mBinding.pullDownRefreshRoot.setPadding(0, paddingTop, 0, 0)
        }
    }

    private fun setRefreshEnable(enable: Boolean) {
        mBinding.bounceLayout.setRefreshEnable(enable)
    }

    private fun addCloudTip() {
        val tipStatusGuideObserver = TipStatusGuideObserver(viewLifecycleOwner)
        tipStatusGuideObserver.bindAdapter(mAdapter)
        lifecycle.addObserver(tipStatusGuideObserver)
        mTipStatusObserver = TipStatusObserver(this, mBinding.toolbarSubtitle, mBinding.bounceLayout, mBrowseViewModel).apply {
            lifecycle.addObserver(this)
        }
    }

    private fun addBlankFooter() {
        if (::mAdapter.isInitialized) {
            mAdapter.setFooter(calBlankFooterHeight())
        }
        mBinding.mListView.post {
            mBehavior?.onListScroll()
        }
    }

    override fun onHiddenChanged(hidden: Boolean) {
        super.onHiddenChanged(hidden)
        DebugUtil.d(TAG, "onHiddenChanged,hidden=$hidden")
        mBehavior?.fragmentIsHidden = hidden
        // 有搜索的时候，首页从不可见到可见，显示搜索框
        if (!hidden && mBrowseViewModel.showSearch.value == true && mSearchFragment != null) {
            mCacheHolder.mSearchAnimView?.showInToolBar()
        }
    }

    private fun calBlankFooterHeight(): Int {
        val editMode = ItemBrowseRecordViewModel.liveEditMode[taskId]?.value
        return when {
            editMode == true -> {
                (mBottomNavigationView?.measuredHeight
                    ?: getDimension(com.soundrecorder.common.R.dimen.dp56).toInt()) + getDimension(com.soundrecorder.common.R.dimen.dp30).toInt()
            }

            mBrowseViewModel.getCurrentGroup().isCallingGroup() -> getDimension(com.soundrecorder.common.R.dimen.dp30).toInt()
            else -> {
                getDimension(R.dimen.recorder_height).toInt() + getDimension(com.soundrecorder.common.R.dimen.dp30).toInt()
            }
        }
    }

    private fun getDimension(id: Int): Float {
        return BaseApplication.getAppContext().resources.getDimension(id)
    }

    fun refreshData() {
        DebugUtil.d(TAG, "refreshData")
        if (itemDragDelegate?.checkIsDraggingWhenRefreshData() != true && mCurrentGroup != null) {
            DebugUtil.i(logTag, "call refresh, from refreshData", true)
            mBrowseViewModel.refresh()
        } else {
            DebugUtil.d(TAG, "refreshData return by drag state")
        }
    }

    private fun fixGradient() {
        // 解决TalkBack下点击面板，事件穿透
        mBinding.gradientBackground.setOnHoverListener { v, event ->
            return@setOnHoverListener true
        }
    }

    private fun configToolbar() {
        mOnDismissListener = PopupWindow.OnDismissListener {
            DebugUtil.d(TAG, "configToolbar, PopupWindow dismiss")
            // Add the action of updating menu into message queue. It will be executed after animation ends
            updateToolBarMenu()
        }

        mBinding.browseToolbar.isTitleCenterStyle = false
        mBinding.browseToolbar.setTitleTextColor(Color.argb(0, 0, 0, 0))
        mBinding.browseToolbar.inflateMenu(R.menu.recorder_list_option_browse)
        mBinding.browseToolbar.setPopupWindowOnDismissListener(mOnDismissListener)

        mOptionsMenu = mBinding.browseToolbar.menu.apply {
            setSearchView(this)
        }
        initMainTitleBelowOS14()

        lifecycleScope.launch {
            repeatOnLifecycle(Lifecycle.State.RESUMED) {
                mOptionsMenu?.apply {
                    onCreateOptionsMenuSpeaker(this)
                    updateToolBarMenu()
                    this.children.forEach {
                        it.setOnMenuItemClickListener { menuItem ->
                            onOptionsItemSelected(menuItem)
                        }
                    }
                    /*// 重建
                    onCreateOptionsMenuCheckRestoreWindow()*/
                }
            }
        }
    }

    private fun hideCallGroupMenu() {
        mBinding.browseToolbar.menuView.apply {
            setUseBackgroundBlur(false)
            setOverflowMenuListener(null)
        }
        if (mToolbarOverflowPopupWindow?.isShowing == true) {
            mToolbarOverflowPopupWindow?.dismiss()
        }
        mToolbarOverflowPopupWindow = null
        mOptionsMenu?.setGroupVisible(R.id.group_1, false)
    }

    private fun showCallGroupMenu() {
        if (mCurrentGroup?.isCallingGroup() == true) {
            mOptionsMenu?.setGroupVisible(R.id.group_1, true)
            mBinding.browseToolbar.menuView.apply {
                setUseBackgroundBlur(true)
                setOverflowMenuListener { popup ->
                    mToolbarOverflowPopupWindow = popup
                    val mainList = popup.itemList
                    DebugUtil.d(TAG, "configToolbar mainList:${mainList.size}")
                    for (item in mainList) {
                        if (item.id == R.id.group_sort_by) {
                            item.subMenuItemList[mSubMenuCheckedPosition].isChecked = true
                            for (subItem in item.subMenuItemList) {
                                subItem.stateIcon = ResourcesCompat.getDrawable(
                                    resources,
                                    R.drawable.menu_sort_selector,
                                    activity?.theme
                                )
                            }
                        }
                    }

                    mToolbarOverflowPopupWindow?.apply {
                        setSubMenuClickListener(AdapterView.OnItemClickListener { _, _, position, _ ->
                            DebugUtil.d(
                                TAG,
                                "subMenuClick, mSubMenuCheckedPosition:$mSubMenuCheckedPosition, position:$position"
                            )
                            if (mSubMenuCheckedPosition == position) { // 正处于选择项位置，不切换数据。
                                dismiss()
                                return@OnItemClickListener
                            }
                            val mainList = itemList
                            for (itemGroup in mainList) {
                                if (itemGroup.id == R.id.group_sort_by) {
                                    itemGroup.subMenuItemList[mSubMenuCheckedPosition].isChecked =
                                        false
                                    itemGroup.subMenuItemList[position].isChecked = true
                                    mSubMenuCheckedPosition = position
                                    mBrowseViewModel.mCallRecordGroupPosition = position
                                    PrefUtil.putInt(activity, KEY_RECORD_CALL_GROUP_SORT, position)
                                    callGroupBySort()
                                    dismiss()
                                }
                            }
                        })
                    }
                }
            }
        }
    }

    /**
     * OS13.2上标题栏全部录音样式不同于OS14
     */
    private fun initMainTitleBelowOS14() {
        if (!OS12FeatureUtil.isColorOS14OrLater()) {
            mBinding.toolbarTitle.updateLayoutParams<MarginLayoutParams> {
                height =
                    mBinding.toolbarTitle.resources.getDimensionPixelOffset(R.dimen.toolbar_title_init_height_os_13_2)
            }
            mBinding.toolbarTitle.setTextAppearance(R.style.Browse_Title_OS13_2)
        }
    }

    private fun setClickListener() {
        mBinding.redCircleIcon.setOnClickListener(this)
        mBinding.toolbarTitle.setOnClickListener(this)
        //mBinding.folderNameRotateView.setOnClickListener(this)
        ensureStatusBarResponse()
    }

    override fun onResume() {
        super.onResume()
        mStatusBarResponse?.onResume()
        if (!checkRestoreOption() && !isDeviceSecureDelete && mCurrentGroup != null) {
            // 异常退出的情况下，需要拿到sp的id去刷新媒体库，清空异常退出的sp
            mBrowseViewModel.viewModelScope.launch(Dispatchers.IO) {
                val set = PrefUtil.getStringSet(context, PrefUtil.KEY_SAVE_RECORD_ID_FOR_ABNORMAL_EXIT, null)
                if (set != null && set.size > 0) {
                    val idList = arrayOfNulls<String?>(set.size)
                    set.forEachIndexed { index, s ->
                        idList[index] = s
                    }
                    val pathList = RecorderDBUtil.getInstance(context).getRecordPathByIds(idList)
                    if (pathList.isNotEmpty()) {
                        val pathArray = pathList.toTypedArray()
                        abnormalExitRefresh(pathArray)
                    } else {
                        if (!checkUserNoticeDialogShowing()) {
                            withContext(Dispatchers.Main) {
                                refreshData()
                            }
                        }
                    }
                    PrefUtil.clearPreference(context, PrefUtil.KEY_SAVE_RECORD_ID_FOR_ABNORMAL_EXIT)
                } else {
                    if (!checkUserNoticeDialogShowing() && mBrowseFileActivityViewModel.isNeedRefresh.value == true) {
                        withContext(Dispatchers.Main) {
                            refreshData()
                        }
                    }
                }
                mBrowseFileActivityViewModel.isNeedRefresh.postValueSafe(true)
            }
        }
        showTipsSame()
    }

    /**
     * 获取云服务商业化配置
     */
    private fun getCloudConfigTips() {
        val headerEmpty = !mAdapter.isHeaderNotEmpty()
        val needShowConfig = cloudKitApi?.isSupportCloudArea() ?: false
        val showCloudCard = headerEmpty && needShowConfig
        val oldCloudView = mAdapter.getHeadeView(TYPE_HEADER_CLOUD_CONFIG) as? CloudConfigTipView
        if (!needShowConfig && oldCloudView != null) {
            mAdapter.setHeader(TYPE_HEADER_CLOUD_CONFIG, null)
            DebugUtil.d(TAG, "getCloudConfigTips is end. hide config view  ")
            return
        }
        if (!showCloudCard || oldCloudView != null) {
            DebugUtil.d(TAG, "getCloudConfigTips is end.  ")
            return
        }
        //如果用户在一天以内，已经点击过忽略，则不请求接口
        val lastTimeMillis = PrefUtil.getLong(
            context,
            PrefUtil.KEY_CLOUD_CONFIG_IGNORE_TIME,
            0
        )
        val timeGap = System.currentTimeMillis() - lastTimeMillis

        if (timeGap < 0) {
            //说明修改过系统时间,重置时间点
            PrefUtil.putLong(
                context,
                PrefUtil.KEY_CLOUD_CONFIG_IGNORE_TIME,
                System.currentTimeMillis()
            )
            DebugUtil.d(TAG, "getCloudConfigTips timeGap < 0")
            return
        } else if (timeGap <= ONE_DAY_TIME_MILLIS) {
            DebugUtil.d(TAG, "getCloudConfigTips timeGap < ONE_DAY_TIME_MILLIS")
            return //点击忽略1天以内，不进行请求
        }

        DebugUtil.d(TAG, "getCloudConfigTips")
        /**
         * 根据刷新规则，此处罗列需要监听状态的变化，当发生变化时，触发获取 服务端配置词条
         */
        kotlin.runCatching {
            val dataStr = MutableLiveData<String>()
            dataStr.observe(viewLifecycleOwner) { result ->
                if (result.isNullOrEmpty()) {
                    DebugUtil.d(TAG, "getCloudConfigTips result is null.")
                    return@observe
                }
                context?.let {
                    val data = Gson().fromJson(
                        result,
                        CloudOperationResponseData::class.java
                    )
                    data.dataList?.first()?.let { configData ->
                        setRefreshEnable(false)
                        val cloudConfigTipView = fragmentManager?.let { fragmentManager ->
                            CloudConfigTipView(
                                it,
                                this, configData
                            )
                        }
                        mAdapter.hideCloudHeader()
                        cloudConfigTipView?.let {
                            it.bindAdapter(mAdapter)
                            mAdapter.setHeader(TYPE_HEADER_CLOUD_CONFIG, it)
                        }
                    }
                    DebugUtil.d(TAG, "json res: $data")
                }
            }
            cloudKitApi?.getCloudSettings(dataStr)  //需要获取返回值对象
        }.onFailure {
            DebugUtil.w(TAG, "getCloudConfigTips error, ${it.message}")
        }
    }

    /**
     * 异常退出，刷新界面
     */
    private fun abnormalExitRefresh(filePaths: Array<String?>) {
        MediaDataScanner.getInstance().mediaScanWithCallback(context, filePaths) { path, uri ->
            DebugUtil.d(TAG, "abnormalExitRefresh: path = $path, uri = $uri")
            context?.let {
                LocalBroadcastManager.getInstance(it).sendBroadcast(Intent(RecordFileChangeNotify.FILE_UPDATE_ACTION))
            }
        }
    }

    /**
     * 录制模式移动到设置中去，check是否显示迁移引导
     * 1. 不支持三种录制模式，则不显示引导及红点、设置不显示入口-- 迁移引导、设置入口已通过不支持feature拦截判断，跟sp无关
     * 2.支持三种模式，旧用户显示迁移引导，新用户不显示迁移引导
     * 旧用户：已经显示过模式新手引导的视为旧用户
     */
    private fun checkTipForUpgrade() {
        if (RecordModeUtil.isSupportMultiRecordMode(BaseApplication.getAppContext()) && !TipUtil.hasShowTip(TipUtil.TYPE_MODE)) {
            // 还没显示过新手引导，说明是新用户，新用户不需要显示迁移引导，迁移引导针对升级上来的用户
            settingApi?.setRecordModeRedDotShowed() // 设置小红点已经显示过了
            TipUtil.saveShowedTip(TipUtil.TYPE_MODE_MOVE) // 设置显示过迁移引导了
        }
    }

    /**
     * 问题:进入录音，在声明页开启分屏，进入首页，三种模式选择的新手提示不弹出
     * 原因：声明页是一个弹窗覆盖在首页上的，在声明页的时候首页已经初始化并且走了新手提示逻辑
     * ，此时新手提示弹窗已经弹出，触摸屏幕上划，响应了tips OnDismissListener事件，导致新手提示
     * 被关闭
     * 解决办法：更换之前的调用逻辑，当前的权限状态不等于DIALOG_BOTH_NOT_SHOW的时候再走该逻辑，
     * 但是该逻辑依附与权限弹窗的弹出，关闭重新走到页面的onResume方法
     */
    fun showTipsSame() {
        if (!shouldShowTips(false)) return

        runCatching {
            if (settingApi?.getNeedShowRecordModeRedDot() == true) {
                mBinding.browseToolbar.setRedDot(R.id.item_setting, 0)
            } else {
                mBinding.browseToolbar.setRedDot(R.id.item_setting, -1)
            }
            TipUtil.checkShow(
                {
                    if (!shouldShowTips(true)) return@checkShow null
                    mBinding.browseToolbar.overFlowMenuButton
                },
                TipUtil.TYPE_MODE_MOVE,
                null,
                lifecycle,
                isInMultiWindowMode(),
                offsetY = -BaseApplication.getAppContext().resources.getDimensionPixelSize(com.soundrecorder.common.R.dimen.dp6),
            )
        }.onFailure {
            DebugUtil.e(TAG, "showTipsSame error $it")
        }
    }

    /**
     * 判断是否可以显示引导弹窗
     * @param judgeMultiWindow 是否判断分屏
     */
    private fun shouldShowTips(judgeMultiWindow: Boolean = false): Boolean {
        if (!RecordModeUtil.isSupportMultiRecordMode(BaseApplication.getAppContext())) {
            DebugUtil.i(TAG, "shouldShowTips notSupportMultiRecordMode.")
            return false
        }
        /**
         * 录制模式的新手提示，不能在Activity的onPause状态之后去显示，主要屏蔽系统权限弹窗显示时，切换亮暗色导致新手提示显示在了系统权限弹窗之下了
         */
        activity?.let {
            if (!OSDKCompatUtils.isResumed(it)) {
                return false
            }
        }
        if (judgeMultiWindow && isInMultiWindowMode()) {
            return false
        }
        val dialogStatus = PermissionUtils.getNextAction()
        if (dialogStatus != PermissionUtils.SHOULD_REQUEST_PERMISSIONS) {
            return false
        }

        if (mBrowseViewModel.showSearch.value == true) {
            return false
        }
        return !(isRemoving || (activity?.isFinishing ?: true))
    }

    private fun checkRestoreOption(): Boolean {
        val restoreDelete = checkRestoreDelete()
        val restoreRename = checkRestoreRename()
        val restoreRecover = checkRestoreRecover()
        val restoreSmartName = checkRestoreSmartName()
        return restoreDelete or restoreRename or restoreRecover && restoreSmartName
    }

    private fun checkRestoreSmartName(): Boolean {
        DebugUtil.d(TAG, "checkRestoreSmartName, isNeedSmartName:${mBrowseViewModel.isNeedSmartName}, " +
                "needSmartNameMediaList:${mBrowseViewModel.needSmartNameMediaList.size}")
        if (mBrowseViewModel.isNeedSmartName && mBrowseViewModel.needSmartNameMediaList.isNotEmpty()) {
            DebugUtil.d(TAG, "checkRestoreSmartName, isOpenSwitch:$isOpenSwitch")
            startConvertAndSmartName(mBrowseViewModel.needSmartNameMediaList, isOpenSwitch)
            return true
        }
        return false
    }

    override fun onPause() {
        DebugUtil.i(TAG, "onPause")
        super.onPause()
        mStatusBarResponse?.onPause()
        if ((mBrowseFileActivityViewModel.mSpeakerModeController.mIsSpeakerOn.value == false)
            && mBrowseViewModel.isPlaying()
        ) {
            mBrowseViewModel.pausePlay()
        }
        //releaseSpeakerUI()
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        val manager = mBinding.mListView.layoutManager as? LinearLayoutManager
        manager?.let {
            mBrowseViewModel.firstVisiblePosition = it.findFirstVisibleItemPosition()
        }
        mBrowseViewModel.isClickDelete = mNavigationViewManager?.isClickDelete() == true
        mBrowseViewModel.isClickRecover = mNavigationViewManager?.isClickRecover() == true
        mBrowseViewModel.isClickDeleteAll = mNavigationViewManager?.isClickDeleteAll() == true
        mBrowseViewModel.isClickRename = mNavigationViewManager?.isClickRename() == true
        mBrowseViewModel.renameContent =
            if (mBrowseViewModel.isClickRename) mNavigationViewManager?.getRenameContent() else mNavigationViewManager?.editDisplayName().title()
        mBrowseViewModel.mCallRecordGroupPosition = mSubMenuCheckedPosition
        DebugUtil.i(
            TAG, """
            onSaveInstanceState
            firstVisiblePosition = ${mBrowseViewModel.firstVisiblePosition}
            isClickRecover = ${mBrowseViewModel.isClickRecover}
            isClickDelete = ${mBrowseViewModel.isClickDelete}
            isClickDeleteAll = ${mBrowseViewModel.isClickDeleteAll}
            isClickRename = ${mBrowseViewModel.isClickRename}
            renameContent = ${mNavigationViewManager?.getRenameContent()}
            """.trimIndent()
        )
    }

    private fun checkRestoreDelete(): Boolean {
        if (mBrowseViewModel.isClickDelete || mBrowseViewModel.isClickDeleteAll) {
            DebugUtil.i(logTag, "<< onResumeCheckDelete")
            ensureNavigationViewManager()
            if (PermissionUtils.hasFilePermissionCompat()) {
                /**
                 * When the interface is rebuilt, the edit mode will be restored,
                 * and the recorder area view disappear anim will be executed.
                 *  Here, you need to switch the edit mode after the end of this animation.
                 */
                if (mBrowseViewModel.isClickDeleteAll && mNavigationViewManager?.getDeleteAllDialog() != null) {
                    mNavigationViewManager?.deleteHasPermission(mBrowseViewModel.isClickDeleteAll)
                } else {
                    mBinding.gradientBackground.postDelayed(
                        {
                            mNavigationViewManager?.deleteHasPermission(mBrowseViewModel.isClickDeleteAll)
                        },
                        ItemAnimationUtil.DURATION_240
                    )
                }
            }
            mBrowseViewModel.isClickDelete = false
            mBrowseViewModel.isClickDeleteAll = false
            mNavigationViewManager?.resetContinueOperator()
            return true
        }
        return false
    }

    private fun checkRestoreRename(): Boolean {
        if (mBrowseViewModel.isClickRename) {
            DebugUtil.i(logTag, "<< onResumeCheckRename")
            if (PermissionUtils.hasFilePermissionCompat()) {
                ensureNavigationViewManager()
                val result = FileDealUtil.renameAgain(
                    mNavigationViewManager?.selectedRecordList?.get(0),
                    mBrowseViewModel.renameContent
                )
                if (result) {
                    /**
                     * When the interface is rebuilt, the edit mode will be restored,
                     * and the recorder area view disappear anim will be executed.
                     * Here, you need to switch the edit mode after the end of this animation.
                     */
                    mBinding.gradientBackground.postDelayed(
                        {
                            mBrowseViewModel.exitEditMode()
                            DebugUtil.i(logTag, "call refresh, from checkRestoreRename", true)
                            mBrowseViewModel.refresh()
                        },
                        ItemAnimationUtil.DURATION_240
                    )
                }
            }
            mBrowseViewModel.isClickRename = false
            mNavigationViewManager?.resetContinueOperator()
            return true
        }
        return false
    }

    private fun checkRestoreRecover(): Boolean {
        if (mBrowseViewModel.isClickRecover) {
            DebugUtil.i(logTag, "<< onResumeCheckRecover")
            if (PermissionUtils.hasFilePermissionCompat()) {
                ensureNavigationViewManager()
                mNavigationViewManager?.recoverHasPermission()
            }
            mBrowseViewModel.isClickRecover = false
            mNavigationViewManager?.resetContinueOperator()
            return true
        }
        return false
    }

    private fun ensureNavigationViewManager() {
        if (mNavigationViewManager == null) {
            mNavigationViewManager = NavigationViewManager(activity, mBottomNavigationView)
            mNavigationViewManager?.supportSmartName = mSupportSmartName
            mNavigationViewManager?.showRecordCount = mBrowseViewModel.getShowRecordCount()
            mNavigationViewManager?.setGroupInfo(mBrowseViewModel.getCurrentGroup())
            if (recordFilterIsRecycle()) {
                mNavigationViewManager?.setAllRecordList(mBrowseViewModel.getRecycleAllRecordList())
            }
            mNavigationViewManager?.onOptionCompletedListener = recordOptionCompletedListener
        }
    }

    private fun resetRecycleDialogShowing() {
        mBrowseViewModel.isRecoverDialogShowing = false
        mBrowseViewModel.isDeleteDialogShowing = false
        mBrowseViewModel.isRecycleDialogAllShowing = false
    }

    private fun loadSpeakerMode() {
        DebugUtil.i(logTag, "registerSpeaker")
        viewLifecycleOwner.lifecycleScope.launch(Dispatchers.Default) {
            viewLifecycleOwner.lifecycle.repeatOnLifecycle(Lifecycle.State.RESUMED) {
                SpeakerStateManager.getInstance().initSpeakerState()
                mBrowseFileActivityViewModel.mSpeakerModeController.loadInitMode()
                SpeakerReceiver.getInstance().setToDoInSpeakerReceiver(mBrowseFileActivityViewModel.mSpeakerModeController)
            }
        }
    }

    private fun onCreateOptionsMenuSpeaker(menu: Menu) {
        mSpeakerMenuItem = menu.findItem(R.id.item_speaker)
        updateSpeakerMenu(mBrowseFileActivityViewModel.mSpeakerModeController.mSpeakerUiMode.value)
    }

    private fun updateSpeakerMenu(uiMode: Int?) {
        DebugUtil.i(logTag, "updateSpeakerMenu: uiMode = $uiMode")
        when (uiMode) {
            SpeakerStateManager.SPEAKER_OFF_WITHOUT_HEADSET -> {
                mSpeakerMenuItem?.setIcon(com.soundrecorder.common.R.drawable.ic_small_speaker_red)
                mSpeakerMenuItem?.setTitle(com.soundrecorder.common.R.string.talk_back_handset_play)
                mSpeakerMenuItem?.contentDescription =
                    resources.getString(com.soundrecorder.common.R.string.talk_back_handset_play)
                mSpeakerMenuItem?.isEnabled = true
            }

            SpeakerStateManager.SPEAKER_ON_WITHIN_HEADSET -> {
                /*don't be grey on androidS*/
                val icon = if (BaseUtil.isAndroidSOrLater) {
                    com.soundrecorder.common.R.drawable.ic_big_speaker_black
                } else {
                    com.soundrecorder.common.R.drawable.ic_big_speaker_gray
                }

                mSpeakerMenuItem?.setIcon(icon)
                mSpeakerMenuItem?.contentDescription =
                    resources.getString(com.soundrecorder.common.R.string.talk_back_speaker_play)
            }

            SpeakerStateManager.SPEAKER_OFF_WITHIN_HEADSET -> {
                /*don't be grey on androidS*/
                val icon = if (BaseUtil.isAndroidSOrLater) {
                    com.soundrecorder.common.R.drawable.ic_small_speaker_red
                } else {
                    com.soundrecorder.common.R.drawable.ic_small_speaker_gray
                }

                mSpeakerMenuItem?.setIcon(icon)
                mSpeakerMenuItem?.contentDescription =
                    resources.getString(com.soundrecorder.common.R.string.talk_back_handset_play)
            }

            SpeakerStateManager.SPEAKER_ON_WITHOUT_HEADSET -> {
                mSpeakerMenuItem?.setIcon(com.soundrecorder.common.R.drawable.ic_big_speaker_black)
                mSpeakerMenuItem?.setTitle(com.soundrecorder.common.R.string.talk_back_speaker_play)
                mSpeakerMenuItem?.isEnabled = true
                mSpeakerMenuItem?.contentDescription =
                    resources.getString(com.soundrecorder.common.R.string.talk_back_speaker_play)
            }

            else -> {
                mSpeakerMenuItem?.setIcon(com.soundrecorder.common.R.drawable.ic_big_speaker_black)
                mSpeakerMenuItem?.setTitle(com.soundrecorder.common.R.string.talk_back_speaker_play)
                mSpeakerMenuItem?.isEnabled = true
                mSpeakerMenuItem?.contentDescription =
                    resources.getString(com.soundrecorder.common.R.string.talk_back_speaker_play)
            }
        }
    }

    private fun initSpeakerMenuObservers() {
        mBrowseFileActivityViewModel.mSpeakerModeController.mSpeakerUiMode.observe(
            viewLifecycleOwner,
            androidx.lifecycle.Observer {
                updateSpeakerMenu(it)
                if (SpeakerStateManager.SPEAKER_OFF_WITHOUT_HEADSET == it) {
                    changeActivityVolumeStream(AudioManager.STREAM_VOICE_CALL)
                } else {
                    changeActivityVolumeStream(AudioManager.STREAM_MUSIC)
                }
            })
        mBrowseFileActivityViewModel.mSpeakerModeController.mIsSpeakerOn.observe(viewLifecycleOwner, Observer {
            mBrowseViewModel.replay()
        })
        mBrowseFileActivityViewModel.mSpeakerModeController.mPlayerCommand.observe(viewLifecycleOwner, Observer {
            if (it == PLAYER_COMMAND_PAUSE) {
                mBrowseViewModel.pausePlay()
            }
        })
    }

    private fun changeActivityVolumeStream(streamType: Int?) {
        DebugUtil.d(TAG, "changeActivityVolumeStream  $streamType")
        activity?.volumeControlStream = streamType ?: AudioManager.STREAM_MUSIC
    }

    private fun attachBehavior() {
        if (mBehavior == null) {
            mBehavior = activity?.let {
                PrimaryTitleBehavior(it)
            }
        }
        mBinding.abl.post {
            mBehavior?.ensureBehaviorView(mBinding.rootView, mBinding.abl, mBinding.mListView)
            if (FunctionOption.PHONE_NEED_HORIZONTAL && LandScapeUtil.spitWindowHeightLessThanForPlay450(activity)) {
                DebugUtil.d(TAG, "attachBehavior, unexpandSubTitle:")
                mAdapter.setPlaceHolder(mBinding.browseToolbar.height)
                setBehaviorUnexpandSubTitle()
            } else {
                mAdapter.setPlaceHolder(mBehavior?.mAppBarLayoutHeight ?: 0)
            }
        }
        (mBinding.abl.layoutParams as? CoordinatorLayout.LayoutParams)?.apply {
            behavior = mBehavior
        }
        (mBinding.content.layoutParams as? LinearLayout.LayoutParams)?.apply {
            topMargin = 0
        }
        mBehavior?.fragmentIsHidden = isHidden
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        DebugUtil.i(logTag, "requestCode = $requestCode resultCode = $resultCode")
        if (!::mBrowseViewModel.isInitialized) {
            DebugUtil.w(logTag, "mBrowseViewModel is not initialized!")
            return
        }
        checkHideDelayJumpMask()
        when (requestCode) {
            RecorderDataConstant.REQUEST_SHOW_ADD_ANIMATOR -> showAddAnimator(data)
            RecorderDataConstant.REQUEST_CODE_TO_RECORDER -> responseMms(resultCode, data)
            DeleteFileDialogUtil.REQUEST_LOCK_SCREEN_RESULT_SUCCESS -> responseDelete(resultCode, false)
            DeleteFileDialogUtil.REQUEST_LOCK_SCREEN_RECYCLE_DELETE_ALL_NONE_EDIT -> responseDelete(resultCode, true)
            RecoverFileDialogUtil.REQUEST_LOCK_SCREEN_RESULT_SUCCESS -> responseRecover(resultCode)
            FileDealUtil.REQUEST_CODE_SHARE -> responseShare(resultCode)
            REQUEST_CODE_SYS_RENAME_AUTH -> responseRename(resultCode)
            REQUEST_CODE_SYS_DELETE_AUTH -> responseDeleteBatch(resultCode)
            REQUEST_CODE_SYS_DELETE_ALL_AUTH -> responseDeleteBatch(resultCode, true)
            REQUEST_CODE_SYS_RECOVER_AUTH -> responseRecoverBatch(resultCode)
            REQUEST_CODE_START_NOTE -> responseStartNote(resultCode, data)
            else -> super.onActivityResult(requestCode, resultCode, data)
        }
        if (resultCode == PermissionUtils.RESULT_NOTIFICATION_PERMISSION_DENIED) {
            //首次使用APP，在录制界面拒绝了权限导致不能进行录制，需要回到首页，同时也拒绝了通知权限，则回到首页展示受阻弹窗。
            showRequestNotificationPermissionSnackBar(activity)
        }
    }

    private fun responseRename(resultCode: Int) {
        if (resultCode == RESULT_OK) {
            val renameContent = mNavigationViewManager?.getRenameContent() ?: mBrowseViewModel.renameContent
            ensureNavigationViewManager()
            val record = mNavigationViewManager?.selectedRecordList?.get(0)
            val renameResult = FileDealUtil.renameAgain(record, renameContent)
            if (renameResult) {
                mBrowseViewModel.exitEditMode()
            }
            CloudStaticsUtil.addCloudLog(TAG, "responseRename,${record?.displayName} renameTo $renameContent,result=$renameResult")
        } else {
            mBrowseViewModel.exitEditMode()
        }
    }

    private fun responseDeleteBatch(resultCode: Int, isDeleteAll: Boolean = false) {
        DebugUtil.i(logTag, "<<< responseDeleteBatch resultCode > $resultCode")
        if (resultCode == RESULT_OK) {
            ensureNavigationViewManager()
            mNavigationViewManager?.delete(isDeleteAll)
        }
    }

    private fun responseRecoverBatch(resultCode: Int) {
        DebugUtil.i(logTag, "<<< responseRecoverBatch resultCode > $resultCode")
        if (resultCode == RESULT_OK) {
            ensureNavigationViewManager()
            mNavigationViewManager?.recover()
        }
    }

    private fun responseRecover(resultCode: Int) {
        if (resultCode == RESULT_OK) {
            DebugUtil.i(logTag, "<<< responseRecover")
            isDeviceSecureDelete = true
            mNavigationViewManager?.recoverHasPermission()
        }
    }

    private fun responseShare(resultCode: Int) {
        if (resultCode == 0) {
            mBrowseViewModel.isCanShareRecordFiles = true
        }
    }

    /**
     * @param fromRecycleNoneEditDeleteAll 回收站非编辑模式下底部删除全部 R.id.item_delete_all
     */
    private fun responseDelete(resultCode: Int, fromRecycleNoneEditDeleteAll: Boolean) {
        if (resultCode == RESULT_OK) {
            DebugUtil.i(logTag, "<<< responseDelete fromRecycleNoneEditDeleteAll=$fromRecycleNoneEditDeleteAll")
            isDeviceSecureDelete = true
            mNavigationViewManager?.deleteHasPermission(fromRecycleNoneEditDeleteAll)
        }
    }

    private fun responseMms(resultCode: Int, data: Intent?) {
        if (resultCode == RESULT_OK) {
            val intent = Intent()
            intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
            intent.data = data?.data
            activity?.setResult(RESULT_OK, intent)
            activity?.finish()
        }
    }

    private fun checkHideDelayJumpMask() {
        if (mBrowseViewModel.isDelayStartRecorder && (mBinding.vDelayJumpMask.visibility == View.VISIBLE)) {
            mBinding.vDelayJumpMask.visibility = View.GONE
        }
    }

    private fun checkExitPlayWhenRecorderSuccess() {
        mBrowseFileActivityViewModel.clickedToRecord = false
        // 小屏退出录制页面：退出播放显示列表； 中大屏：保持之前选中音频文件或者更新为新录音详情
        if ((mBrowseFileActivityViewModel.isSmallWindow())) {
            mBrowseFileActivityViewModel.clearPlayRecordData()
        }
    }

    private fun showAddAnimator(data: Intent?) {
        DebugUtil.i(logTag, "showAddAnimator")
        checkExitPlayWhenRecorderSuccess()
        mBinding.mListView.scrollToPosition(0)
        mBehavior?.expandSubTitle()
        //mBrowseViewModel.recordFilter.value = SingleModelSheetDialog.SINGLE_CHECK_ITEM_ALL
        var showAlert = false
        val autoFind: Boolean
        var displayName: String? = null
        var fullPath: String? = null
        var needSmartName: Boolean = false
        try {
            showAlert = data?.getBooleanExtra(RecorderDataConstant.SHOULD_SHOW_EXCEEDING_ALERT, false)
                ?: false
            autoFind = data?.getBooleanExtra(RecorderDataConstant.SHOULD_AUTO_FIND_SAVE_ITEM, false)
                ?: false
            if (autoFind) {
                displayName = data?.getStringExtra(RecorderDataConstant.SHOULD_AUTO_FIND_FILE_NAME)
            }
            fullPath = data?.getStringExtra(RecorderDataConstant.SHOULD_AUTO_FIND_FULL_PATH)
            needSmartName = data?.getBooleanExtra(RecorderDataConstant.SHOULD_AUTO_NEED_SMART_NAME, false) ?: false
        } catch (e: Exception) {
            DebugUtil.e(logTag, "get extra error.", e)
        }
        if (showAlert) {
            showLimitDialog()
        }
        ItemBrowseRecordViewModel.addAnimatorDisplayName = displayName
        DebugUtil.i(logTag, "showAddAnimator $displayName, fullPath:$fullPath")
        checkNeedConvertSmartName(fullPath, needSmartName)
    }

    private fun checkNeedConvertSmartName(fullPath: String?, needSmartName: Boolean) {
        if (needSmartName) {
            lifecycleScope.launch(Dispatchers.IO) {
                val mediaRecord = MediaDBUtils.queryRecordByFullPath(fullPath)
                DebugUtil.d(TAG, "checkNeedConvertSmartName, mediaRecord:$mediaRecord")
                if (mediaRecord != null) {
                    withContext(Dispatchers.Main) {
                        val mediaIdList = mutableListOf<Long>().apply {
                            add(mediaRecord.id)
                        }
                        isOpenSwitch = true
                        if (!PermissionUtils.hasAllFilePermission()) {
                            showPermissionAllFileDialog(true, mediaIdList)
                            return@withContext
                        }
                        //首次从录制保存过来智能命名时打开开关
                        startConvertAndSmartName(mediaIdList, true)
                    }
                }
            }
        }
    }

    private fun initSmartNameManagerImpl() {
        if (mSmartNameMangerImpl == null) {
            DebugUtil.d(TAG, "initSmartNameManagerImpl")
            mSmartNameMangerImpl = playbackApi?.getSmartNameManager()
        }
    }

    fun startConvertAndSmartName(selectedMediaIdList: MutableList<Long>?, isOpenSwitch: Boolean = false) {
        if (selectedMediaIdList.isNullOrEmpty()) {
            return
        }
        if (PermissionUtils.hasAllFilePermission()) {
            initSmartNameManagerImpl()
            mSmartNameMangerImpl?.convertStartSmartNameClickHandle(
                activity,
                selectedMediaIdList,
                PrivacyPolicyConstant.PAGE_FROM_BROWSE,
                isOpenSwitch
            )
            mBrowseViewModel.clearNeedSmartNameMedias()
        }
    }

    private fun showLimitDialog() {
        if (mLimitDialog == null) {
            activity?.let { act ->
                mLimitDialog = COUIAlertDialogBuilder(act)
                    .setTitle(com.soundrecorder.common.R.string.auto_save_v2)
                    .setBlurBackgroundDrawable(true)
                    .setPositiveButton(com.soundrecorder.common.R.string.button_ok, null)
                    .setCancelable(true)
                    .create()
            }
        }
        mLimitDialog?.show()
        ViewUtils.updateWindowLayoutParams(mLimitDialog?.window)
    }

    private fun releaseLimitDialog() {
        if (mLimitDialog?.isShowing == true) {
            mLimitDialog?.dismiss()
        }
        mLimitDialog = null
    }

    override fun onBackPressed(): Boolean {
        var consume = findFragment<SearchFragment>(SearchFragment.TAG)?.onBackPressed() ?: false
        DebugUtil.i(
            TAG,
            "ItemBrowseRecordViewModel.liveEditMode.value > ${ItemBrowseRecordViewModel.liveEditMode[taskId]?.value}"
        )
        if (!consume && (ItemBrowseRecordViewModel.liveEditMode[taskId]?.value == true)) {
            mBrowseViewModel.exitEditMode()
            mBrowseViewModel.isRecycleDialogAllShowing = false
            consume = true

            //退出编辑模式，状态重置，否则可能导致新增记录列表无法刷新
            mNavigationViewManager?.resetContinueOperator()
        }
        return consume
    }

    private fun ensureStatusBarResponse() {
        if (mStatusBarResponse == null) {
            mStatusBarResponse = COUIStatusBarResponseUtil(activity)
            mStatusBarResponse?.setStatusBarClickListener(this)
        }
    }

    override fun onStatusBarClicked() {
        DebugUtil.i(TAG, "onStatusBarClicked")
        /*小屏，播放详情不响应*/
        if (mBrowseFileActivityViewModel.isSmallWindow() && (mBrowseFileActivityViewModel.hasPlayPageData())) {
            DebugUtil.i(TAG, "onStatusBarClicked  return for in play page")
            return
        }
        if (mAdapter.isContentNotEmpty()) {
            smoothScrollToTop()
        } else {
            mBehavior?.expandSubTitle()
        }
    }

    /**
     * 滚动到顶部
     * 若音频过多，会导致滑动时间过长，所以先滚动到2屏的位置，再执行smoothscroll
     */
    private fun smoothScrollToTop() {
        if (mBinding.mListView.scrollState == RecyclerView.SCROLL_STATE_SETTLING) {
            mBinding.mListView.stopScroll()
        }
        mBinding.mListView.removeCallbacks(smoothToTopRunnable)
        (mBinding.mListView.layoutManager as? LinearLayoutManager)?.let {
            val firstPosition = it.findFirstVisibleItemPosition()
            val lastPosition = it.findLastVisibleItemPosition()
            val itemCountInTwoScreen = (lastPosition - firstPosition) * 2
            DebugUtil.d(TAG, "smoothScrollToTop, firstPosition=$firstPosition,lastPosition=$lastPosition")
            if ((firstPosition > 0) && (itemCountInTwoScreen > 0) && (firstPosition > itemCountInTwoScreen)) {
                mBinding.mListView.scrollToPosition(itemCountInTwoScreen)
                mBinding.mListView.post(smoothToTopRunnable)
                return
            }
        }
        mBinding.mListView.smoothScrollToPosition(0)
    }

    private fun checkJumpRecorderActivity() {
        val act = activity ?: return
        if (act.intent.isFromOther()) return
        if (mBrowseViewModel.isAlreadyStartRecorded) return
        if (false == recorderViewModelApi?.hasInitRecorderService()) return
        if (false == recorderViewModelApi?.isStartServiceFromOtherApp()) return
        if (false == RecordRouterManager.instance?.getBaseActivityIsSelf(act)) return
        /*
         修复：ID:8386477 标题:【录音】【卡片】【高概率】【23105】录音自动开启了音频录制。
         文件保存过程中，不要进入录制界面。否则会开启新的录制
         */
        if (recorderViewModelApi?.isRecordSaving() == true) {
            DebugUtil.d(TAG, "checkJumpRecorderActivity: record file is saving")
            return
        }

        mBrowseViewModel.isAlreadyStartRecorded = true
        mBinding.vDelayJumpMask.visibility = View.VISIBLE
        mBinding.vDelayJumpMask.postDelayed({
            mBrowseViewModel.isDelayStartRecorder = true
            mBrowseViewModel.onClickStartRecorderActivity(
                this, isSmallWindowHeight(), false,
                checkIsCall = false
            )
        }, DELAY_TIME)
    }

    /**
     * 如果当前window的高度小于240
     * 需要隐藏掉录制的三种模式选择
     * UI
     */
    private val mLayoutChangeListener: WindowLayoutChangeListener =
        object : WindowLayoutChangeListener() {
            override fun onLayoutChange(v: View?, rect: Rect, oldRect: Rect) {
                DebugUtil.i(TAG, "mLayoutChangeListener  首页。。。")
                activity?.let {
                    setRecorderPanelHeight()
                    //非搜索模式下标题栏适配列表高度，避免首次去更新oldRect（0，0，0，0）
                    if ((!oldRect.isEmpty) && (mBrowseViewModel.showSearch.value == false) &&
                        (PermissionUtils.hasReadAudioPermission())
                    ) {
                        if (::mAdapter.isInitialized && mAdapter.getRealItemCount() > 0) {
                            mBehavior?.onListScroll()
                        }
                    }
                    updatePullRefreshRoot()
                    setOtherViewBottomMargin()
                }
            }
        }

    private fun changeBehaviorCurrentStatusByScreenHeight(showSearch: Boolean?) {
        if (showSearch == true) {
            if (LandScapeUtil.spitWindowHeightLessThanForPlay450(activity)) {
                mBrowseViewModel.behaviorCurrentStatus.value = SET_BEHAVIOR_STATUS_CODE_3
            } else {
                mBrowseViewModel.behaviorCurrentStatus.value = SET_BEHAVIOR_STATUS_CODE_2
            }
        } else {
            if (LandScapeUtil.spitWindowHeightLessThanForPlay450(activity)) {
                mBrowseViewModel.behaviorCurrentStatus.value = SET_BEHAVIOR_STATUS_CODE_1
            } else {
                mBrowseViewModel.behaviorCurrentStatus.value = SET_BEHAVIOR_STATUS_CODE_0
            }
        }
    }

    /**
     * behavior折叠
     */
    private fun setBehaviorUnexpandSubTitle() {
        DebugUtil.i(TAG, "setBehaviorUnexpandSubTitle >>")
        mBinding.abl.removeCallbacks(unexpandSubTitleRunnable)
        mBinding.abl.post(unexpandSubTitleRunnable)
    }

    private val unexpandSubTitleRunnable = Runnable {
        //横屏的时候将PlaceHolder的高度置为browseToolbar的高度
        mAdapter.setPlaceHolder(mBinding.browseToolbar.height)
        //将Behavior收起来
        mBehavior?.unexpandSubTitle()
        //断开Behavior与recyclerView之间的联系
        mBehavior?.setCanScroll(false)
        setOtherViewBottomMargin()
    }


    private val expandSubTitleRunnable = Runnable {
        setBehaviorExpandSubTitleByFirstViewLocation()
    }

    /**
     * behavior 展开
     */
    private fun setBehaviorExpandSubTitle() {
        DebugUtil.i(TAG, "setBehaviorExpandSubTitle >>")
        mBinding.abl.removeCallbacks(expandSubTitleRunnable)
        mBinding.abl.post(expandSubTitleRunnable)
    }

    /**
     * 判断Behavior是否需要真正的展开，因为文件超过竖屏一屏后，往上滑的时候behavior是折叠起来的
     * 此时就不需要展开
     */
    private fun setBehaviorExpandSubTitleByFirstViewLocation() {
        val firstBrowseViewMoreThanListView = mBehavior?.getFirstBrowseViewMoreThanListView()
        DebugUtil.i(TAG, "setBehaviorExpandSubTitleByFirstViewLocation >> $firstBrowseViewMoreThanListView")
        //竖屏的时候置为true 使 Behavior与recyclerView连上
        mBehavior?.setCanScroll(true)
        when (firstBrowseViewMoreThanListView) {
            PrimaryTitleBehavior.LOCATION_STATE_BELOW, PrimaryTitleBehavior.LOCATION_STATE_BOTH -> {
                mBehavior?.expandSubTitle()
                executeInSearchAnim()
                smoothScrollToTop()
            }

            PrimaryTitleBehavior.LOCATION_STATE_ABOVE -> {
                if (mBehavior?.isDoTopMarginAnim == true) {
                    /**
                     * 这种状态的话 是竖屏搜索到横屏搜索，再切回竖屏搜索，期间没有退出搜索的情况，因为切到竖屏的时候，第一个数据项被顶上去了
                     * 回来的时候还是被顶上去的，所以拿到的坐标就是之上的情况，所以到单独处理一下
                     */
                    mBehavior?.expandSubTitle()
                }
            }
        }
        setOtherViewBottomMargin()
    }

    /**
     * 是否需要强制执行进入搜索的动画
     */
    private fun executeInSearchAnim() {
        if (mBrowseViewModel.behaviorCurrentStatus.value == SET_BEHAVIOR_STATUS_CODE_2) {
            //这里如果是竖屏搜索的状态，需要强制执行一下顶上去的动画，不然会有空白
            mSearchFragment?.inSearch(true)
            //重建时用到这个值
            mBrowseViewModel.showSearchUseMaxHeight = true
        }
    }

    /**
     * -1 初始值
     * 0  大于450的状态(竖屏)   非搜索  正常显示
     * 1  小于450的状态(横屏)    非搜索  折叠
     * 2  大于450的状态(竖屏)   搜索     退出时展开
     * 3 小于450的状态 (横屏)   搜索     都不展开
     *
     * 变化的几种状态
     * a.竖屏非搜索 到 横屏非搜索  Behavior 折叠
     * b.横屏非搜索 到 竖屏非搜索  根据计算判断Behavior是否展开
     * c.竖屏搜索 到 横屏搜索  不做处理，应为竖屏搜索已经对Behavior做了处理
     * d.横屏搜索 到 横屏非搜索 ，判断Behavior是否折叠，未折叠就折叠
     * e.横屏搜索 到 竖屏搜索，根据计算判断Behavior是否展开
     * f.横屏搜索 到 横屏非搜索 ，不做处理
     **/

    private fun changeBehaviorSubTitle(status: Int) {
        DebugUtil.i(TAG, "setBehaviorStatus status :$status     lastStatus: ${mBrowseViewModel.lastSetBehaviorStatus}")
        // 之后需要把这里的代码移除，更改横屏自检的问题
        if (!FunctionOption.PHONE_NEED_HORIZONTAL) {
            return
        }
        when (status) {
            SET_BEHAVIOR_STATUS_CODE_0 -> {
                /**
                 * 2到0  竖屏搜索到竖屏非搜索，不用处理，搜索退出的动画就已经处理了
                 * 1到0，横屏到竖屏的变化
                 */
                if (mBrowseViewModel.lastSetBehaviorStatus == SET_BEHAVIOR_STATUS_CODE_1) {
                    //竖屏的时候将PlaceHolder高度置为mAppBarLayoutHeight的高度,这个要放在主线程中，放在runnable中会影响第一个item坐标的计算
                    mAdapter.setPlaceHolder(mBehavior?.mAppBarLayoutHeight ?: 0)
                    //执行展开逻辑，方法里面判断是否需要展开
                    setBehaviorExpandSubTitle()
                }
            }

            SET_BEHAVIOR_STATUS_CODE_1 -> {
                /**
                 * 3到1，横屏搜索变为横屏非搜索 ,不需处理
                 * 0到1，竖屏到横屏的变化
                 */
                if (mBrowseViewModel.lastSetBehaviorStatus == SET_BEHAVIOR_STATUS_CODE_0) {
                    setBehaviorUnexpandSubTitle()
                }
            }

            SET_BEHAVIOR_STATUS_CODE_2 -> {
                /**
                 * 0到2，不做处理，搜索进入动画已处理
                 * 3到2，横屏搜索切换为竖屏搜索
                 */
                if (mBrowseViewModel.lastSetBehaviorStatus == SET_BEHAVIOR_STATUS_CODE_3) {
                    //竖屏的时候将PlaceHolder高度置为mAppBarLayoutHeight的高度，这个要放在主线程中，放在runnable中会影响第一个item坐标的计算
                    mAdapter.setPlaceHolder(mBehavior?.mAppBarLayoutHeight ?: 0)
                    //执行展开逻辑，方法里面判断是否需要展开
                    setBehaviorExpandSubTitle()
                }
            }

            SET_BEHAVIOR_STATUS_CODE_3 -> {
                /**
                 * 1到3，横屏非搜索 到 横屏搜索 ，已经折叠，不需处理
                 * 2到3，竖屏搜索 到 横屏搜索
                 */
                if (mBrowseViewModel.lastSetBehaviorStatus == SET_BEHAVIOR_STATUS_CODE_2) {
                    setBehaviorUnexpandSubTitle()
                }
            }
        }
        mBrowseViewModel.lastSetBehaviorStatus = status
    }

    /**
     * 设置录制面板高度以及显示隐藏
     */
    private fun setRecorderPanelHeight() {
        val params = mBinding.gradientBackground.layoutParams
        mBinding.gradientBackground.updatePadding(bottom = nagivationHeight)
        params.height =
            resources.getDimension(R.dimen.recorder_height).toInt() + nagivationHeight
        mBinding.gradientBackground.layoutParams = params
        // 小屏下，按钮距离底部间距大些，中大屏要小点
        mBinding.middleControl.updateLayoutParams<FrameLayout.LayoutParams> {
            bottomMargin =
                resources.getDimension(com.soundrecorder.common.R.dimen.circle_browsefile_button_margin_bottom).toInt()
            val newWidth = resources.getDimension(com.soundrecorder.common.R.dimen.circle_record_button_diam).toInt()
            if (width != newWidth) {
                width = newWidth
                height = newWidth
                mBinding.redCircleIcon.refreshCircleRadius(
                    resources.getDimension(com.soundrecorder.common.R.dimen.circle_record_button_radius)
                )
            }
        }
    }

    /**
     * 判断是否处于分屏且window高度小于240
     */
    private fun isSmallWindowHeight(): Boolean {
        var isSmall = false
        activity?.let {
            if (it.isInMultiWindowMode) {
                val windowParameter = SplitWindowUtil.getCurrentSplitWindowParameter(it)
                DebugUtil.e(
                    TAG,
                    "onLayoutChange windowParameter == ${windowParameter.windowHeight}"
                )
                isSmall = windowParameter.windowHeight < 240
            }
        }
        return isSmall
    }

    /**
     * 初始化云同步
     */
    private fun initCloudState() {
        val act = activity ?: return
        val isNetWorkGranted = cloudKitApi?.isNetWorkGranted(act)
        DebugUtil.d(TAG, "initCloudState, isNetWorkGranted:$isNetWorkGranted")
        if (isNetWorkGranted == true && !mIsInitCloudState) {
            mIsInitCloudState = true
            // 1.先初始化
            mTipStatusObserver?.initCloudState()

            // 2.再触发媒体库全量or云同步操作
            RecordDataSyncHelper.trigRecordSync(BaseApplication.getApplication())
        }
    }

    private fun checkUserNoticeDialogShowing(): Boolean {
        if (mIsNoticeDialogShowing) {
            mIsNoticeDialogShowing = PermissionUtils.getNextAction() == SHOULD_SHOW_USER_NOTICE
        }
        DebugUtil.d(TAG, "checkUserNoticeDialogShowing: $mIsNoticeDialogShowing")
        return mIsNoticeDialogShowing
    }

    /**
     * 首页是否正在快捷播放
     * @return true: 有文件正在快捷播放
     * @return false：没有文件再快捷播放
     */
    fun isFastPlaying(): Boolean {
        return if (::mBrowseViewModel.isInitialized) {
            mBrowseViewModel.fastPlayHelper.hasPlayingRecord()
        } else false
    }

    private fun notifySummaryStateChanged(intent: Intent?) {
        val from = intent?.getStringExtraSecure(BUNDLE_FROM_WHERE) ?: return
        val defaultMediaId = -1L
        val mediaId = intent.getLongExtra(BUNDLE_MEDIA_ID, defaultMediaId)
        val noteId = intent.getStringExtraSecure(BUNDLE_NOTE_ID)
        val callId = intent.getStringExtraSecure(BUNDLE_CALL_ID)
        DebugUtil.i(TAG, "notifySummaryStateChanged from=$from, mediaId=$mediaId,noteId=$noteId,callId=$callId")
        if (mediaId != defaultMediaId && !noteId.isNullOrEmpty() && !callId.isNullOrEmpty()) {
            runCatching {
                if (isGroupingByContact()) {
                    notifyCallGroupSummaryState(mediaId, noteId, callId, from)
                } else {
                    notifyRecordSummaryState(mediaId, noteId, callId, from)
                }
            }.onFailure {
                DebugUtil.e(TAG, "notifySummaryStateChanged error$it")
            }
        }
    }

    private fun notifyRecordSummaryState(
        mediaId: Long,
        noteId: String?,
        callId: String?,
        from: String
    ) = mAdapter.getData()?.forEachIndexed { index, itemModel ->
        if (itemModel.mediaId == mediaId) {
            itemModel.noteId = noteId
            itemModel.recordUUID = callId
            if (SummaryStaticUtil.EVENT_FROM_RECORD == from && !TipUtil.hasShowTip(TipUtil.TYPE_SUMMARY_TIPS)) {
                // 录制入口的生成摘要且未显示过摘要tip才显示新手引导
                ItemBrowseRecordViewModel.summaryTipMediaId[taskId] = mediaId
            }
            mAdapter.notifyItemChanged(index + 1 + mAdapter.getHeaderSize())
            return@forEachIndexed
        }
    }

    private fun notifyCallGroupSummaryState(
        mediaId: Long,
        noteId: String?,
        callId: String?,
        from: String
    ) = mAdapter.getData()?.forEachIndexed { index, itemModel ->
        itemModel.recordList?.forEach { record ->
            if (record.mediaId == mediaId) {
                record.noteId = noteId
                record.recordUUID = callId
                if (SummaryStaticUtil.EVENT_FROM_RECORD == from && !TipUtil.hasShowTip(TipUtil.TYPE_SUMMARY_TIPS)) {
                    // 录制入口的生成摘要且未显示过摘要tip才显示新手引导
                    ItemBrowseRecordViewModel.summaryTipMediaId[taskId] = mediaId
                }
                mAdapter.notifyItemChanged(index + 1 + mAdapter.getHeaderSize())
                return@forEach
            }
        }
    }

    /**
     * 点击摘要图标跳转到便签
     */
    private fun clickSummaryIcon(summaryCallId: String?, summaryNoteId: String?) {
        val act = activity ?: kotlin.run {
            DebugUtil.e(TAG, "clickSummaryIcon activity is null!")
            return
        }
        val callId = summaryCallId ?: return
        val noteId = summaryNoteId ?: return
        clickedItemSummaryNoteId = noteId
        summaryApi?.toNotesSummaryActivity(
            act, callId, noteId, REQUEST_CODE_START_NOTE
        ) { clearSummary, disableDialog ->
            if (clearSummary) {
                mBrowseViewModel.clearAllSummary()
            } else {
                <EMAIL> = disableDialog
            }
        }
        SummaryStaticUtil.addClickViewSummaryEvent(SummaryStaticUtil.EVENT_FROM_MAIN)
    }

    private fun responseStartNote(resultCode: Int, data: Intent?) {
        if (resultCode == Constants.NOTES_RESULT_CODE_ERROR) {
            val uuid = data?.getStringExtra("speech_log_id")
            val noteId = data?.getStringExtra("note_id")
            DebugUtil.e(logTag, "<<< responseStartNote callId=$uuid, noteId=$noteId, clickedItemSummaryNoteId=$clickedItemSummaryNoteId")

            lifecycleScope.launch(Dispatchers.IO) {
                /*兼容老版本便签不支持noteId跳转的，所以也不会回传noteId*/
                val summaryNoteId = noteId ?: clickedItemSummaryNoteId
                if (!summaryNoteId.isNullOrEmpty()) {
                    mBrowseViewModel.clearSummaryByNoteId(summaryNoteId)
                    mBrowseFileActivityViewModel.beDeleteSummaryNoteId.postValueSafe(summaryNoteId)
                }
                withContext(Dispatchers.Main) {
                    mSearchFragment?.onQueryTextChange(mBrowseViewModel.searchValue.value)
                }
            }
            ToastManager.showShortToast(BaseApplication.getAppContext(), com.soundrecorder.common.R.string.tip_summary_be_deleted)
        } else {
            DebugUtil.i(logTag, "<<< responseStartNote resultCode > $resultCode")
        }
    }

    override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
    }

    override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
        if (mBrowseViewModel.searchValue.value != s) {
            mBrowseViewModel.releasePlayer()
        }
        mBrowseViewModel.searchValue.value = s?.toString()
    }

    override fun afterTextChanged(s: Editable?) {
        mCacheHolder.mSearchAnimView?.apply {
            quickDeleteButton.contentDescription = getString(com.soundrecorder.base.R.string.role_name_tag_clear)
        }
    }

    private fun showGroupChooseFragment() {
        val fm = activity?.supportFragmentManager
        fm?.let {
            mGroupViewModel.showChooseGroupFragment(it)
        }
    }

    /**
     * 显示 “请稍后。。。” dialog
     * 当转文本的文件大于  Constants.SHOW_WAITING_DIALOG__THRESHOLD 的时候，保存到本地和分享需要弹窗
     * 弹窗显示不足1s的时候，需要延迟到1s之后再关闭
     */
    private fun showShareWaitingDialog(@StringRes msgResId: Int = com.soundrecorder.common.R.string.waiting) {
        val activity = activity ?: return
        if (shareWaitingDialog == null) {
            shareWaitingDialog = LoadingDialog(activity)
        }
        //修复loading dialog只显示一次的问题
        if (shareWaitingDialog?.isActivityNull() == true) {
            shareWaitingDialog?.resetActivity(activity)
        }
        shareWaitingDialog?.show(msgResId)
    }

    /**
     * 关闭waitingDialog并且显示分享面板
     */
    private fun dismissShareWaitingDialog() {
        if (shareWaitingDialog?.isShowing() == true) {
            shareWaitingDialog?.dismiss()
        }
    }

    /**
     * 设置 BrowseFragment 在无障碍talkback下的可访问性
     */
    private fun setBrowseFragmentAccessibility(accessibility: Int) {
        val fragments: MutableList<Fragment>? = activity?.supportFragmentManager?.fragments
        fragments?.find { it is BrowseFragment }?.view?.apply {
            importantForAccessibility =
                accessibility
            if (accessibility == View.IMPORTANT_FOR_ACCESSIBILITY_YES) {
                postDelayed(
                    { sendAccessibilityEvent(AccessibilityEvent.TYPE_WINDOW_STATE_CHANGED) },
                    DELAY_WINDOW_STATE_CHANGED_TIME
                )
            }
        }
    }

    override fun onEditorAction(v: TextView?, actionId: Int, event: KeyEvent?): Boolean {
        if (actionId == EditorInfo.IME_ACTION_SEARCH) {
            // 搜索键被点击
            v?.text?.toString()?.let { mSearchFragment?.addSearchHistory(it) }
            return true; // 表示事件已处理
        }
        return false
    }

    private class MediaStoreContentObserver(fragment: BrowseFragment) : ContentObserver(Handler(Looper.getMainLooper())) {
        private var weakFragment: WeakReference<BrowseFragment> = WeakReference(fragment)

        override fun onChange(selfChange: Boolean, uri: Uri?) {
            val fragment = weakFragment.get() ?: return
            if (fragment.isDetached) return
            DebugUtil.d(TAG, "onChange MediaStoreContentObserver: $uri")
            fragment.mBrowseFileActivityViewModel.isFileChanged = true
        }
    }

    private class BrowseFileEventListener(fragment: BrowseFragment) : OnFileEventListener {
        private var weakFragment: WeakReference<BrowseFragment> = WeakReference(fragment)
        override fun onFileObserver(event: Int, path: String?, allPath: String?) {
            val fragment = weakFragment.get() ?: return
            if (fragment.isDetached) return

            fragment.onFileObserver(event, path, allPath)
        }
    }
}