/************************************************************
 * Copyright 2000-2021 OPlus Mobile Comm Corp., Ltd.
 * All rights reserved.
 * FileName      : BrowseAdapter.kt
 * Version Number: 1.0
 * Description   :
 * Author        : tianjun
 * Date          : 2021.06.04
 * History       :(ID,  2021.06.04, tianjun, Description)
 */
package com.soundrecorder.browsefile.home.item

import android.animation.Animator
import android.app.Activity
import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.LinearLayout
import android.widget.TextView
import androidx.core.view.updateLayoutParams
import androidx.databinding.DataBindingUtil
import androidx.lifecycle.LifecycleOwner
import androidx.recyclerview.widget.COUIRecyclerView
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.soundrecorder.base.utils.ClickUtils
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.NumberConstant
import com.soundrecorder.base.view.IRecyclerAdapterData
import com.soundrecorder.browsefile.R
import com.soundrecorder.browsefile.databinding.ItemBrowseBinding
import com.soundrecorder.browsefile.home.ItemAnimationUtil
import com.soundrecorder.common.transition.RemoveItemAnimator
import com.soundrecorder.common.utils.ViewUtils.dp2px
import com.soundrecorder.common.utils.ViewUtils.setSmoothRoundCorner
import com.soundrecorder.common.utils.click
import com.soundrecorder.common.utils.gone
import com.soundrecorder.common.utils.visible
import com.soundrecorder.common.widget.CircleTextImage

class BrowseAdapter(private val owner: LifecycleOwner, private val mContext: Context, private val mBrowseViewListener: IBrowseViewHolderListener) :
    RecyclerView.Adapter<RecyclerView.ViewHolder>(), IRecyclerAdapterData {
    companion object {
        const val TYPE_HEADER_CLOUD = -1
        const val TYPE_HEADER_CALL_GROUP_MORE = -7
        const val TYPE_HEADER_RECYCLE = -6
        const val TYPE_HEADER_CLOUD_CONFIG = -5
        const val TYPE_HEADER_QUESTION = -4
        const val TYPE_FOOTER = -2
        const val TYPE_PLACEHOLDER = -3
        const val TYPE_BROWSE = 1
        const val TYPE_CALL_CONTACT_GROUP = 2
        const val TAG = "BrowseAdapter"
        const val DURATION_ADD = 317L
        const val DURATION_CHANGE = 383L
        const val DURATION_REMOVE = 233L
        const val TIPS_START = 206L
        const val TIPS_END = 410L

        private const val DELAY_REFRESH = 1000L
    }

    private var bindRecyclerView: RecyclerView? = null
    private val mLayoutInflater = LayoutInflater.from(mContext)

    private var mPlaceHolderView: View = View(mContext).apply {
        layoutParams = RecyclerView.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, 0)
        visibility = View.INVISIBLE
    }

    private var mFooterView: View = View(mContext).apply {
        layoutParams = RecyclerView.LayoutParams(
            ViewGroup.LayoutParams.MATCH_PARENT, mContext.resources.getDimension(
                R.dimen.recorder_height
            ).toInt()
        )
        visibility = View.INVISIBLE
    }

    private val mSingHeader: HashMap<Int, View> = HashMap()
    private var mContentData: MutableList<ItemBrowseRecordViewModel>? = null
    private var isGroupingByContact: Boolean = false
    private var isDeleteing: Boolean = false
    private var mSelectedMediaIdList: ArrayList<String>? = null

    fun setGroupingByContact(isContactGroup: Boolean) {
        this.isGroupingByContact = isContactGroup
        notifyDataSetChanged()
    }

    fun setData(data: List<ItemBrowseRecordViewModel>?, isContactGroup: Boolean = false) {
        val oldData = mContentData
        val oldIsGroupingByContact = this.isGroupingByContact

        mContentData = data?.toMutableList()
        this.isGroupingByContact = isContactGroup

        // 优化数据更新：只有在数据真正发生变化时才刷新UI
        if (shouldNotifyDataSetChanged(oldData, data, oldIsGroupingByContact, isContactGroup)) {
            // 使用post延迟执行，避免阻塞当前帧的渲染
            (mContext as? Activity)?.runOnUiThread {
                notifyDataSetChanged()
            }
        }
    }

    /**
     * 判断是否需要刷新整个列表
     * 避免不必要的UI刷新，减少卡顿
     */
    private fun shouldNotifyDataSetChanged(
        oldData: List<ItemBrowseRecordViewModel>?,
        newData: List<ItemBrowseRecordViewModel>?,
        oldIsGroupingByContact: Boolean,
        newIsGroupingByContact: Boolean
    ): Boolean {
        // 分组方式改变，必须刷新
        if (oldIsGroupingByContact != newIsGroupingByContact) {
            return true
        }

        // 数据为空的情况
        if (oldData.isNullOrEmpty() && newData.isNullOrEmpty()) {
            return false
        }

        // 一个为空一个不为空，需要刷新
        if (oldData.isNullOrEmpty() || newData.isNullOrEmpty()) {
            return true
        }

        // 数据大小不同，需要刷新
        if (oldData.size != newData.size) {
            return true
        }

        // 简单比较前几个元素的ID，避免全量比较造成性能问题
        val compareCount = minOf(oldData.size, newData.size, 5)
        for (i in 0 until compareCount) {
            if (oldData[i].mediaId != newData[i].mediaId) {
                return true
            }
        }

        return false
    }

    fun getData(): MutableList<ItemBrowseRecordViewModel>? {
        return mContentData
    }

    fun setFooter(height: Int) {
        mFooterView.updateLayoutParams {
            this.height = height
        }
    }

    fun getFooter(): View = mFooterView

    fun setPlaceHolder(height: Int) {
        mPlaceHolderView.updateLayoutParams {
            this.height = height
        }
    }

    fun setHeader(headerType: Int, header: View?) {
        DebugUtil.i(TAG, "setHeader,type=$headerType,header=$header,equals:${header == mSingHeader[headerType]}")
        val oldHeader = mSingHeader[headerType]
        if (header != null && oldHeader != header) {
            mSingHeader[headerType] = header
            if (oldHeader == null) {
                notifyItemInserted(1)
            } else {
                notifyItemChanged(1)
            }
        } else if (header == null && oldHeader != null) {
            mSingHeader.remove(headerType)
            notifyDataSetChanged()
        }
    }

    @Suppress("TooGenericExceptionCaught")
    fun deleteItems(selectedMediaIdList: ArrayList<String>?, runnable: Runnable?): Long {
        var delayRefresh = 0L
        if (selectedMediaIdList.isNullOrEmpty() || mContentData.isNullOrEmpty()) {
            return delayRefresh
        }
        DebugUtil.d(TAG, "deleteItems, selectedMediaIdList:$selectedMediaIdList")
        try {
            var iterator: Iterator<BaseItemRecordViewModel>?
            var index: Int
            for (mediaId in selectedMediaIdList) {
                index = 1 + mSingHeader.size
                iterator = mContentData!!.iterator()
                while (iterator.hasNext()) {
                    val itemRecord = iterator.next()
                    if (itemRecord.mediaId.toString() == mediaId) {
                        //DebugUtil.d(TAG, "deleteItems, itemRecord.mediaId:${itemRecord.mediaId}")
                        iterator.remove()
                        notifyItemRemoved(index)
                        break
                    }
                    index++
                }
            }

            if (mContentData.isNullOrEmpty()) {
                runnable?.run()
            } else {
                bindRecyclerView?.postDelayed(runnable, DELAY_REFRESH)
                delayRefresh = DELAY_REFRESH
            }
        } catch (e: Exception) {
            DebugUtil.e(TAG, "deleteItems error! ${e.message}")
            runnable?.run()
        }
        return delayRefresh
    }

    fun deleteGroupReocrdItems(selectedMediaIdList: ArrayList<String>?, runnable: Runnable?): Long {
        if (selectedMediaIdList.isNullOrEmpty() || mContentData.isNullOrEmpty()) {
            return DELAY_REFRESH
        }
        /*val realPos = position - mSingHeader.size - 1
        notifyItemChanged(position)*/
        isDeleteing = true
        mSelectedMediaIdList = selectedMediaIdList
        notifyDataSetChanged()

        return DELAY_REFRESH
    }

    fun hideCloudHeader() {
        ignoreHeaderAnimation(TYPE_HEADER_CLOUD, true)
    }

    fun hideCloudConfigHeader() {
        ignoreHeaderAnimation(TYPE_HEADER_CLOUD_CONFIG, true)
    }

    fun hideQuestionHeader(animEndFuc: (() -> Unit)? = null) {
        ignoreHeaderAnimation(TYPE_HEADER_QUESTION, true, animEndFuc)
    }

    fun hideRecycleHeader() {
        ignoreHeaderAnimation(TYPE_HEADER_RECYCLE, true)
    }

    fun hideCallGroupMoreHeader() {
        ignoreHeaderAnimation(TYPE_HEADER_CALL_GROUP_MORE, true)
    }

    fun getHeadeView(headerType: Int): View? {
        return mSingHeader[headerType]
    }

    fun getHeaderSize(): Int = mSingHeader.size

    //Step 1
    override fun getItemCount(): Int = getRealItemCount() + mSingHeader.size + 2

    override fun getRealItemCount(): Int = mContentData?.size ?: 0

    override fun getRealPosInViewType(position: Int): Int {
        return if (position < 0) {
            position
        } else if (position == 0 || position == itemCount - 1) {
            //placeHolder
            0
        } else if (position < mSingHeader.size + 1) {
            //header
            position - 1
        } else {
            //item
            position - mSingHeader.size - 1
        }
    }

    //Step 2
    override fun getItemViewType(position: Int): Int {
        //DebugUtil.i(TAG, "getItemViewType postion ${position}, itemCount ${itemCount}, mSingHeader.size() ${mSingHeader.size}")
        return when {
            position == 0 -> TYPE_PLACEHOLDER
            position == itemCount - 1 -> TYPE_FOOTER
            (mSingHeader.size > 0) && (position < mSingHeader.size + 1) -> {
                if (mSingHeader[TYPE_HEADER_QUESTION] != null) {
                    DebugUtil.i(TAG, "getItemViewType postion $position, HEAD QUESTION")
                    TYPE_HEADER_QUESTION
                } else if (mSingHeader[TYPE_HEADER_CLOUD] != null) {
                    DebugUtil.i(TAG, "getItemViewType postion $position, HEAD CLOUD")
                    TYPE_HEADER_CLOUD
                } else if (mSingHeader[TYPE_HEADER_CLOUD_CONFIG] != null) {
                    DebugUtil.i(TAG, "getItemViewType postion $position, HEAD CLOUD_CONFIG")
                    TYPE_HEADER_CLOUD_CONFIG
                } else if (mSingHeader[TYPE_HEADER_RECYCLE] != null) {
                    TYPE_HEADER_RECYCLE
                } else if (mSingHeader[TYPE_HEADER_CALL_GROUP_MORE] != null) {
                    TYPE_HEADER_CALL_GROUP_MORE
                } else {
                    throw IllegalStateException("Wrong position")
                }
            }

            else -> {
                if (isGroupingByContact) {
                    TYPE_CALL_CONTACT_GROUP
                } else {
                    TYPE_BROWSE
                }
                /*when (mContentData?.get(position - mSingHeader.size - 1)) {
                    is ItemBrowseRecordViewModel  -> TYPE_BROWSE
                    else -> throw IllegalStateException("Wrong position")
                }*/
            }
        }
    }

    //Step 3
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return when (viewType) {
            TYPE_PLACEHOLDER -> object : RecyclerView.ViewHolder(mPlaceHolderView) {}
            TYPE_HEADER_QUESTION -> {
                DebugUtil.i(TAG, "onCreateViewHolder: typeHeader $mSingHeader")
                val contentLayout = FrameLayout(mContext).apply {
                    layoutParams = RecyclerView.LayoutParams(
                        ViewGroup.LayoutParams.MATCH_PARENT,
                        ViewGroup.LayoutParams.WRAP_CONTENT
                    )
                }
                object : RecyclerView.ViewHolder(contentLayout) {}
            }

            TYPE_HEADER_CLOUD -> {
                DebugUtil.i(TAG, "onCreateViewHolder: typeHeader $mSingHeader")
                val contentLayout = FrameLayout(mContext).apply {
                    layoutParams = RecyclerView.LayoutParams(
                        ViewGroup.LayoutParams.MATCH_PARENT,
                        ViewGroup.LayoutParams.WRAP_CONTENT
                    )
                }
                object : RecyclerView.ViewHolder(contentLayout) {}
            }

            TYPE_HEADER_CLOUD_CONFIG -> {
                DebugUtil.i(TAG, "onCreateViewHolder: typeHeader $mSingHeader")
                val contentLayout = FrameLayout(mContext).apply {
                    layoutParams = RecyclerView.LayoutParams(
                        ViewGroup.LayoutParams.MATCH_PARENT,
                        ViewGroup.LayoutParams.WRAP_CONTENT
                    )
                }
                object : RecyclerView.ViewHolder(contentLayout) {}
            }

            TYPE_HEADER_RECYCLE -> {
                DebugUtil.i(TAG, "onCreateViewHolder: typeHeader $mSingHeader")
                val contentLayout = FrameLayout(mContext).apply {
                    layoutParams = RecyclerView.LayoutParams(
                        ViewGroup.LayoutParams.MATCH_PARENT,
                        ViewGroup.LayoutParams.WRAP_CONTENT
                    )
                }
                object : RecyclerView.ViewHolder(contentLayout) {}
            }

            TYPE_HEADER_CALL_GROUP_MORE -> {
                DebugUtil.i(TAG, "onCreateViewHolder: typeHeader $mSingHeader")
                val contentLayout = FrameLayout(mContext).apply {
                    layoutParams = RecyclerView.LayoutParams(
                        ViewGroup.LayoutParams.MATCH_PARENT,
                        ViewGroup.LayoutParams.WRAP_CONTENT
                    )
                }
                object : RecyclerView.ViewHolder(contentLayout) {}
            }

            TYPE_BROWSE -> {
                val item: ItemBrowseBinding = DataBindingUtil.inflate(
                    mLayoutInflater,
                    R.layout.item_browse,
                    parent,
                    false
                )
                item.checkboxLayout.setSmoothRoundCorner(dp2px(NumberConstant.NUM_F12_0))
                ItemBrowseViewHolder(
                    item, mBrowseViewListener,
                    false
                ).also {
                    it.adapterData = this
                }
            }

            TYPE_CALL_CONTACT_GROUP -> {
                ItemCallGroupParentViewHolder(
                    LayoutInflater.from(mContext).inflate(
                        R.layout.item_call_record_group,
                        parent,
                        false
                    )
                )
            }

            TYPE_FOOTER -> object : RecyclerView.ViewHolder(mFooterView) {}
            else -> throw IllegalStateException("Wrong item view type=$viewType")
        }
    }

    //Step 4
    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        //DebugUtil.i(TAG, "position >>> $position")
        val taskId = (holder.itemView.context as Activity).taskId
        when (getItemViewType(position)) {
            TYPE_BROWSE -> {
                mContentData?.let {
                    val realPos = position - mSingHeader.size - 1
                    val item = it[realPos]
                    item.taskId = taskId
                    (holder as? ItemBrowseViewHolder)?.onBindViewHolder(item, owner)
                }
            }

            TYPE_CALL_CONTACT_GROUP -> {
                if (holder is ItemCallGroupParentViewHolder) {
                    bindCallGroupHolder(holder, position)
                }
            }

            TYPE_HEADER_CLOUD -> {
                val cloudGuideTipView = mSingHeader[TYPE_HEADER_CLOUD]
                cloudGuideTipView?.parent?.let {
                    (it as? ViewGroup)?.removeView(cloudGuideTipView)
                }
                if (holder.itemView is FrameLayout && cloudGuideTipView != null) {
                    val itemView = holder.itemView as FrameLayout
                    itemView.removeAllViews()
                    itemView.addView(cloudGuideTipView)
                }
                //DebugUtil.e(TAG, "onBindViewHolder Cloud header ${position}, cloudTipView $cloudGuideTipView, visible ")
            }

            TYPE_HEADER_CLOUD_CONFIG -> {
                val cloudConfigTipView = mSingHeader[TYPE_HEADER_CLOUD_CONFIG]
                cloudConfigTipView?.parent?.let {
                    (it as? ViewGroup)?.removeView(cloudConfigTipView)
                }
                if (holder.itemView is FrameLayout && cloudConfigTipView != null) {
                    val itemView = holder.itemView as FrameLayout
                    itemView.removeAllViews()
                    itemView.addView(cloudConfigTipView)
                }
            }

            TYPE_HEADER_RECYCLE -> {
                val recycleHeaderView = mSingHeader[TYPE_HEADER_RECYCLE]
                recycleHeaderView?.parent?.let {
                    (it as? ViewGroup)?.removeView(recycleHeaderView)
                }
                if (holder.itemView is FrameLayout && recycleHeaderView != null) {
                    val itemView = holder.itemView as FrameLayout
                    itemView.removeAllViews()
                    itemView.addView(recycleHeaderView)
                }
            }

            TYPE_HEADER_CALL_GROUP_MORE -> {
                val callGroupMoreHeaderView = mSingHeader[TYPE_HEADER_CALL_GROUP_MORE]
                callGroupMoreHeaderView?.parent?.let {
                    (it as? ViewGroup)?.removeView(callGroupMoreHeaderView)
                }
                if (holder.itemView is FrameLayout && callGroupMoreHeaderView != null) {
                    val itemView = holder.itemView as FrameLayout
                    itemView.removeAllViews()
                    itemView.addView(callGroupMoreHeaderView)
                }
            }

            TYPE_HEADER_QUESTION -> {
                val questionTipView = mSingHeader[TYPE_HEADER_QUESTION]
                questionTipView?.parent?.let {
                    (it as? ViewGroup)?.removeView(questionTipView)
                }
                if (holder.itemView is FrameLayout && questionTipView != null) {
                    val itemView = holder.itemView as FrameLayout
                    itemView.removeAllViews()
                    itemView.addView(questionTipView)
                }
                //DebugUtil.e(TAG, "onBindViewHolder Question header ${position}, questionTipeView : $questionTipView")
            }

            else -> {}
        }
    }

    private fun bindCallGroupHolder(
        holder: ItemCallGroupParentViewHolder,
        position: Int
    ) {
        val realPos = position - mSingHeader.size - 1
        val itemGroupParent = mContentData?.get(realPos)
        itemGroupParent?.let {
            holder.tvName.text = itemGroupParent.callerName
            holder.civImage.setCircleImageText(itemGroupParent.callerName)
            holder.civImage.setCircleImageColor(itemGroupParent.avatarColor)
            holder.childAdapter = CallGroupChildRecordAdapter(mContext, owner, mBrowseViewListener)
            if (itemGroupParent.recordList.isNullOrEmpty()) {
                holder.llGroupMore.gone()
            }
            if (itemGroupParent.totalRecordCount > NumberConstant.NUM_3) {
                holder.llGroupMore.visible()
                holder.tvCallMore.text = itemGroupParent.totalRecordCount.toString()
                holder.llGroupMore.click {
                    if (!ClickUtils.isQuickClick()) {
                        mBrowseViewListener.onCallGroupMoreClick(it, itemGroupParent)
                    }
                }
            } else {
                holder.llGroupMore.gone()
            }
            itemGroupParent.recordList?.let {
                var childList: MutableList<ItemBrowseRecordViewModel>? = null
                if (it.size >= NumberConstant.NUM_3) {
                    childList = it.subList(0, NumberConstant.NUM_3)
                } else {
                    childList = it
                }
                holder.childAdapter?.setData(childList)
            }

            holder.rvChildList.apply {
                layoutManager = LinearLayoutManager(mContext, LinearLayoutManager.VERTICAL, false)
                adapter = holder.childAdapter
                isNestedScrollingEnabled = false
                setDispatchEventWhileScrolling(true)
            }

            if (mSelectedMediaIdList != null && mSelectedMediaIdList?.isNotEmpty() == true) {
                holder.childAdapter?.deleteItems(mSelectedMediaIdList) { delay ->
                    isDeleteing = false
                    mSelectedMediaIdList?.clear()
                    mSelectedMediaIdList = null
                    mBrowseViewListener.onGroupRecordDeleteSuccess(delay, true)
                }
            }
        }
    }

    class ItemCallGroupParentViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {

        /*fun bindViewHolder(itemGroupParent: ItemBrowseRecordViewModel) {
            binding.itemGroupParent = itemGroupParent
            binding.tvCallName.setText(itemGroupParent.callerName)
        }*/

        val civImage = itemView.findViewById<CircleTextImage>(R.id.civ_call_image)
        val tvName = itemView.findViewById<TextView>(R.id.tv_call_name)
        val rvChildList = itemView.findViewById<COUIRecyclerView>(R.id.rv_call_child_list)
        val llGroupMore = itemView.findViewById<LinearLayout>(R.id.ll_call_group_more)
        val tvCallMore = itemView.findViewById<TextView>(R.id.tv_call_more)
        var childAdapter: CallGroupChildRecordAdapter? = null
    }

    fun isContentNotEmpty(): Boolean = mContentData?.isNotEmpty() ?: false

    fun isHeaderNotEmpty(): Boolean = mSingHeader.isNotEmpty()

    fun isHeaderEmpty(): Boolean = mSingHeader.isEmpty()

    override fun onViewRecycled(holder: RecyclerView.ViewHolder) {
        if (holder is ItemBrowseViewHolder) {
            holder.onViewRecycled()
            super.onViewRecycled(holder)
        }
    }

    /**
     * 隐藏header
     * 动画
     */
    private fun ignoreHeaderAnimation(headerType: Int, shouldAnimate: Boolean, animEndFuc: (() -> Unit)? = null) {
        if (mSingHeader.size <= 0) {
            DebugUtil.e(TAG, "ignoreHeaderAnimation mSingHeader not child")
            return
        }
        val header = mSingHeader[headerType]
        if (header == null) {
            DebugUtil.e(TAG, "header view not in map, return ")
            return
        }
        if (shouldAnimate) {
            val animListener = object : Animator.AnimatorListener {

                override fun onAnimationStart(p0: Animator) {
                    //do nothing
                }

                override fun onAnimationEnd(p0: Animator) {
                    mSingHeader.clear()
                    notifyDataSetChanged()
                    animEndFuc?.invoke()
                }

                override fun onAnimationCancel(p0: Animator) {
                    //do nothing
                }

                override fun onAnimationRepeat(p0: Animator) {
                    //do nothing
                }
            }
            //val header = mSingHeader[0]
            val itemView = header.parent as? View
            val height = header.height
            DebugUtil.d(TAG, "ignoreHeaderAnimation  height = $height")
            ItemAnimationUtil.transAlphaTopHeaderAnimator(
                itemView,
                height,
                1,
                animListener
            )
        } else {
            mSingHeader.clear()
            notifyDataSetChanged()
            animEndFuc?.invoke()
        }
    }

    override fun onAttachedToRecyclerView(recyclerView: RecyclerView) {
        super.onAttachedToRecyclerView(recyclerView)
        bindRecyclerView = recyclerView
        RemoveItemAnimator().let {
            it.addDuration = 0
            it.removeDuration = DURATION_REMOVE
            it.changeDuration = DURATION_CHANGE
            it.moveDuration = DURATION_CHANGE
            recyclerView.itemAnimator = it
        }
    }

    fun clear() {
        mContentData?.clear()
    }

    fun release() {
        if (isHeaderNotEmpty()) {
            mSingHeader.clear()
        }
        mContentData?.clear()
        bindRecyclerView = null
    }
}