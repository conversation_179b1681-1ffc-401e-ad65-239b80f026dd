/*********************************************************************************
 ** Copyright (C) 2020 Oplus. All rights reserver.
 ** COUI_EDIT, All rights reserved.
 **
 ** File: - BottomMarginView.kt
 ** Description: Set margin of [.COUINavigationView] dynamically
 **
 ** Version: 1.1
 ** Date: 2020-04-20
 ** Author: <EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** Yupeng.<PERSON><PERSON>@ROM.SDK             2018-11-25   1.0         Create this module
 ** <EMAIL>              2020-04-20   1.1         Convert this demo into Kotlin
 ********************************************************************************/

package com.coui.uikit.demo.navigationview

import android.view.View
import android.view.ViewGroup
import androidx.annotation.Keep
import java.lang.ref.WeakReference

class BottomMarginView {
    private var mViews: MutableList<WeakReference<View>>? = null

    fun addView(view: View): BottomMarginView {
        if (mViews == null) {
            mViews = ArrayList()
        }
        mViews?.add(WeakReference(view))
        return this
    }

    fun setViewBottomMargin(view: View, margin: Int) {
        val lp = view.layoutParams
        if (lp != null && lp is ViewGroup.MarginLayoutParams) {
            lp.bottomMargin = margin
            view.layoutParams = lp
        }
    }

    fun getViewBottomMargin(view: View): Int {
        val lp = view.layoutParams
        if (lp != null && lp is ViewGroup.MarginLayoutParams) {
            return lp.bottomMargin
        }
        return 0
    }

    @Keep
    fun setBottomMargin(margin: Int) {
        if (mViews != null && mViews?.isNotEmpty()!!) {
            var v: View
            for (vRef in mViews!!) {
                if (vRef.get() != null) {
                    v = vRef.get()!!
                    setViewBottomMargin(v, margin)
                }
            }
        }
    }

    fun getBottomMargin(): Int {
        if (mViews != null && mViews?.isNotEmpty()!!) {
            val view: View
            for (vRef in mViews!!) {
                if (vRef.get() != null) {
                    view = vRef.get()!!
                    return getViewBottomMargin(view)
                }
            }
        }
        return 0
    }
}