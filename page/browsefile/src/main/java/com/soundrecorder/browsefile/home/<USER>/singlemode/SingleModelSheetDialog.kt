package com.soundrecorder.browsefile.home.dialog.singlemode

import android.app.Activity
import androidx.appcompat.app.AlertDialog
import com.coui.appcompat.dialog.COUIAlertDialogBuilder
import com.coui.appcompat.dialog.adapter.ChoiceListAdapter
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.constant.RecordModeConstant

class SingleModelSheetDialog(val activity: Activity?, private val isSupportCall: Boolean) {

    companion object {
        const val DEFAULT = -1
        const val SINGLE_CHECK_ITEM_ALL = RecordModeConstant.BUCKET_VALUE_ALL
        const val SINGLE_CHECK_ITEM_CALL = RecordModeConstant.BUCKET_VALUE_CALL
        const val SINGLE_CHECK_ITEM_RECENTLY_DELETED_CALL = RecordModeConstant.BUCKET_VALUE_RECENTLY_DELETED
        private const val TAG = "SingleModelSheetDialog"
    }

    var dialogCallback: SingleDialogCallback? = null
    private var mActivity: Activity? = activity
    private var mDialog: AlertDialog? = null
    private var mCheckItem: Int = 0

    fun create(): AlertDialog? {
        init()
        return mDialog
    }

    fun init() {
        mActivity?.let {
            val titles = getTitles(it)
            val checkboxStates: BooleanArray = getCheckboxStates()
            val singleChoiceListAdapter = ChoiceListAdapter(
                it,
                com.support.dialog.R.layout.coui_select_dialog_singlechoice,
                titles,
                null, checkboxStates, null, false
            )
            val dialogBuilder = COUIAlertDialogBuilder(it, com.support.dialog.R.style.COUIAlertDialog_BottomAssignment)
            dialogBuilder?.apply {
                setTitle(it.getString(com.soundrecorder.common.R.string.recording_group))
                setBlurBackgroundDrawable(true)
                setAdapter(singleChoiceListAdapter) { _, position ->
                    DebugUtil.d(TAG, "ckIm == $mCheckItem")
                    val checkedType = when (position) {
                        0 -> SINGLE_CHECK_ITEM_ALL
                        1 -> {
                            if (isSupportCall) {
                                SINGLE_CHECK_ITEM_CALL
                            } else {
                                SINGLE_CHECK_ITEM_RECENTLY_DELETED_CALL
                            }
                        }
                        2 -> SINGLE_CHECK_ITEM_RECENTLY_DELETED_CALL
                        else -> SINGLE_CHECK_ITEM_ALL
                    }
                    if (mCheckItem == checkedType) {
                        dismiss()
                    } else {
                        mCheckItem = checkedType
                        dialogCallback?.itemChecked(mCheckItem)
                    }
                }.setNegativeButton(com.soundrecorder.common.R.string.cancel, null)
            }
            mDialog = dialogBuilder.create()
            mDialog?.setOnShowListener {
                dialogCallback?.onShow()
            }
            mDialog?.setOnDismissListener {
                dialogCallback?.onDismiss()
            }
        }
    }

    private fun getCheckboxStates(): BooleanArray {
        val checkAll = isCheckAll()
        val checkRecently = isCheckRecently()
        var checkboxStates: BooleanArray
        if (isSupportCall) {
            val checkCall = isCheckCall()
            checkboxStates = booleanArrayOf(checkAll, checkCall, checkRecently)
        } else {
            checkboxStates = booleanArrayOf(checkAll, checkRecently)
        }
        return checkboxStates
    }

    private fun getTitles(it: Activity): Array<String> {
        val titles = if (isSupportCall) {
            arrayOf(
                it.getString(com.soundrecorder.common.R.string.all_the_recordings),
                it.getString(com.soundrecorder.common.R.string.incall_recording_tab),
                it.getString(com.soundrecorder.common.R.string.recycle_recently_deleted)
            )
        } else {
            arrayOf(
                it.getString(com.soundrecorder.common.R.string.all_the_recordings),
                it.getString(com.soundrecorder.common.R.string.recycle_recently_deleted)
            )
        }
        return titles
    }

    private fun isCheckAll() = mCheckItem == SINGLE_CHECK_ITEM_ALL
    private fun isCheckCall() = mCheckItem == SINGLE_CHECK_ITEM_CALL

    private fun isCheckRecently() = mCheckItem == SINGLE_CHECK_ITEM_RECENTLY_DELETED_CALL

    fun show(checkItem: Int) {
        mCheckItem = checkItem
        create()?.show()
    }

    fun show(activity: Activity?, checkItem: Int) {
        if (mActivity == null && activity != null) {
            mActivity = activity
        }
        mCheckItem = checkItem
        DebugUtil.d(TAG, "show checkItem:$checkItem")
        create()?.show()
    }

    fun dismiss() {
        mDialog?.dismiss()
    }

    fun isShowing(): Boolean {
        return mDialog?.isShowing ?: false
    }

    fun release() {
        mDialog?.dismiss()
        mActivity = null
        mDialog = null
        dialogCallback = null
    }
}