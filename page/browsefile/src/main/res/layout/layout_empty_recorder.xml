<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/empty_recorder_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true"
    android:scrollbars="none">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/guide_empty_recorder"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            app:layout_constraintGuide_percent="0.45" />

        <LinearLayout
            android:id="@+id/mEmptyLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="vertical"
            app:layout_constraintBottom_toBottomOf="@id/guide_empty_recorder"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/guide_empty_recorder">

            <com.soundrecorder.common.widget.OSImageView
                android:id="@+id/mEmptyImage"
                android:layout_width="@dimen/os_image_def_width"
                android:layout_height="@dimen/os_image_def_height"
                app:anim_raw_json="@raw/ic_no_record_json_new"
                app:anim_raw_json_night="@raw/ic_no_record_json_night_new"
                app:img_draw="@drawable/ic_no_record"
                 />

            <TextView
                android:id="@+id/bottle"
                style="@style/Widget.COUI.COUINoContentStyle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/no_record_file"
                android:textColor="@color/coui_color_label_primary"
                android:textSize="@dimen/sp16"
                android:textFontWeight="500"
                android:visibility="visible" />

            <!-- coui_color_primary_neutral -->
            <TextView
                android:id="@+id/tv_recently_bottle"
                style="@style/Widget.COUI.COUINoContentStyle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/recycle_records_delete_keep_days"
                android:textColor="@color/coui_color_label_secondary"
                android:textSize="@dimen/sp12"
                android:visibility="gone" />
        </LinearLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>
</ScrollView>