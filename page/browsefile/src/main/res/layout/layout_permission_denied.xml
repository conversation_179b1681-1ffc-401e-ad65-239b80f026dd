<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/permission_denied_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true"
    android:visibility="gone"
    tools:visibility="visible">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/guide_permission_denied"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            app:layout_constraintGuide_percent="0.45" />

        <LinearLayout
            android:id="@+id/permission_content"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:clickable="true"
            android:focusable="true"
            android:gravity="center"
            android:orientation="vertical"
            app:layout_constraintBottom_toBottomOf="@id/guide_permission_denied"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/guide_permission_denied">


            <TextView
                android:id="@+id/tips_permission_denied"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:paddingStart="@dimen/dp24"
                android:paddingEnd="@dimen/dp24"
                android:text="@string/storage_permission_denied"
                android:textAppearance="@style/couiTextAppearanceHeadline5"
                android:textColor="@color/coui_color_label_primary" />

            <TextView
                android:id="@+id/tips_hint_permission_denied"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="sans-serif"
                android:gravity="center"
                android:layout_marginTop="@dimen/dp4"
                android:lineSpacingExtra="@dimen/dp2"
                android:paddingStart="@dimen/dp24"
                android:paddingEnd="@dimen/dp24"
                android:text="@string/storage_permission_describe_v2"
                android:textColor="@color/coui_color_label_primary"
                android:textSize="@dimen/sp14" />

            <TextView
                android:id="@+id/btn_permission_operate"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp12"
                android:ellipsize="end"
                android:gravity="center"
                android:maxLines="2"
                android:paddingStart="@dimen/dp12"
                android:paddingTop="@dimen/dp4"
                android:paddingEnd="@dimen/dp12"
                android:paddingBottom="@dimen/dp4"
                android:textColor="?attr/couiColorLabelTheme"
                android:textAppearance="@style/couiTextAppearanceButtonL"
                tools:text="@string/permission_open" />

        </LinearLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>
</ScrollView>
