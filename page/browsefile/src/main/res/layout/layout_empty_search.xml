<?xml version="1.0" encoding="utf-8"?>

<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <merge>

        <ScrollView
            android:id="@+id/emptyContainer"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:clickable="true"
            android:fillViewport="true"
            android:scrollbars="none"
            android:visibility="gone"
            tools:visibility="visible">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:clickable="true"
                android:visibility="visible">

                <androidx.constraintlayout.widget.Guideline
                    android:id="@+id/guide_empty_search"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    app:layout_constraintGuide_percent="0.45" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:orientation="vertical"
                    app:layout_constraintBottom_toBottomOf="@id/guide_empty_search"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="@id/guide_empty_search">

                    <com.soundrecorder.common.widget.OSImageView
                        android:id="@+id/img"
                        android:layout_width="@dimen/os_image_def_width"
                        android:layout_height="@dimen/os_image_def_height"
                        app:anim_raw_json="@raw/ic_search_no_result_new"
                        app:img_draw="@drawable/ic_no_search_result"
                        app:anim_raw_json_night="@raw/ic_search_no_result_night_new" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/not_search_result"
                        android:textColor="@color/coui_color_label_secondary"
                        android:textSize="@dimen/sp16"
                        tools:ignore="NotSibling" />
                </LinearLayout>
            </androidx.constraintlayout.widget.ConstraintLayout>
        </ScrollView>
    </merge>
</layout>