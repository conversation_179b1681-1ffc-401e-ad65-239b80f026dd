/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: MiniRecorderViewHelperTest
 * Description:
 * Version: 1.0
 * Date: 2023/4/18
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2023/4/18 1.0 create
 */

package com.soundrecorder.miniapp

import android.content.Context
import android.os.Build
import android.widget.TextView
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.miniapp.shadow.ShadowFeatureOption
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowFeatureOption::class])
class MiniRecorderViewHelperTest {
    private var mContext: Context? = null

    @Before
    fun setUp() {
        mContext = ApplicationProvider.getApplicationContext()
    }

    @Test
    fun should_correct_when_setTimerTextViewText() {
        val textView = TextView(mContext)
        textView.isFakeBoldText(true)
        textView.setTimerTextViewText()
        Assert.assertEquals("00:00.00", textView.text)
    }

    @Test
    fun should_correct_when_setStateTextViewText() {
        val textView = TextView(mContext)
        textView.setStateTextViewText(true)
        Assert.assertEquals(mContext!!.getString(com.soundrecorder.common.R.string.saving), textView.text)

        textView.setStateTextViewText(false)
        Assert.assertFalse(textView.text.isNotEmpty())

        textView.setStateTextViewText()
        Assert.assertTrue(textView.text.isEmpty())
    }
}