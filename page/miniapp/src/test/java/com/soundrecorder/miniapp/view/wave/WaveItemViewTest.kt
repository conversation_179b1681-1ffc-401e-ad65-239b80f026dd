/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: WaveItemViewTest
 Description:
 Version: 1.0
 Date: 2022/8/29
 Author: W9013333(v-zhen<PERSON><PERSON><PERSON><PERSON>@oppo.com)
 -----------Revision History-----------
 <author> <date> <version> <desc>
 W9013333 2022/8/29 1.0 create
 */

package com.soundrecorder.miniapp.view.wave

import android.graphics.Canvas
import android.os.Build
import android.os.SystemClock
import android.widget.FrameLayout
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.miniapp.shadow.ShadowFeatureOption
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mockito.*
import org.powermock.reflect.Whitebox
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowFeatureOption::class])
class WaveItemViewTest {

    @Test
    fun onDrawTest() {
        BaseApplication.sIsRTLanguage = true
        val systemClockMockStatic = mockStatic(SystemClock::class.java)
        systemClockMockStatic.`when`<Long> { SystemClock.elapsedRealtime() }.thenReturn(10000)
        val parentView = FrameLayout(ApplicationProvider.getApplicationContext())
        val waveItemView = spy(WaveItemView(ApplicationProvider.getApplicationContext()))
        parentView.addView(waveItemView)
        doReturn(200f).doCallRealMethod().`when`(waveItemView).halfParentWidth()
        drawItem(waveItemView)
        BaseApplication.sIsRTLanguage = false
        drawItem(waveItemView)
        systemClockMockStatic.close()
    }

    private fun drawItem(waveItemView: WaveItemView) {
        waveItemView.refreshData(true, 3, mutableListOf<Int>().apply {
            for (index in 0 until 3) {
                add(index)
            }
        }, -1L, -1L)
        waveItemView.setCurViewIndex(0)
        Whitebox.invokeMethod<Unit>(waveItemView, "onDraw", Canvas())
        waveItemView.setCurViewIndex(1)
        Whitebox.invokeMethod<Unit>(waveItemView, "onDraw", Canvas())
        waveItemView.setCurViewIndex(2)
        Whitebox.invokeMethod<Unit>(waveItemView, "onDraw", Canvas())
        waveItemView.setCurViewIndex(3)
        Whitebox.invokeMethod<Unit>(waveItemView, "onDraw", Canvas())
        waveItemView.setCurViewIndex(4)
        Whitebox.invokeMethod<Unit>(waveItemView, "onDraw", Canvas())
        waveItemView.refreshData(true, 3, mutableListOf<Int>().apply {
            for (index in 0 until 3) {
                add(index)
            }
        }, 9999, -1L)
        waveItemView.setCurViewIndex(0)
        Whitebox.invokeMethod<Unit>(waveItemView, "onDraw", Canvas())
        waveItemView.setCurViewIndex(1)
        Whitebox.invokeMethod<Unit>(waveItemView, "onDraw", Canvas())
        waveItemView.setCurViewIndex(2)
        Whitebox.invokeMethod<Unit>(waveItemView, "onDraw", Canvas())
        waveItemView.setCurViewIndex(3)
        Whitebox.invokeMethod<Unit>(waveItemView, "onDraw", Canvas())
        waveItemView.setCurViewIndex(4)
        Whitebox.invokeMethod<Unit>(waveItemView, "onDraw", Canvas())
        waveItemView.refreshData(true, 3, mutableListOf<Int>().apply {
            for (index in 0 until 3) {
                add(index)
            }
        }, -1L, 9999)
        waveItemView.setCurViewIndex(0)
        Whitebox.invokeMethod<Unit>(waveItemView, "onDraw", Canvas())
        waveItemView.setCurViewIndex(1)
        Whitebox.invokeMethod<Unit>(waveItemView, "onDraw", Canvas())
        waveItemView.setCurViewIndex(2)
        Whitebox.invokeMethod<Unit>(waveItemView, "onDraw", Canvas())
        waveItemView.setCurViewIndex(3)
        Whitebox.invokeMethod<Unit>(waveItemView, "onDraw", Canvas())
        waveItemView.setCurViewIndex(4)
        Whitebox.invokeMethod<Unit>(waveItemView, "onDraw", Canvas())
    }
}