/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: MiniRecorderApiTest
 * Description:
 * Version: 1.0
 * Date: 2023/8/28
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2023/8/28 1.0 create
 */

package com.soundrecorder.miniapp.api

import android.content.Context
import android.content.Intent
import android.os.Build
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.miniapp.MiniRecorderActivity
import com.soundrecorder.miniapp.shadow.ShadowFeatureOption
import com.soundrecorder.modulerouter.miniapp.MiniAppConstant
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.Robolectric
import org.robolectric.android.controller.ActivityController
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowFeatureOption::class])
class MiniRecorderApiTest {
    private var activityController: ActivityController<MiniRecorderActivity>? = null
    private var context: Context? = null

    @Before
    fun setUp() {
        activityController = Robolectric.buildActivity(MiniRecorderActivity::class.java)
        context = ApplicationProvider.getApplicationContext()
    }

    @After
    fun release() {
        activityController = null
        context = null
    }

    @Test
    fun should_notNull_when_createMiniAppIntent() {
        context?.let {
            Assert.assertNotNull(MiniRecorderApi.createMiniAppIntent(it))
        }
    }

    @Test
    fun should_returnTrue_when_isMiniRecorderActivity() {
        activityController?.get()?.let {
            Assert.assertTrue(MiniRecorderApi.isMiniRecorderActivity(it))
        }
    }

    @Test
    fun should_nextAction_when_checkMiniAppContinueAction() {
        val activity = activityController?.get() ?: return
        activity.intent = Intent().apply {
            putExtra(MiniAppConstant.EXTRA_NAME_NEXT_ACTION, MiniAppConstant.NEXT_ACTION_BOOT_PRIVACY)
        }
        MiniRecorderApi.checkMiniAppContinueAction(activity, activity.intent) {}

        activity.intent = Intent().apply {
            putExtra(MiniAppConstant.EXTRA_NAME_NEXT_ACTION, MiniAppConstant.NEXT_ACTION_RECORDER_PRIVACY)
        }
        MiniRecorderApi.checkMiniAppContinueAction(activity, activity.intent) {}

        activity.intent = Intent().apply {
            putExtra(MiniAppConstant.EXTRA_NAME_NEXT_ACTION, MiniAppConstant.NEXT_ACTION_APP_SETTING)
            putExtra(MiniAppConstant.EXTRA_NAME_CHECK_SHOW_NOTIFICATION_SNACK_BAR, true)
        }
        MiniRecorderApi.checkMiniAppContinueAction(activity, activity.intent) {}
    }
}