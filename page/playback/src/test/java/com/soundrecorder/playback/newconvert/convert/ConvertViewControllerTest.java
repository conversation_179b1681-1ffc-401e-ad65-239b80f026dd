/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/12/5
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.playback.newconvert.convert;

import static org.mockito.ArgumentMatchers.any;
import static org.robolectric.Shadows.shadowOf;

import android.content.Context;
import android.os.Build;
import android.view.View;
import android.widget.TextView;

import androidx.lifecycle.ViewModelProvider;
import androidx.test.ext.junit.runners.AndroidJUnit4;

import com.coui.appcompat.darkmode.COUIDarkModeUtil;
import com.soundrecorder.base.BaseApplication;
import com.soundrecorder.base.utils.ClickUtils;

import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.powermock.reflect.Whitebox;
import org.robolectric.Robolectric;
import org.robolectric.RuntimeEnvironment;
import org.robolectric.android.controller.ActivityController;
import org.robolectric.annotation.Config;
import org.robolectric.shadows.ShadowDialog;
import org.robolectric.shadows.ShadowLog;

import java.util.ArrayList;
import java.util.List;

import com.soundrecorder.playback.PlaybackActivity;
import com.soundrecorder.playback.PlaybackActivityViewModel;
import com.soundrecorder.common.databean.ConvertContentItem;

import com.soundrecorder.playback.newconvert.PlaybackConvertViewModel;
import com.soundrecorder.playback.newconvert.search.ConvertSearchHelper;
import com.soundrecorder.playback.newconvert.ui.ConvertRenameBottomSheetDialog;
import com.soundrecorder.playback.newconvert.ui.ConvertViewContainer;
import com.soundrecorder.playback.shadows.ShadowCOUIVersionUtil;
import com.soundrecorder.playback.shadows.ShadowFeatureOption;
import com.soundrecorder.playback.shadows.ShadowOS12FeatureUtil;
import com.soundrecorder.playback.shadows.ShadowOplusUsbEnvironment;

@RunWith(AndroidJUnit4.class)
@Config(sdk = Build.VERSION_CODES.S,
        shadows = {
                ShadowLog.class, ShadowOplusUsbEnvironment.class, ShadowOS12FeatureUtil.class,
                ShadowFeatureOption.class, ShadowCOUIVersionUtil.class
        })
public class ConvertViewControllerTest {
    private Context mContext;
    private ActivityController<PlaybackActivity> mController;
    private PlaybackActivity mActivity;
    private PlaybackActivityViewModel mPlaybackActivityViewModel;

    private static MockedStatic<COUIDarkModeUtil> mockedStatic;
    private static MockedStatic<ClickUtils> mockedClickUtils;

    @Before
    public void setUp() {
        ShadowLog.stream = System.out;
        mContext = BaseApplication.getAppContext();
        mController = Robolectric.buildActivity(PlaybackActivity.class);
        mActivity = mController.create().get();
        mPlaybackActivityViewModel = new ViewModelProvider(mActivity).get(PlaybackActivityViewModel.class);

        mockedStatic = Mockito.mockStatic(COUIDarkModeUtil.class);
        mockedStatic.when(() -> COUIDarkModeUtil.isNightMode(any())).thenReturn(true);

        mockedClickUtils = Mockito.mockStatic(ClickUtils.class);
        mockedClickUtils.when(() -> ClickUtils.isQuickClick()).thenReturn(false);
    }

    @After
    public void tearDown() {
        mContext = null;
        mController = null;
        mActivity = null;

        mockedStatic.close();
        mockedStatic = null;

        mockedClickUtils.close();
        mockedClickUtils = null;
    }

    @Test
    public void should_correct_when_onClickListener() {
        ConvertViewContainer viewGroup = new ConvertViewContainer(mActivity);
        ConvertViewController convertViewController = new ConvertViewController(mActivity, viewGroup, null);
        ConvertRenameBottomSheetDialog.OnClickListener clickListener = Whitebox.getInternalState(convertViewController, "mClickListener");
        clickListener.onPositiveClick(1, true, "123", true);
        Assert.assertNull(Whitebox.getInternalState(convertViewController, "mRenameSpeakerDialog"));
        clickListener.onNegtiveClick();
    }

    @Test
    public void should_returnNull_when_init() {
        ConvertViewContainer viewGroup = new ConvertViewContainer(mActivity);
        ConvertViewController convertViewController = new ConvertViewController(mActivity, viewGroup, null);
        convertViewController.release();
        Assert.assertNull(Whitebox.getInternalState(convertViewController, "mToolbar"));
    }

    @Test
    public void should_notNull_when_setViewModel() {
        ConvertViewContainer viewGroup = new ConvertViewContainer(mActivity);
        ConvertViewController convertViewController = new ConvertViewController(mActivity, viewGroup, null);
        convertViewController.setViewModel(Mockito.mock(PlaybackActivityViewModel.class));
        Assert.assertNotNull(convertViewController.getViewModel());
    }

    @Test
    public void should_notNull_when_setConvertViewModel() {
        ConvertViewContainer viewGroup = new ConvertViewContainer(mActivity);
        ConvertViewController convertViewController = new ConvertViewController(mActivity, viewGroup, null);
        convertViewController.setConvertViewModel(Mockito.mock(PlaybackConvertViewModel.class));
        Assert.assertNotNull(Whitebox.getInternalState(convertViewController, "mConvertViewModel"));
    }

    @Test
    public void should_returnNull_when_showContinueTranscribingDialog() throws Exception {
        ConvertViewContainer viewGroup = new ConvertViewContainer(mActivity);
        ConvertViewController convertViewController = new ConvertViewController(mActivity, viewGroup, null);
        Whitebox.setInternalState(convertViewController, "mActivity", mActivity, ConvertViewController.class);
        Whitebox.invokeMethod(convertViewController, "showContinueTranscribingDialog");
        ShadowDialog dialog = shadowOf(RuntimeEnvironment.application).getLatestDialog();
        Assert.assertNotNull(dialog);
    }

    @Test
    public void should_returnNull_when_showRenameSpeakerDialog() {
        ConvertViewContainer viewGroup = new ConvertViewContainer(mActivity);
        ConvertViewController convertViewController = new ConvertViewController(mActivity, viewGroup, null);
        Whitebox.setInternalState(convertViewController, "mActivity", mActivity, ConvertViewController.class);
        convertViewController.setViewModel(new ViewModelProvider(mActivity).get(PlaybackActivityViewModel.class));
        convertViewController.showRenameSpeakerDialog("test", true, "test");
        ShadowDialog dialog = shadowOf(RuntimeEnvironment.application).getLatestDialog();
        Assert.assertNotNull(dialog);
    }

    @Test
    public void should_returnNull_when_releaseRenameSpeakerDialog() {
        ConvertViewContainer viewGroup = new ConvertViewContainer(mActivity);
        ConvertViewController convertViewController = new ConvertViewController(mActivity, viewGroup, null);
        convertViewController.setViewModel(mPlaybackActivityViewModel);
        convertViewController.showRenameSpeakerDialog("test", true, "test");
        convertViewController.releaseRenameSpeakerDialog();
        Assert.assertNull(Whitebox.getInternalState(convertViewController, "mRenameSpeakerDialog"));
    }

    @Test
    public void should_notNull_when_queryKeyword() throws Exception {
        ConvertViewContainer viewGroup = new ConvertViewContainer(mActivity);
        ConvertViewController convertViewController = new ConvertViewController(mActivity, viewGroup, null);
        mPlaybackActivityViewModel.setBrowseSearchWord("123");
        convertViewController.setViewModel(mPlaybackActivityViewModel);
        List<ConvertContentItem> data = new ArrayList<>();
        data.add(new ConvertContentItem(0L, 3000L, "123", false, 0, "", null, null, null));
        Whitebox.invokeMethod(convertViewController, "queryKeyWord", data);
        ConvertSearchHelper searchHelper = Whitebox.getInternalState(convertViewController, "searchHelper");
        Assert.assertNotNull(searchHelper);
    }

    @Test
    public void should_null_when_dismissContinuetranDialog() throws Exception {
        ConvertViewContainer viewGroup = new ConvertViewContainer(mActivity);
        ConvertViewController convertViewController = new ConvertViewController(mActivity, viewGroup, null);
        Whitebox.invokeMethod(convertViewController, "showContinueTranscribingDialog");
        Whitebox.invokeMethod(convertViewController, "dismissContinuetranDialog");
        Assert.assertNull(Whitebox.getInternalState(convertViewController, "mContinuetranDialog"));
    }

    @Test
    public void should_null_when_switchConvertInitView() {
        ConvertViewContainer viewGroup = new ConvertViewContainer(mActivity);
        ConvertViewController convertViewController = new ConvertViewController(mActivity, viewGroup, null);
        convertViewController.switchConvertInitView(false);
        Assert.assertEquals(1, viewGroup.getChildCount());
        convertViewController.switchConvertInitView(true);
        Assert.assertNull(Whitebox.getInternalState(convertViewController, "mContinuetranDialog"));
    }

    @Test
    public void should_null_when_switchConvertEmptyView() {
        ConvertViewContainer viewGroup = new ConvertViewContainer(mActivity);
        ConvertViewController convertViewController = new ConvertViewController(mActivity, viewGroup, null);
        convertViewController.switchConvertEmptyView();
        Assert.assertNull(Whitebox.getInternalState(convertViewController, "mContinuetranDialog"));
    }

    @Test
    public void should_null_when_switchConvertingView() {
        ConvertViewContainer viewGroup = new ConvertViewContainer(mActivity);
        ConvertViewController convertViewController = new ConvertViewController(mActivity, viewGroup, null);
        convertViewController.switchConvertingView();
        Assert.assertEquals(1, viewGroup.getChildCount());
    }

    @Test
    public void should_null_when_switchConvertContentView() throws Exception {
        ConvertViewContainer viewGroup = new ConvertViewContainer(mActivity);
        ConvertViewController convertViewController = new ConvertViewController(mActivity, viewGroup, null);
        convertViewController.setViewModel(mPlaybackActivityViewModel);
        convertViewController.switchConvertContentView();
        Assert.assertNull(Whitebox.getInternalState(convertViewController, "mContinuetranDialog"));
    }

    @Test
    public void should_equals_when_setViewAlpha() {
        ConvertViewContainer viewGroup = new ConvertViewContainer(mActivity);
        ConvertViewController convertViewController = new ConvertViewController(mActivity, viewGroup, null);
        View testView = new View(mActivity);
        List<View> list = new ArrayList<>();
        list.add(testView);
        convertViewController.setViewAlpha(0.5f, list);
        Assert.assertEquals(0.5f, testView.getAlpha(), 0);
    }

    @Test
    public void should_returnNull_when_getTimeString() {
        ConvertViewContainer viewGroup = new ConvertViewContainer(mActivity);
        ConvertViewController convertViewController = new ConvertViewController(mActivity, viewGroup, null);
        Assert.assertNotNull(convertViewController.getTimeString());
    }

    @Test
    public void should_equals_when_updateProgress() throws Exception {
        ConvertViewContainer viewGroup = new ConvertViewContainer(mActivity);
        ConvertViewController convertViewController = new ConvertViewController(mActivity, viewGroup, null);
        Whitebox.invokeMethod(convertViewController, "initConvertingView");
        convertViewController.updateProgress(50);
        TextView convertProgress = Whitebox.getInternalState(convertViewController, "mConvertProgress");
        String expectStr = mActivity.getString(com.soundrecorder.common.R.string.transfer_text_progress, "50%");
        Assert.assertEquals(expectStr, convertProgress.getText().toString());
    }

    @Test
    public void should_equals_when_updateProgress1() throws Exception {
        ConvertViewContainer viewGroup = new ConvertViewContainer(mActivity);
        ConvertViewController convertViewController = new ConvertViewController(mActivity, viewGroup, null);
        Whitebox.invokeMethod(convertViewController, "initConvertingView");
        convertViewController.updateProgress(50, 3);
        TextView convertProgress = Whitebox.getInternalState(convertViewController, "mConvertProgress");
        Assert.assertEquals(mActivity.getString(com.soundrecorder.common.R.string.transfer_text_progress, ""), convertProgress.getText().toString());

        convertViewController.updateProgress(50, 1);
        Assert.assertEquals(mActivity.getString(com.soundrecorder.common.R.string.transfer_text_progress, "50% "), convertProgress.getText().toString());
    }

    @Test
    public void should_notNull_when_showBackButtonAnimate() throws Exception {
        ConvertViewContainer viewGroup = new ConvertViewContainer(mActivity);
        ConvertViewController convertViewController = new ConvertViewController(mActivity, viewGroup, null);
        Whitebox.invokeMethod(convertViewController, "initConvertContentView");
        Whitebox.invokeMethod(convertViewController, "showBackButtonAnimate");
        View convertBackButton = Whitebox.getInternalState(convertViewController, "mConvertBackButton");
        Assert.assertNotNull(convertBackButton);
    }

    @Test
    public void should_false_when_hideBackButtonAnimate() throws Exception {
        ConvertViewContainer viewGroup = new ConvertViewContainer(mActivity);
        ConvertViewController convertViewController = new ConvertViewController(mActivity, viewGroup, null);
        Whitebox.invokeMethod(convertViewController, "initConvertContentView");
        Whitebox.invokeMethod(convertViewController, "hideBackButtonAnimate");
        View convertBackButton = Whitebox.getInternalState(convertViewController, "mConvertBackButton");
        Assert.assertFalse(convertBackButton.isClickable());
    }

    @Test
    public void should_equals_when_removeAllViews() {
        ConvertViewContainer viewGroup = new ConvertViewContainer(mActivity);
        ConvertViewController convertViewController = new ConvertViewController(mActivity, viewGroup, null);
        convertViewController.removeAllViews();
        Assert.assertEquals(0, viewGroup.getChildCount());
    }

    @Test
    public void should_success_when_setIsNeedShowAnimView() {
        ConvertViewContainer viewGroup = new ConvertViewContainer(mActivity);
        ConvertViewController convertViewController = new ConvertViewController(mActivity, viewGroup, null);
        convertViewController.setIsNeedShowAnimView(true);
        Assert.assertEquals(true, convertViewController.isNeedShowAnimView());
    }

    @Test
    public void should_success_when_onClick() {
        ConvertViewContainer viewGroup = new ConvertViewContainer(mActivity);
        ConvertViewController convertViewController = new ConvertViewController(mActivity, viewGroup, null);
        convertViewController.setViewModel(mPlaybackActivityViewModel);
        convertViewController.onClick(2);
        Assert.assertEquals(2, (int) mPlaybackActivityViewModel.getDataPosition());
    }
}
