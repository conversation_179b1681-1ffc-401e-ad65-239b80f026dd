/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/12/5
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.playback.newconvert.convert;

import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;

import android.content.Context;
import android.os.Build;

import androidx.test.ext.junit.runners.AndroidJUnit4;

import com.soundrecorder.convertservice.convert.NewConvertTextService;
import com.soundrecorder.playback.R;

import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.MockedStatic;
import org.powermock.reflect.Whitebox;
import org.robolectric.Robolectric;
import org.robolectric.annotation.Config;
import org.robolectric.shadows.ShadowLog;
import org.robolectric.shadows.ShadowToast;

import com.soundrecorder.base.BaseApplication;
import com.soundrecorder.playback.PlaybackActivity;
import com.soundrecorder.base.utils.NetworkUtils;
import com.soundrecorder.common.utils.ConvertDbUtil;
import com.soundrecorder.playback.shadows.ShadowFeatureOption;
import com.soundrecorder.playback.shadows.ShadowOS12FeatureUtil;

@RunWith(AndroidJUnit4.class)
@Config(sdk = Build.VERSION_CODES.S, shadows = {ShadowLog.class, ShadowOS12FeatureUtil.class, ShadowFeatureOption.class})
public class ConvertServiceManagerTest {

    private Context mContext;
    private ConvertServiceManager mConvertServiceManager;

    @Before
    public void setUp() {
        ShadowLog.stream = System.out;
        mContext = BaseApplication.getAppContext();
        mConvertServiceManager = new ConvertServiceManager(null, null);
    }

    @After
    public void tearDown() {
        mConvertServiceManager = null;
        mContext = null;
    }

    @Test
    public void should_null_when_release() {
        PlaybackActivity activity = Robolectric.buildActivity(PlaybackActivity.class).get();
        mConvertServiceManager.release();
        Assert.assertNull(Whitebox.getInternalState(mConvertServiceManager, "mToastRunnable"));
        Assert.assertNull(Whitebox.getInternalState(mConvertServiceManager, "mConvertCallback"));
    }

    @Test
    public void should_correct_when_checkCanConvert() throws Exception {
        MockedStatic<NetworkUtils> mockedNetworkUtils = mockStatic(NetworkUtils.class);
        mockedNetworkUtils.when(() -> NetworkUtils.isNetworkInvalid(mContext)).thenReturn(true);
        Assert.assertFalse(Whitebox.invokeMethod(mConvertServiceManager, "checkCanConvert", mContext));
        Assert.assertEquals(mContext.getResources().getString(com.soundrecorder.common.R.string.network_disconnect), ShadowToast.getTextOfLatestToast());

        mockedNetworkUtils.when(() -> NetworkUtils.isNetworkInvalid(mContext)).thenReturn(false);
        MockedStatic<ConvertDbUtil> mockedConvertDbUtil = mockStatic(ConvertDbUtil.class);
        mockedConvertDbUtil.when(() -> ConvertDbUtil.checkAlreadyConvertComplete(anyLong())).thenReturn(true);
        Assert.assertFalse(Whitebox.invokeMethod(mConvertServiceManager, "checkCanConvert", mContext));
        Assert.assertEquals(mContext.getResources().getString(com.soundrecorder.common.R.string.transfer_finished), ShadowToast.getTextOfLatestToast());

        mockedConvertDbUtil.when(() -> ConvertDbUtil.checkAlreadyConvertComplete(anyLong())).thenReturn(false);
        NewConvertTextService service = mock(NewConvertTextService.class);
        Whitebox.setInternalState(mConvertServiceManager, "mConvertService", service);
        doReturn(2).when(service).checkCanAddNewTask(anyLong());
        Assert.assertFalse(Whitebox.invokeMethod(mConvertServiceManager, "checkCanConvert", mContext));
        Assert.assertEquals(mContext.getResources().getString(com.soundrecorder.common.R.string.convert_recordings_exceeds_upper_limit_v2), ShadowToast.getTextOfLatestToast());

        doReturn(3).when(service).checkCanAddNewTask(anyLong());
        Assert.assertTrue(Whitebox.invokeMethod(mConvertServiceManager, "checkCanConvert", mContext));
        mockedNetworkUtils.close();
        mockedConvertDbUtil.close();
    }
}
