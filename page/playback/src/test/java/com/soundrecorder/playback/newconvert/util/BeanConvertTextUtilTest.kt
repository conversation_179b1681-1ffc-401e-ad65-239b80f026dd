/*********************************************************************
 * * Copyright (C), 2023, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  BeanConvertTextUtilTest
 * * Description :
 * * Version     : 1.0
 * * Date        : 2023/5/26
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.playback.newconvert.util

import android.os.Build
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.common.databean.ConvertContentItem
import com.soundrecorder.convertservice.util.BeanConvertTextUtil
import com.soundrecorder.playback.shadows.ShadowFeatureOption
import com.soundrecorder.playback.shadows.ShadowOS12FeatureUtil
import com.soundrecorder.playback.shadows.ShadowRecorderLogger
import org.junit.Assert
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(
    sdk = [Build.VERSION_CODES.S],
    shadows = [ShadowRecorderLogger::class, ShadowOS12FeatureUtil::class, ShadowFeatureOption::class]
)
class BeanConvertTextUtilTest {

    @Test
    fun should_success_when_genTextWithWords() {
        var result = BeanConvertTextUtil.genTextWithWords(null)
        Assert.assertNull(result)

        val inString = "1234567"
        result = BeanConvertTextUtil.genTextWithWords(inString)
        Assert.assertEquals(4, result?.size)
    }

    @Test
    fun should_success_when_genTextWithWordsTimeStamp() {
        var result = BeanConvertTextUtil.genTextWithWordsTimeStamp(null)
        Assert.assertNull(result)

        var inString = " "
        result = BeanConvertTextUtil.genTextWithWordsTimeStamp(inString)
        Assert.assertNull(result)

        inString = "1 2 3"
        result = BeanConvertTextUtil.genTextWithWordsTimeStamp(inString)
        Assert.assertEquals(3, result?.size)
    }

    @Test
    fun should_success_when_combineList() {
        val inList = mutableListOf<String>().also {
            it.add("123")
            it.add("456")
        }
        val result = BeanConvertTextUtil.combineList(inList)
        Assert.assertEquals("123456", result)
    }

    @Test
    fun should_success_when_genTime() {
        val item = ConvertContentItem(startTime = 0, endTime = 1000)
        var result = BeanConvertTextUtil.genTime(item, -1)
        Assert.assertEquals(0F, result)

        item.textWithWordsTimeStamp = mutableListOf("1", "2", "3")
        result = BeanConvertTextUtil.genTime(item, 3)
        Assert.assertEquals(1000F, result)

        result = BeanConvertTextUtil.genTime(item, 1)
        Assert.assertEquals(2000F, result)
    }

    @Test
    fun should_success_when_checkIsSpiltFlag() {
        var result = BeanConvertTextUtil.checkIsSpiltFlag("?")
        Assert.assertTrue(result)

        result = BeanConvertTextUtil.checkIsSpiltFlag(">")
        Assert.assertFalse(result)
    }

    @Test
    fun should_success_when_genStartPlayTime() {
        val item = ConvertContentItem(startTime = 0, endTime = 1000)
        var result = BeanConvertTextUtil.genStartPlayTime(item, -1)
        Assert.assertEquals(-1L, result)

        item.listSubSentence = mutableListOf(
            ConvertContentItem.SubSentence(0, 2, 1000F, "12").also {
                 it.onlyHasSimpleMark = true
            },
            ConvertContentItem.SubSentence(4, 7, 6000F, "345")
        )
        result = BeanConvertTextUtil.genStartPlayTime(item, -1)
        Assert.assertEquals(-1L, result)

        result = BeanConvertTextUtil.genStartPlayTime(item, 2)
        Assert.assertEquals(1000L, result)

        item.listSubSentence!![0].onlyHasSimpleMark = false
        result = BeanConvertTextUtil.genStartPlayTime(item, 4)
        Assert.assertEquals(6000L, result)
    }
}