/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/12/5
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.playback.newconvert.keyword

import android.app.Application
import android.content.Context
import android.os.Build
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.NetworkUtils
import com.soundrecorder.convertservice.keyword.ExtractKeyWordTask
import com.soundrecorder.playback.newconvert.PlaybackConvertViewModel
import com.soundrecorder.playback.shadows.ShadowFeatureOption
import com.soundrecorder.playback.shadows.ShadowOS12FeatureUtil
import com.soundrecorder.playback.shadows.ShadowOplusUsbEnvironment
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.MockedStatic
import org.mockito.Mockito
import org.powermock.reflect.Whitebox
import org.robolectric.annotation.Config
import org.robolectric.shadows.ShadowLog

@RunWith(AndroidJUnit4::class)
@Config(
    sdk = [Build.VERSION_CODES.S],
    shadows = [ShadowLog::class, ShadowOplusUsbEnvironment::class, ShadowOS12FeatureUtil::class, ShadowFeatureOption::class]
)
class ExtractKeyWordManagerTest {

    private var context: Context? = null
    private var model: PlaybackConvertViewModel? = null
    private var manager: ExtractKeyWordManager? = null
    private var networkUtils: MockedStatic<NetworkUtils>? = null

    @Before
    fun setUp() {
        context = BaseApplication.getAppContext()
        model = PlaybackConvertViewModel(context as Application)
        model?.let {
            manager = ExtractKeyWordManager(it)
        }
        networkUtils = Mockito.mockStatic(NetworkUtils::class.java)
    }


    @After
    fun tearDown() {
        context = null
        model = null
        manager = null
        networkUtils?.close()
        networkUtils = null
    }

    private fun mockNetworkUtils(mockValue: Boolean) {
        networkUtils?.`when`<Boolean> { NetworkUtils.isNetworkInvalid(context) }?.thenReturn(mockValue)
    }

    @Test
    fun should_return_int_when_checkCanExtract() {
        mockNetworkUtils(true)
        var code = Whitebox.invokeMethod<Int>(manager, "checkCanExtract", context)
        Assert.assertEquals(ExtractKeyWordTask.CODE_NO_NET, code)

        mockNetworkUtils(false)
        code = Whitebox.invokeMethod<Int>(manager, "checkCanExtract", context)
        Assert.assertEquals(ExtractKeyWordTask.CODE_OK, code)
    }


    @Test
    fun should_return_bool_when_extractKeyWord() {
        mockNetworkUtils(true)
        var result = manager?.extractKeyWord(1L)
        Assert.assertEquals(false, result)

        mockNetworkUtils(false)
        result = manager?.extractKeyWord(1L)
        Assert.assertEquals(true, result)
    }

    @Test
    fun should_return_void_when_onExtracting() {
        Whitebox.invokeMethod<Int>(manager, "onExtracting")
        Assert.assertEquals(KeyWordChipGroup.LOADING_STATE, model?.extractState)
    }

    @Test
    fun should_return_void_when_onExtractFailed() {
        Whitebox.invokeMethod<Int>(
            manager,
            "onExtractFailed",
            ExtractKeyWordTask.CODE_NO_NET,
            "测试"
        )
        Assert.assertEquals(KeyWordChipGroup.DEFAULT_STATE, model?.extractState)
    }

    @Test
    fun should_return_void_when_setKeyWords() {
        val list: List<String> = emptyList()
        Whitebox.invokeMethod<Int>(manager, "setKeyWords", list)
        Assert.assertEquals(KeyWordChipGroup.DEFAULT_STATE, model?.extractState)
    }

    @Test
    fun should_return_void_when_release() {
        manager?.release()
    }
}