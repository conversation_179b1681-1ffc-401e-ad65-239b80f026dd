/*********************************************************************
 * * Copyright (C), 2024, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  ConvertUtilsTest
 * * Description :
 * * Version     : 1.0
 * * Date        : 2024/3/29
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.playback.newconvert.convert

import android.content.Context
import android.os.Build
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.convertservice.convert.ConvertTaskThreadManager
import com.soundrecorder.modulerouter.recorder.RecorderServiceInterface
import com.soundrecorder.playback.convert.ConvertContentItem
import com.soundrecorder.playback.newconvert.ConvertUtils
import com.soundrecorder.playback.shadows.ShadowFeatureOption
import com.soundrecorder.playback.shadows.ShadowOS12FeatureUtil
import io.mockk.mockk
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.koin.core.context.startKoin
import org.koin.core.context.stopKoin
import org.koin.dsl.koinApplication
import org.koin.dsl.module
import org.robolectric.annotation.Config
import org.robolectric.shadows.ShadowLog

@RunWith(AndroidJUnit4::class)
@Config(
    sdk = [Build.VERSION_CODES.S],
    shadows = [ShadowLog::class, ShadowOS12FeatureUtil::class, ShadowFeatureOption::class]
)
class ConvertUtilsTest {

    private var context: Context? = null

    private val recorderViewModelApi = mockk<RecorderServiceInterface>()

    private val koinApp = koinApplication {
        modules(module {
            single { recorderViewModelApi }
        })
    }

    @Before
    fun init() {
        context = BaseApplication.getAppContext()
        startKoin(koinApp)
    }

    @After
    fun tearDown() {
        context = null
        stopKoin()
    }

    @Test
    fun should_invoke_when_cancelAllConvertTask() {
        ConvertUtils.cancelAllConvertTask()
        Assert.assertTrue(ConvertTaskThreadManager.checkNoTaskRunning())
    }

    @Test
    fun should_success_when_stopConvertService() {
        ConvertUtils.stopConvertService(context!!)
        Assert.assertFalse(recorderViewModelApi.hasInitRecorderService())
    }

    @Test
    fun should_success_when_convertConvertContentItemToSentence() {
        var result = ConvertUtils.convertConvertContentItemToSentence(null)
        Assert.assertNull(result)

        val itemList = mutableListOf<ConvertContentItem>()
        itemList.add(ConvertContentItem(0L, 3000L, "123", false, 0, "", null, null, null, null))
        result = ConvertUtils.convertConvertContentItemToSentence(itemList)
        Assert.assertEquals("123", result!![0].content)
    }
}