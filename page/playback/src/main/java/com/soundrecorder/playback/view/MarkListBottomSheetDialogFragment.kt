/*********************************************************************
 * * Copyright (C), 2025, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  MarkListBottomSheetDialogFragment
 * * Description :
 * * Version     : 1.0
 * * Date        : 2025/06/18
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.playback.view

import com.coui.appcompat.panel.COUIBottomSheetDialogFragment
import com.coui.appcompat.panel.COUIPanelFragment

class MarkListBottomSheetDialogFragment : COUIBottomSheetDialogFragment() {
    private var markListContainerFragment: MarkListContainerFragment? = null

    override fun setMainPanelFragment(panelFragment: COUIPanelFragment?) {
        super.setMainPanelFragment(panelFragment)
        if (panelFragment is MarkListContainerFragment) {
            this.markListContainerFragment = panelFragment
        }
    }

    fun setMarkListContainerFragment(markListContainerFragment: MarkListContainerFragment?) {
        this.markListContainerFragment = markListContainerFragment
    }

    fun setDialogContentViewState() {
        markListContainerFragment?.setDialogContentViewState()
    }
}