/*********************************************************************
 * * Copyright (C), 2023, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  SaveSummaryTextUtil.kt
 * * Description :
 * * Version     : 1.0
 * * Date        : 2025/5/29
 * * Author      : W9067780
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.playback.newconvert.exportconvert.summary

import android.content.Context
import android.media.MediaScannerConnection
import com.soundrecorder.base.BaseApplication
import kotlinx.coroutines.*
import com.soundrecorder.common.constant.Constants
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.playback.newconvert.exportconvert.txt.SaveToLocalCallback
import java.io.File
import java.io.FileNotFoundException
import java.io.FileOutputStream
import java.io.IOException
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

object SaveSummaryTextUtil {
    private const val TAG = "SaveSummaryTextUtil"
    private const val DELAY_MILLIS = 1000L
    private var mCallback: SaveToLocalCallback? = null
    private var mSaveTxtToLocalAbsPath: String = ""
    private var mSaveTxtToLocalFileName: String = ""

    //显示dialog的时间，最少存在1s再消失
    private var needShowDialog = false
    private var mShowDialogDuration = 0L
    @JvmStatic
    fun saveTxtToLocal(
        viewModel: ShareWithSummaryViewModel,
        folderPath: String,
        originalFileName: String,
        convertFileSize: Long,
        callback: SaveToLocalCallback?,
        viewModelScope: CoroutineScope,
    ) {
        this.mCallback = callback
        if (convertFileSize >= Constants.SHOW_WAITING_DIALOG_THRESHOLD) {
            needShowDialog = true
            //开始计时
            mShowDialogDuration = System.currentTimeMillis()
            //显示waitingDialog
            mCallback?.onShowSaveFileWaitingDialog()
        }
        viewModelScope.launch(Dispatchers.IO) {
            kotlin.runCatching {
                if (checkFolderExist(folderPath)) {
                    if (writeFileToFolder(
                            viewModel,
                            folderPath,
                            getRealFileName(folderPath, originalFileName)
                        )
                    ) {
                        updateMediaDatabase(BaseApplication.getAppContext(), folderPath)
                        setSaveResult(true)
                    } else {
                        setSaveResult(false, "setSaveTxtResult failed")
                    }
                } else {
                    DebugUtil.e(TAG, "checkFolderExist false >>")
                    setSaveResult(false, "checkFolderExist false")
                }
            }.onFailure {
                DebugUtil.e(TAG, "saveTxtToLocal", it)
            }
        }
    }

    /**
     * 统一返回保存结果
     */
    @JvmStatic
    private suspend fun setSaveResult(success: Boolean, message: String = "") {
        if (needShowDialog) {
            val delay = System.currentTimeMillis() - mShowDialogDuration
            if (delay < DELAY_MILLIS) {
                delay(DELAY_MILLIS)
            }
            needShowDialog = false
        }
        withContext(Dispatchers.Main) {
            if (success) {
                if (mSaveTxtToLocalFileName.isBlank() || mSaveTxtToLocalAbsPath.isBlank()) {
                    mCallback?.onSaveFailed(message)
                } else {
                    mCallback?.onSaveSuccess(mSaveTxtToLocalFileName, mSaveTxtToLocalAbsPath)
                }
            } else {
                mCallback?.onSaveFailed(message)
            }
        }
    }

    /**
     * 检查Documents/SoundRecordDoc/文件夹是否存在
     */
    @JvmStatic
    private fun checkFolderExist(path: String): Boolean {
        return try {
            val recorderFolder = File(path)
            if (!recorderFolder.exists()) {
                recorderFolder.mkdirs()
            }
            recorderFolder.exists()
        } catch (exception: FileNotFoundException) {
            DebugUtil.e(TAG, "fileOutputStream exception > ${exception.message}")
            false
        }
    }

    /**
     * 获取保本到本地文件真正的名称
     * 有可能saveFileName已经存在，则在后面加上"_x"，x为数字，从1开始递增
     * 例如："标准录音-2021-10-26 20-57-33"-转文本结果_1
     */
    @JvmStatic
    private fun getRealFileName(folderPath: String, saveFileName: String): String {
        val realName: String
        val fileExtension = ".txt"
        var number = 1
        var file = File(folderPath + saveFileName + fileExtension)
        if (!file.exists()) {
            return saveFileName
        }
        while (true) {
            file = File(folderPath + saveFileName + "_" + number + fileExtension)
            if (!file.exists()) {
                realName = file.nameWithoutExtension
                break
            }
            number++
        }
        DebugUtil.i(TAG, "getRealFileName > $realName")
        return realName
    }

    /**
     * 将转文本内容写入到文件
     * @param fileName 文件名称，不包含扩展名
     */
    @JvmStatic
    private fun writeFileToFolder(
        viewModel: ShareWithSummaryViewModel,
        folderPath: String,
        fileName: String
    ): Boolean {
        if (fileName.isBlank() || folderPath.isBlank()) {
            DebugUtil.e(TAG, "writeFileToFolder fullFileName is empty or blank")
            return false
        }
        var fileOutputStream: FileOutputStream? = null
        try {
            val absPath = "$folderPath$fileName.txt"
            mSaveTxtToLocalAbsPath = absPath
            mSaveTxtToLocalFileName = fileName
            mCallback?.onGetFileName(fileName, absPath)
            val txtString = viewModel.summaryContent.toString()
            fileOutputStream = FileOutputStream(absPath)
            fileOutputStream.write(txtString.toByteArray())
            fileOutputStream.close()
            return true
        } catch (exception: FileNotFoundException) {
            DebugUtil.e(TAG, "fileOutputStream exception > ${exception.message}")
        } catch (exception: IOException) {
            DebugUtil.e(TAG, "fileOutputStream exception > ${exception.message}")
        } finally {
            try {
                fileOutputStream?.close()
            } catch (exception: IOException) {
                DebugUtil.e(TAG, "close fileOutputStream exception > ${exception.message}")
            }
        }
        return false
    }

    /**
     * 保存文件完成之后更新媒体库
     */
    @JvmStatic
    private fun updateMediaDatabase(context: Context, saveTxtFolder: String) {
        //R上更新媒体库方法,
        kotlin.runCatching {
            if (saveTxtFolder.isBlank()) {
                return
            }
            val paths = arrayOf(saveTxtFolder)
            MediaScannerConnection.scanFile(context, paths, null, null)
        }.onFailure {
            DebugUtil.e(TAG, "internalMediaScanner :$saveTxtFolder, error: ${it.message}")
        }
    }
    @JvmStatic
    internal fun generateExportTitle(): String {
        val timestamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
        return "录音摘要_$timestamp"
    }
    @JvmStatic
    fun clearCallback() {
        mCallback = null
    }
}