/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  MarkListContainerFragment.kt
 * * Description :
 * * Version     : 1.0
 * * Date        : 2025/06/02
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.playback.view

import android.annotation.SuppressLint
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.graphics.toColorInt
import androidx.core.view.isVisible
import androidx.recyclerview.widget.COUIRecyclerView
import androidx.recyclerview.widget.LinearLayoutManager
import com.coui.appcompat.panel.COUIBottomSheetDialog
import com.coui.appcompat.panel.COUIBottomSheetDialogFragment
import com.coui.appcompat.panel.COUIPanelFragment
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.NightModeUtil
import com.soundrecorder.common.utils.ViewUtils.addItemDecorationBottom
import com.soundrecorder.common.widget.OSImageView
import com.soundrecorder.playback.R
import com.soundrecorder.wavemark.mark.MarkListAdapter

class MarkListContainerFragment(val fragmentMarkListAdapter: MarkListAdapter? = null)  : COUIPanelFragment() {
    companion object {
        private const val TAG = "MarkListContainerFragment"
        private const val DARK_BG_COLOR = "#FF333333"
        private const val BG_COLOR = "#FFF0F1F2"
    }

    private var markListRecyclerView: COUIRecyclerView? = null
    private var markListEmptyImageView: OSImageView? = null
    private var markListEmptyContainer: ConstraintLayout? = null

    @SuppressLint("InflateParams")
    override fun initView(panelView: View?) {
        toolbar?.visibility = View.GONE
        LayoutInflater.from(activity).inflate(R.layout.fragment_playback_mark_list, null, false).apply {
            findViewById<com.coui.appcompat.toolbar.COUIToolbar>(R.id.mark_list_toolbar)?.apply {
                title = context?.resources?.getString(com.soundrecorder.base.R.string.mark_list)
                isTitleCenterStyle = true
                inflateMenu(R.menu.mark_list_menu)
                menu?.findItem(R.id.cancel).apply {
                    setOnMenuItemClickListener {
                        dismissPanel()
                        setOnMenuItemClickListener(null)
                        true
                    }
                }

                toolbar?.menuView?.setOverflowMenuListener {
                    it.setOnItemClickListener { _, _, _, _ -> it.dismiss() }
                }
            }

            markListRecyclerView = this.findViewById(R.id.mark_list_recycler_view)
            if (markListRecyclerView == null) DebugUtil.i(TAG, "<<markListRecyclerView is null.")

            markListEmptyImageView = this.findViewById(R.id.iv_mark_list_empty_image)
            if (markListEmptyImageView == null) {
                DebugUtil.i(
                    TAG,
                    "<<markListEmptyImageView is null."
                )
            }

            markListEmptyContainer = this.findViewById(R.id.iv_mark_list_empty_view)
            if (markListEmptyContainer == null) {
                DebugUtil.i(
                    TAG,
                    "<<markListEmptyContainer is null."
                )
            }

            (contentView as? ViewGroup)?.addView(this)
        }

        markListRecyclerView?.let {
            it.layoutManager = LinearLayoutManager(it.context)
            it.adapter = fragmentMarkListAdapter
            it.addItemDecorationBottom(com.soundrecorder.common.R.dimen.card_margin_top_buttom)
        }

        markListEmptyImageView?.initImageResource()

        setDialogContentViewState()
        /*
         * Since 15.0, the dragView of panel without handle is hidden by default,
         * and there is no need to actively call this method.
         */
        //hideDragView()
    }

    fun setDialogContentViewState() {
        var isShowRecyclerView = true
        if (fragmentMarkListAdapter == null || fragmentMarkListAdapter.itemCount == 0) {
            isShowRecyclerView = false
        }
        DebugUtil.i(
            TAG,
            "setDialogContentViewState isShowRecyclerView:$isShowRecyclerView"
        )

        DebugUtil.i(
            TAG,
            "setDialogContentViewState item count:${fragmentMarkListAdapter?.itemCount}"
        )
        markListEmptyContainer?.isVisible = (!isShowRecyclerView)
        markListEmptyImageView?.isVisible = (!isShowRecyclerView)
        markListRecyclerView?.isVisible = isShowRecyclerView
    }

    private fun dismissPanel() {
        (parentFragment as? COUIBottomSheetDialogFragment)?.dismiss()
    }

    private fun initBackgroundColor() {
        var colorValue = context?.getColor(R.color.mark_list_dialog_color)

        DebugUtil.i(
            TAG,
            "Resource color value:$colorValue constant color value:${BG_COLOR.toColorInt()}"
        )

        if (colorValue == null) {
            colorValue = NightModeUtil.isNightMode(context)?.let {
                if (it) DARK_BG_COLOR.toColorInt() else BG_COLOR.toColorInt()
            } ?: BG_COLOR.toColorInt()
        }

        ((parentFragment as? COUIBottomSheetDialogFragment)?.dialog as? COUIBottomSheetDialog)
            ?.setPanelBackgroundTintColor(colorValue)
    }

    override fun onShow(isShowOnFirstPanel: Boolean?) {
        super.onShow(isShowOnFirstPanel)
        initBackgroundColor()
    }
}
