/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/12/5
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.playback.newconvert.view

import android.graphics.Rect
import android.widget.TextView
import com.soundrecorder.base.utils.DebugUtil

object TextViewMeasureUtil {
    const val TAG = "TextViewMeasureUtil"

    /**
     * 测量TextView中当前界面中
     */
    @JvmStatic
    fun getBackgroundTextViewOffSet(
        measureTextView: TextView?,
        textContent: String,
        startSeq: Int
    ): Int {
        if (measureTextView == null) {
            DebugUtil.i(TAG, "getBackgroundTextViewOffSet input textView null, return 0")
            return 0
        }
        measureTextView.text = textContent
        val line = measureTextView.layout?.getLineForOffset(startSeq) //获取字符在第几行
        val bound = Rect()
        line?.let { measureTextView.layout?.getLineBounds(it, bound) }
        val charY = bound.top
        //DebugUtil.i(TAG, "====>charY = $charY;  startSeq = $startSeq")
        measureTextView.text = ""
        return charY
    }
}