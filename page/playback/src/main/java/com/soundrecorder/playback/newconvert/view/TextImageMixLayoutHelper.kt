/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/12/5
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.playback.newconvert.view

import android.content.Context
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.util.TypedValue
import android.view.View
import android.widget.ImageView
import androidx.constraintlayout.widget.ConstraintLayout
import com.soundrecorder.playback.R
import com.google.android.material.imageview.ShapeableImageView
import com.google.android.material.shape.ShapeAppearanceModel
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.BaseUtil
import com.soundrecorder.common.databean.markdata.MarkDataBean
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.FileUtils
import com.soundrecorder.common.utils.FunctionOption
import com.soundrecorder.common.utils.ViewUtils
import com.soundrecorder.imageload.ImageLoadData
import com.soundrecorder.imageload.ImageLoaderUtils.into
import com.soundrecorder.common.databean.ConvertContentItem
import com.soundrecorder.playback.newconvert.ui.SeekPlayActionModeCallback

class TextImageMixLayoutHelper {

    companion object {
        const val TAG = "TextImageMixLayoutHelper"
        const val SAMPLE_SCALE = 2
        const val TEXT_SIZE = 16f

        fun getDefaultDrawAttr(inputContext: Context?): TextImageMixLayoutDrawAttr {
            val context = inputContext ?: BaseApplication.getAppContext()
            val defaultTopMargin = context.resources.getDimensionPixelSize(R.dimen.mix_image_text_layout_first_top_margin)
            val lastItemBottomMargin = context.resources.getDimensionPixelSize(R.dimen.mix_image_text_layout_last_buttom_margin)
            val defaultBottomMargin = context.resources.getDimensionPixelSize(R.dimen.mix_image_text_layout_bottom_margin)
            val defaultMarginBetweenImageAndText = context.resources.getDimensionPixelSize(R.dimen.mix_image_margin_between_image_and_text)
            val defaultMarginBetweenTextAndImage = context.resources.getDimensionPixelSize(R.dimen.mix_image_margin_between_text_and_image)
            val defaultMarginBetweenImages = context.resources.getDimensionPixelSize(R.dimen.mix_image_margin_between_image_and_image)
            val defaultCornerRadiusInDp = context.resources.getDimension(R.dimen.round_cornor_radius)
            val typeValue = TypedValue()
            context.theme.resolveAttribute(com.support.appcompat.R.attr.couiColorPrimary, typeValue, true)
            val defaultTextBackgroundColor = typeValue.data
            val drawAttr = TextImageMixLayoutDrawAttr(
                defaultTopMargin,
                lastItemBottomMargin,
                defaultMarginBetweenImageAndText,
                defaultMarginBetweenTextAndImage,
                defaultMarginBetweenImages,
                defaultCornerRadiusInDp,
                defaultTextBackgroundColor,
                defaultBottomMargin
            )
            DebugUtil.i(TextImageMixLayout.TAG, "parce attr drawAttr $drawAttr")
            return drawAttr
        }
    }

    //是否是搜索模式,进入搜索模式时，需要取消当前播放高亮状态
    var searchMode = false


    /**
     * 绘制添加子view的时候，TextImageMixLayout中的自定义属性
     */
    data class TextImageMixLayoutDrawAttr(
        var firstItemTopMargin: Int,
        var lastItemButtomMargin: Int,
        var marginBetweenImageAndText: Int,
        var marginBetweenTextAndImage: Int,
        var marginBetweenImages: Int,
        var roundedCornerRadisInDp: Float,
        var textBackgroundColor: Int,
        var bottomMargin: Int
    )


    fun checkAndAddBackgroundTextView(
        context: Context,
        itemIndex: Int,
        currentItem: ConvertContentItem.TextItemMetaData?,
        convertContentItem: ConvertContentItem?,
        anchorTopView: View?,
        parentView: View,
        isLastOne: Boolean,
        callback: SeekPlayActionModeCallback?,
        onClickListener: TextImageMixLayout.OnMixLayoutClickListenner?,
        drawAttr: TextImageMixLayoutDrawAttr
    ): View? {
        DebugUtil.i(TAG, "checkAndAddBackgroundTextView isLastOne $isLastOne")
        if (currentItem == null || convertContentItem == null) {
            DebugUtil.i(
                TAG,
                "checkAndAddBackgroundTextView currentItem or convertContentItem null, return"
            )
            return null
        }
        var backgroundTextView = BackgroundTextView(context)
        backgroundTextView.id = View.generateViewId()

        backgroundTextView.setPadding(
            0,
            0,
            0,
            context.resources.getDimensionPixelSize(com.soundrecorder.common.R.dimen.dp14)
        )
        backgroundTextView.setLineSpacing(
            context.resources.getDimension(R.dimen.line_spacing_extra),
            1.0f
        )
        backgroundTextView.setTextIsSelectable(true)
        backgroundTextView.setTextSize(TypedValue.COMPLEX_UNIT_SP, TEXT_SIZE)
        backgroundTextView.includeFontPadding = true
        backgroundTextView.textDirection = ConstraintLayout.TEXT_DIRECTION_LTR
        backgroundTextView.setBackGroundPaintColor(drawAttr.textBackgroundColor)

        val isFirstOne = (anchorTopView == null)
        val layoutParams: ConstraintLayout.LayoutParams? =
            ImageTextItemLayoutParamUtil.getConstraintLayoutParamForTextItemAndSetIds(
                context,
                isFirstOne,
                isLastOne,
                drawAttr,
                anchorTopView,
                parentView
            )
        backgroundTextView.layoutParams = layoutParams
        //设置标记下划线
        sentenceOfContentUnderLine(backgroundTextView, currentItem, convertContentItem, itemIndex)
        //设置text，同时设置标记小旗子
        setItemContent(backgroundTextView, currentItem, itemIndex)
        //设置正在播放的的高亮部分
        switchContentBackground(backgroundTextView, currentItem, convertContentItem, itemIndex)

        //设置TextView相关的Callback
        callback?.mItemTextView = backgroundTextView
        backgroundTextView.customSelectionActionModeCallback = callback
        DebugUtil.i(
            TAG,
            "checkAndAddBackgroundTextView addTextViewHeight ${backgroundTextView.measuredHeight}"
        )
        //设置TextView的点击事件处理
        if (FunctionOption.IS_SUPPORT_SEEKPLAY_FEATURE) {
            backgroundTextView.setOnClickListener {
                DebugUtil.i(TAG, "onTextViewClick")
                onClickListener?.onTextViewClick(it, convertContentItem, itemIndex)
            }
        }
        return backgroundTextView
    }


    fun checkAndAddImageView(
        context: Context,
        currentItem: ConvertContentItem.ImageMetaData?,
        convertContentItem: ConvertContentItem?,
        anchorTopView: View?,
        parentView: View,
        isLastOne: Boolean,
        onClickListener: TextImageMixLayout.OnMixLayoutClickListenner?,
        drawAttr: TextImageMixLayoutDrawAttr
    ): Pair<View, ImageLoadData>? {
        DebugUtil.i(TAG, "checkAndAddImageView isLastOne $isLastOne")
        if (currentItem == null || convertContentItem == null) {
            DebugUtil.i(TAG, "checkAndAddImageView currentItem or convertContentItem null, return")
            return null
        }
        var currentPictureMarkDataBean: MarkDataBean = currentItem.imageItem ?: return null


        var imageView = ShapeableImageView(context)
        imageView.id = View.generateViewId()

        val isFirstOne = (anchorTopView == null)
        val lastItemType = when (anchorTopView) {
            is ShapeableImageView -> ConvertContentItem.ItemMetaData.TYPE_IMAGE
            is BackgroundTextView -> ConvertContentItem.ItemMetaData.TYPE_TEXT
            else -> ConvertContentItem.ItemMetaData.TYPE_TEXT
        }
        val layoutParams: ConstraintLayout.LayoutParams? =
            ImageTextItemLayoutParamUtil.getConstraintLayoutParamForImageItemAndSetIds(
                context,
                currentItem,
                isFirstOne,
                isLastOne,
                anchorTopView,
                parentView,
                lastItemType,
                drawAttr
            )
        imageView.layoutParams = layoutParams

        var maxtWithAndRatio = ImageWithHeightCaculateUtil.getImageViewMaxtWithAndRatio(context)
        var imageViewConfig = ImageWithHeightCaculateUtil.caculateImageViewWithAndHeight(
            currentPictureMarkDataBean,
            maxtWithAndRatio
        )
        if (imageViewConfig.needFitCenter) {
            imageView.scaleType = ImageView.ScaleType.CENTER_CROP
        }
        //Coil加载时Size选项
        var imageLoadData = ImageLoadData(
            FileUtils.getAppFile(currentPictureMarkDataBean.pictureFilePath, false),
            imageViewConfig.imageViewWidth / SAMPLE_SCALE,
            imageViewConfig.imageViewHeight / SAMPLE_SCALE
        )
        imageView.shapeAppearanceModel = ShapeAppearanceModel.Builder()
            .setAllCornerSizes(ViewUtils.dp2px(drawAttr.roundedCornerRadisInDp, true))
            .build()
        imageView.strokeColor =
            context.resources.getColorStateList(R.color.mixlayout_image_bord_color, null)
        imageView.strokeWidth =
            ViewUtils.dp2px(context.resources.getDimension(R.dimen.mix_image_border_stoke))
        if (BaseUtil.isAndroidQOrLater) {
            imageView.isForceDarkAllowed = false
        }
        var strokeWith = imageView.strokeWidth
        var halfStrokeWith = (strokeWith / 2).toInt()
        imageView.setPadding(halfStrokeWith, halfStrokeWith, halfStrokeWith, halfStrokeWith)
        imageView.contentDescription = context.resources.getString(com.soundrecorder.common.R.string.talkback_preview_mark_picture)
        //加载图片显示
        //imageView.intoRoundImage(imageLoadData, roundedCornerRadisInDp)
        loadImageView(imageView, imageLoadData)
        //设置点击事件
        imageView.setOnClickListener {
            onClickListener?.onImageViewClick(
                imageView,
                convertContentItem,
                currentPictureMarkDataBean
            )
        }
        return Pair(imageView, imageLoadData)
    }


    fun loadImageView(imageView: ImageView, imageLoadData: ImageLoadData) {
        DebugUtil.i(TAG, "loadImageView imageLoadData $imageLoadData")
        imageView.into(imageLoadData)
    }

    /**
     * change focus state background
     */
    fun switchContentBackground(
        backgroundTextView: BackgroundTextView,
        currentItem: ConvertContentItem.TextItemMetaData,
        convertContentItem: ConvertContentItem?,
        itemIndex: Int
    ) {
        switchSentenceBackground(backgroundTextView, convertContentItem, currentItem, itemIndex)
    }


    private fun sentenceOfContentUnderLine(
        backgroundTextView: BackgroundTextView,
        currentItem: ConvertContentItem.TextItemMetaData,
        convertContentItem: ConvertContentItem,
        itemIndex: Int
    ) {
        if (!FunctionOption.IS_SUPPORT_NEW_UI_OF13) return
        if (currentItem.textParagraph?.isNullOrEmpty() == true) return
        if (currentItem.hasTextMark()) {
            val underline = mutableListOf<Pair<Int, Int>>()
            var count = 0
            for (sentencesIndex in currentItem.textParagraph!!.indices) {
                val subItem = currentItem.textParagraph?.get(sentencesIndex)
                if (subItem?.onlyHasSimpleMark == true) {
                    count++
                    var stringLengthBefore =
                        convertContentItem.getTextStringLengthBeforeTextImageItemIndex(itemIndex)
                            ?: 0
                    var space = count * TextImageMixLayout.SPACE.length  //当前段旗子所占用字符数
                    val startSeq = subItem.startCharSeq + space - stringLengthBefore
                    val endSeq =
                        subItem.endCharSeq + space - 1 - stringLengthBefore  //句尾不需要覆盖标点符号 减去标点符号所占用的字符数
                    DebugUtil.i(
                        TAG,
                        "sentenceOfContentUnderLine stringLengthBefore $stringLengthBefore, startSeq $startSeq, endSeq $endSeq"
                    )
                    underline.add(Pair(startSeq, endSeq))
                }
            }
            DebugUtil.i(
                TAG,
                "sentenceOfContentUnderLine   size = ${underline.size} underline == $underline"
            )
            if (underline.size > 0) {
                backgroundTextView.setIsUnderLine(underline, true)
            } else {
                backgroundTextView.setIsUnderLine(mutableListOf(), false)
            }
        } else {
            DebugUtil.i(
                TAG,
                "sentenceOfContentUnderLine hasMark = ${currentItem.hasTextMark()}" +
                        " listSubSentence is null or empty = ${currentItem.textParagraph?.isNullOrEmpty()}"
            )
            backgroundTextView.setIsUnderLine(mutableListOf(), false)
        }
    }


    fun setItemContent(
        itemTextContent: BackgroundTextView,
        currentItem: ConvertContentItem.TextItemMetaData?,
        itemIndex: Int
    ) {
        if (!FunctionOption.IS_SUPPORT_NEW_UI_OF13) {
            itemTextContent.text = currentItem?.getTextString()
        } else {
            setItemContentSpannable(
                SpannableStringBuilder(currentItem?.getTextString()),
                itemTextContent,
                currentItem,
                itemIndex
            )
        }
    }


    fun setItemContentSpannable(
        builder: SpannableStringBuilder,
        itemTextContent: BackgroundTextView,
        currentItem: ConvertContentItem.TextItemMetaData?,
        itemIndex: Int,
        searchMode: Boolean = false
    ) {
        if (currentItem == null) {
            DebugUtil.i(TAG, "setItemContentSpannable item == null, return")
            return
        }
        if (currentItem.textParagraph != null && currentItem.textParagraph!!.isNotEmpty()) {
            var start = 0
            for (sentencesIndex in currentItem.textParagraph!!.indices) {
                val sentencesItem = currentItem.textParagraph!![sentencesIndex]
                if (sentencesItem.onlyHasSimpleMark) {
                    val imageSpan = CenterImageSpan(itemTextContent.context, R.drawable.ic_red_flag)
                    builder.insert(start, TextImageMixLayout.SPACE)
                    builder.setSpan(
                        imageSpan,
                        start,
                        start + TextImageMixLayout.IMAGE_SPAN_SPACE,
                        Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
                    )
                    //todo
                    if (searchMode && itemTextContent.getHighLight()) { // 本TextView中有关键词被选中，存在标题ICON的字符长度，需要矫正index
                        val originStart = itemTextContent.getStartHighLightSeq()
                        val originEnd = itemTextContent.getEndHighLightSeq()

                        if (originStart >= start) {  //在高亮词前插入ImageSpan，需要矫正高亮词Index
                            itemTextContent.updateHighLight(
                                originStart + TextImageMixLayout.SPACE.length,
                                originEnd + TextImageMixLayout.SPACE.length,
                                true,
                                invalidate = false
                            )
                            //DebugUtil.i(TAG, "setItemContentSpannable originStart ${originStart}, originEnd $originEnd ")
                        }
                    }
                    start += TextImageMixLayout.SPACE.length
                }
                start += sentencesItem.text.length
            }
        }
        DebugUtil.i(TAG, "setItemContentSpannable  text = $builder")
        itemTextContent.text = builder
    }
    /**
     * change focus state background by Sentence
     */
    private fun switchSentenceBackground(
        backgroundTextView: BackgroundTextView,
        convertContentItem: ConvertContentItem?,
        currentItem: ConvertContentItem.TextItemMetaData,
        itemIndex: Int
    ) {

        if (convertContentItem == null) {
            return
        }
        if (currentItem.isFocuse() && !searchMode) {
            var count = 0
            var startSeq: Int = 0
            var endSeq: Int = 0
            var space = 0
            for (subItem in currentItem.textParagraph!!) {
                if (subItem.onlyHasSimpleMark) {
                    val hasFlag = backgroundTextView.hasFlagSpan(endSeq, endSeq + TextImageMixLayout.SPACE.length)
                    DebugUtil.i(TAG, "switchSentenceBackground data has mark start:$endSeq view has flag：$hasFlag")
                    if (hasFlag) {
                        count++
                    }
                }
                space = count * TextImageMixLayout.SPACE.length  //当前段旗子所占用字符数
                startSeq = subItem.startCharSeq + space
                endSeq = subItem.endCharSeq + space
                if (subItem.isFocused) {
                    var textLengthBeforeIndex =
                        convertContentItem.getTextStringLengthBeforeTextImageItemIndex(itemIndex)
                    var showStartSeq = startSeq - textLengthBeforeIndex
                    var showEndSeq = endSeq - textLengthBeforeIndex

                    backgroundTextView.updateHighLight(showStartSeq, showEndSeq, true, invalidate = true)
                    DebugUtil.i(TAG, "switchSentenceBackground ItemIndex $itemIndex startSeq $startSeq" +
                            ", endSeq $endSeq, subItem.startCharSeq ${subItem.startCharSeq}" +
                            ", subItem.endCharSeq ${subItem.endCharSeq}, space $space , textLengthBeforeIndex $textLengthBeforeIndex"
                    )
                }
            }
        } else {
            //当前整个item不是focus，设置为非高亮
            backgroundTextView.updateHighLight(0, 0, false, invalidate = true)
            backgroundTextView.background = null
        }
    }
}
