/*********************************************************************
 * * Copyright (C), 2025, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  PlaybackButtonPanel
 * * Description :
 * * Version     : 1.0
 * * Date        : 2025/06/16
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.playback.view

import android.content.Context
import android.util.AttributeSet
import androidx.constraintlayout.widget.ConstraintLayout
import com.soundrecorder.base.utils.DebugUtil

class PlaybackButtonPanel @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : ConstraintLayout(context, attrs, defStyleAttr) {

    private var callback: ((height: Int) -> Unit)? = null
    private var lastHeight = 0

    /**
     * 注册高度的监听
     */
    fun observeHeightChange(callback: ((height: Int) -> Unit)) {
        this.callback = callback
    }

    /**
     * 接触注册
     */
    fun unObserveHeightChange() {
        this.callback = null
    }

    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        DebugUtil.d("PlaybackButtonPanel", "onSizeChanged h = $h, oldh = $oldh")
        if (lastHeight != h) {
            callback?.invoke(h)
            lastHeight = h
        }
    }
}