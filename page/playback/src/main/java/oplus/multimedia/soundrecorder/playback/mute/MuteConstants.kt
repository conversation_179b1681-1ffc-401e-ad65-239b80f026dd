/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: MuteConstants
 Description:
 Version: 1.0
 Date: 2022/10/9
 Author: W9010241
 -----------Revision History-----------
 <author> <date> <version> <desc>
 W9010241 2022/10/9 1.0 create
 */


package oplus.multimedia.soundrecorder.playback.mute

object MuteConstants {

    const val TYPE_AUDIO = "audio/"

    const val MUTE_PATH = "/mutedata"

    const val MUTE_DETECT_WORKER = "mute_data_detect"
    //录音完成后预加载静音数据，只针对10 * 60 * 1000以上的时长预加载
    const val MUTE_DETECT_DURATION = 600000

    //顺滑时长，即跳过静音片段后返回一段距离，防止错过关键内容
    const val SMOOTH_TIME = 500
    //回退时滑到静音位置又跳转过去造成跳顿感，此时要求播放器继续播放一段时间后再检测跳转
    const val SMOOTH_TIME_BACK_TRACK = 500
    //最小静音时长
    const val THRESHOLD_TIME = 2000
    const val THRESHOLD_TIME_SECOND = 2
    //检测静音时过滤掉突变的声音波峰，时间间隔毫秒
    const val SENSITIVE_TIME = 2
    //最小静音分贝
    const val THRESHOLD_DB = -30

    //多媒体传递pcm数据时，每次只传输10ms大小的buffer
    const val MEDIA_FRAME_TIMES = 10
    //音频文件本身属性
    const val CHANNEL_COUNT_1 = 1
    const val CHANNEL_COUNT_2 = 2
    const val NUM_2 = 2

    const val PLAY_INDEX_DEFAULT = 1

    const val CHECK_STATUS_OK = 1
    const val CHECK_STATUS_INVALID_FORMAT = 2
    const val CHECK_STATUS_NO_AUDIO_TRACK = 3
    const val CHECK_STATUS_NO_INPUT_FILE_DESCRIPTOR = 4
    const val CHECK_STATUS_NO_OUTPUT_FILE_DESCRIPTOR = 5
    const val CHECK_STATUS_NO_OUTPUT_FILE_URI = 6
    const val CHECK_STATUS_NOT_ENOUGH_DISKSPACE = 7
    const val CHECK_STATUS_DATA_SOURCE_ERROR = 8

    const val CONVERT_STATUS_START = 0
    const val CONVERT_STATUS_COMPLETE = 1
    const val CONVERT_STATUS_ERROR = 2
    const val CONVERT_STATUS_CANCELED = 3
    const val CONVERT_STATUS_SPACE_NOTENOUGH_CANCELED = 4
}