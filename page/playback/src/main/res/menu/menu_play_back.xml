<?xml version="1.0" encoding="utf-8"?>
<menu xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <item
        android:id="@+id/search"
        android:icon="@drawable/menu_ic_search"
        android:title="@string/search"
        app:actionViewClass="com.coui.appcompat.searchview.COUISearchBar"
        app:showAsAction="always|collapseActionView" />

    <item
        android:id="@+id/export"
        android:enabled="true"
        android:icon="@drawable/menu_ic_share_normal"
        android:title="@string/share"
        app:showAsAction="always" />

    <item
        android:id="@+id/convert_role"
        android:enabled="true"
        android:title="@string/export_show_speaker"
        app:showAsAction="never" />

    <item
        android:id="@+id/play_setting"
        android:enabled="true"
        android:title="@string/play_setting_title"
        app:showAsAction="never" />

    <item
        android:id="@+id/speaker"
        android:enabled="true"
        android:title="@string/talk_back_speaker_play"
        app:showAsAction="never" />

    <group
        android:id="@+id/group_1"
        android:enabled="true">
        <item
            android:id="@+id/cut"
            android:enabled="true"
            android:title="@string/cut_new"
            app:showAsAction="never" />

        <item
            android:id="@+id/rename"
            android:enabled="true"
            android:title="@string/rename"
            app:showAsAction="never" />

        <item
            android:id="@+id/rename_new"
            android:enabled="true"
            android:title="@string/rename"
            app:showAsAction="never">
            <menu>
                <item
                    android:id="@+id/custom_naming"
                    android:checkable="true"
                    android:checked="true"
                    android:title="@string/custom_name"
                    app:showAsAction="never" />

                <item
                    android:id="@+id/smart_naming"
                    android:checkable="true"
                    android:title="@string/intelligent_name"
                    app:showAsAction="never" />
            </menu>
        </item>

        <item
            android:id="@+id/set_ringtone"
            android:enabled="true"
            android:title="@string/set_as"
            app:showAsAction="never" />
        <item
            android:id="@+id/move"
            android:enabled="true"
            android:title="@string/move"
            app:showAsAction="never" />
        <item
            android:id="@+id/detail"
            android:enabled="true"
            android:title="@string/talkback_detail"
            app:showAsAction="never" />
        <item
            android:id="@+id/delete"
            android:enabled="true"
            android:title="@string/delete"
            app:showAsAction="never" />
    </group>
</menu>