<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <com.coui.appcompat.toolbar.COUIToolbar
        android:id="@+id/mark_list_toolbar"
        style="@style/Widget.COUI.Toolbar.Panel"
        android:background="@null"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.recyclerview.widget.COUIRecyclerView
        android:id="@+id/mark_list_recycler_view"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginStart="@dimen/dp16"
        android:layout_marginTop="@dimen/dp12"
        android:layout_marginEnd="@dimen/dp16"
        android:layout_marginBottom="@dimen/dp12"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHeight_default="spread"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/mark_list_toolbar" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/iv_mark_list_empty_view"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginStart="@dimen/dp16"
        android:layout_marginTop="@dimen/dp16"
        android:layout_marginEnd="@dimen/dp16"
        android:layout_marginBottom="@dimen/dp24"
        android:background="@drawable/empty_mark_list_bg"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHeight_default="spread"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/mark_list_toolbar">

        <com.soundrecorder.common.widget.OSImageView
            android:id="@+id/iv_mark_list_empty_image"
            android:layout_width="@dimen/dp204"
            android:layout_height="@dimen/dp146"
            app:anim_raw_json="@raw/ic_mark_list_empty"
            app:anim_raw_json_night="@raw/ic_mark_list_empty_night"
            app:img_draw="@drawable/ic_mark_list_empty"
            app:layout_constraintBottom_toTopOf="@+id/tv_mark_empty_text"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_chainStyle="packed" />

        <TextView
            android:id="@+id/tv_mark_empty_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:fontFamily="sans-serif-medium"
            android:gravity="center"
            android:orientation="vertical"
            android:text="@string/no_mark"
            android:textColor="@color/coui_color_label_primary"
            android:textFontWeight="500"
            android:textSize="@dimen/sp16"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/iv_mark_list_empty_image"
            app:layout_constraintBottom_toBottomOf="parent"/>
    </androidx.constraintlayout.widget.ConstraintLayout>


</androidx.constraintlayout.widget.ConstraintLayout>