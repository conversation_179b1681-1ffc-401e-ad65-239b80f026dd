<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/layout_share_summary_root"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:id="@+id/color_load_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        tools:visibility="gone">

        <com.coui.appcompat.progressbar.COUILoadingView
            style="?attr/couiLoadingViewLargeStyle"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_above="@+id/loadingTip"
            android:layout_centerHorizontal="true"
            android:layout_marginBottom="10dp"
            tools:ignore="ObsoleteLayoutParam" />

        <TextView
            android:id="@+id/loadingTip"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:layout_centerVertical="true"
            android:text="@string/oplus_loading_dialog_text_view"
            tools:ignore="ObsoleteLayoutParam" />
    </LinearLayout>

    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/appbar_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@null"
        android:paddingTop="@dimen/dp42"
        app:elevation="0dp"
        app:layout_constraintTop_toTopOf="parent">

        <com.coui.appcompat.toolbar.COUIToolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:minHeight="@dimen/recorder_toolbar_height"
            app:navigationIcon="@drawable/ic_home_back_arrow" />

    </com.google.android.material.appbar.AppBarLayout>

    <RelativeLayout
        android:id="@+id/body_summary"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="@dimen/dp24"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/appbar_layout">

        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_above="@id/navi_menu_tool_share_summary"
            android:layout_alignParentTop="true"
            android:fillViewport="true"
            android:paddingStart="@dimen/responsive_ui_margin_large"
            android:paddingEnd="@dimen/responsive_ui_margin_large">

            <com.coui.appcompat.textview.COUITextView
                android:id="@+id/tv_share_summary_content"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textColor="@color/black_color"
                android:textSize="@dimen/sp14"
                tools:text="@string/app_name" />
        </ScrollView>

        <com.coui.appcompat.bottomnavigation.COUINavigationView
            android:id="@+id/navi_menu_tool_share_summary"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp56"
            android:layout_alignParentBottom="true"
            app:couiNaviMenu="@menu/bottom_navigation_menu_share_summary"
            app:couiToolNavigationViewBg="@color/share_txt_navigation_color" />
    </RelativeLayout>

</androidx.constraintlayout.widget.ConstraintLayout>