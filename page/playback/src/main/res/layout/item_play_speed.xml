<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="52dp"
    android:background="@drawable/item_mark_selector"
    android:paddingStart="@dimen/common_margin"
    android:paddingEnd="@dimen/common_margin">

    <TextView
        android:id="@+id/itemTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentStart="true"
        android:layout_centerVertical="true"
        android:textColor="@color/percent_85_black"
        android:textSize="@dimen/sp16" />

    <RadioButton
        android:id="@+id/itemBtn"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentEnd="true"
        android:layout_centerVertical="true"
        android:clickable="false"
        android:focusable="false"
        android:textColor="@color/percent_85_black" />

</RelativeLayout>