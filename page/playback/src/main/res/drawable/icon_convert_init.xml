<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="280dp"
    android:height="200dp"
    android:viewportWidth="280"
    android:viewportHeight="200">
  <group>
    <clip-path
        android:pathData="M78,13h123.43v174.35h-123.43z"/>
    <path
        android:pathData="M174.3,93.07H121.06C119.88,93.23 118.69,93.1 117.57,92.69C116.45,92.28 115.45,91.62 114.65,90.74C112.41,88.41 113.83,84.36 113.83,84.36H169C169,84.36 167.59,88.36 169.83,90.74C171.01,92.01 172.59,92.84 174.3,93.07Z"
        android:strokeLineJoin="round"
        android:strokeWidth="0.6"
        android:fillColor="#00000000"
        android:strokeColor="#808080"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M174.3,93.07C172.58,92.84 170.99,92.02 169.81,90.74C167.57,88.41 168.98,84.36 168.98,84.36H128.52C128.64,83.23 128.64,82.08 128.52,80.95C127.28,70.77 118.52,53.15 118.52,35.56C118.47,17.46 122.91,12.8 129.12,13.34H182.48C178.71,13.71 175.79,16.6 174.48,24.23C173.88,27.98 173.61,31.77 173.66,35.56C173.66,53.15 182.44,70.77 183.66,80.95C184.62,89.11 179.29,93.73 174.3,93.07Z"
        android:strokeLineJoin="round"
        android:strokeWidth="0.6"
        android:fillColor="#00000000"
        android:strokeColor="#808080"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M190.66,24.23H174.44C175.79,16.6 178.71,13.71 182.44,13.34H184.24C191.86,14 190.66,24.23 190.66,24.23Z"
        android:strokeLineJoin="round"
        android:strokeWidth="0.6"
        android:fillColor="#00000000"
        android:strokeColor="#808080"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M89,79.22L89.11,79.33V79.22H89Z"
        android:fillColor="#7E73E5"/>
    <path
        android:pathData="M88.97,26C89.36,25.91 89.76,25.84 90.16,25.79C92.21,25.59 94.25,26.2 95.84,27.5C97.43,28.81 98.44,30.69 98.64,32.73L98.8,34.18C99.65,32.68 99.9,30.83 99.35,29.03C98.3,25.59 94.71,23.64 91.33,24.67C90.43,24.95 89.63,25.41 88.97,26Z"
        android:fillColor="#424447"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M96.2,27.36C96.17,27.18 96.01,27.03 95.87,27.18C94.68,28.61 93.27,29.85 91.7,30.84C89.99,31.84 88.2,32.69 86.35,33.37C87.21,33.83 88.21,34.27 89.23,34.51C89.43,34.34 89.66,34.22 89.92,34.16C90.28,34.08 90.66,34.11 91,34.27C91.19,34.35 91.36,34.47 91.51,34.62C91.98,34.53 92.42,34.35 92.82,34.07C95.4,32.23 96.49,29.07 96.2,27.36Z"
        android:fillColor="#424447"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M89.67,35.72C89.74,35.62 89.84,35.54 89.95,35.49C90.06,35.44 90.19,35.42 90.31,35.43C90.56,35.47 90.79,35.57 90.98,35.73C91.16,35.9 91.32,36.09 91.45,36.3"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#808080"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M92.28,168.5C92.54,172.39 92.76,175.63 93,177.29L91.29,182C91.18,182.26 91.12,182.54 91.13,182.83C91.14,183.11 91.21,183.39 91.34,183.65C91.47,183.9 91.66,184.12 91.88,184.29C92.11,184.46 92.37,184.58 92.65,184.64L104.22,186.15L109.37,186.2C109.37,186.2 109.99,184.82 108.12,184.28C108.12,184.28 104.23,182.99 103.12,181.65L99.9,176.94C99.9,176.94 100.1,173.38 100.39,168.25V168.6"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#808080"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M86.51,47.65L87.41,45.15C87.47,44.99 87.58,44.86 87.73,44.78C87.87,44.69 88.04,44.67 88.21,44.7L95.21,46.22C95.43,46.27 95.62,46.4 95.75,46.58C95.88,46.77 95.93,47 95.9,47.22L95.77,48.22C97,49.35 103.27,55.12 103.27,59.75C103.53,67.55 102.35,69.42 103.53,78.75H92.5"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#808080"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M81.33,34.76C81.54,37.02 82.61,39.12 84.33,40.61C85.56,41.52 86.95,42.2 88.42,42.61C88.67,42.67 88.96,42.14 89.07,41.88C89.21,41.52 89.4,40.93 89.65,40.19L89.65,40.19C89.74,39.9 89.85,39.59 89.96,39.26C89.55,39.13 89.26,38.75 89.26,38.3C89.26,37.89 89.5,37.54 89.85,37.38C89.11,36.73 88.47,35.5 89,34.74C89.23,34.45 89.56,34.25 89.92,34.16C90.28,34.08 90.66,34.11 91,34.27C91.23,34.36 91.43,34.52 91.59,34.71C92.69,31.89 94.02,29.11 95.36,27.97L96.29,27.27C93.29,24.27 85.49,25.27 82.91,28.77C81.67,30.5 81.1,32.64 81.33,34.76Z"
        android:fillColor="#424447"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M90.27,39.31C90.83,39.31 91.28,38.86 91.28,38.3C91.28,37.74 90.83,37.29 90.27,37.29C89.71,37.29 89.26,37.74 89.26,38.3C89.26,38.86 89.71,39.31 90.27,39.31Z"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#808080"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M104.9,182.81L103.36,184.38L91.69,180.48C91.67,180.47 91.64,180.47 91.62,180.47C91.59,180.47 91.57,180.48 91.55,180.49C91.53,180.5 91.51,180.52 91.49,180.54C91.48,180.55 91.47,180.58 91.46,180.6C91.34,180.99 91.06,181.94 90.96,182.31C90.85,182.69 90.8,183.09 90.82,183.49L91.13,186.8H95.56L95.72,185.72L100.8,186.8H111.17C111.24,186.8 111.31,186.79 111.37,186.76C111.43,186.73 111.49,186.69 111.54,186.64C111.59,186.6 111.63,186.54 111.65,186.47C111.68,186.41 111.69,186.34 111.69,186.27V185.94C111.69,185.61 111.58,185.29 111.39,185.03C111.19,184.76 110.92,184.57 110.6,184.47L104.9,182.81Z"
        android:fillColor="#424447"/>
    <path
        android:pathData="M80.38,168C80.16,173.26 80.07,177 80.07,177L78.81,183.63L80.81,185.97L86.34,185.72C86.69,185.7 87.03,185.6 87.33,185.43C87.63,185.26 87.89,185.01 88.08,184.72C88.27,184.42 88.38,184.09 88.42,183.74C88.45,183.4 88.4,183.04 88.27,182.72L86.42,178.2L88.58,168.15M80.38,168C80.39,167.78 80.4,167.55 80.41,167.32L80.38,168Z"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#808080"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M93.43,185.82V186.23C93.43,186.31 93.41,186.38 93.38,186.45C93.35,186.52 93.3,186.59 93.24,186.64C93.19,186.69 93.12,186.74 93.05,186.76C92.97,186.79 92.9,186.8 92.82,186.8H78.35L78,183.12C77.96,182.84 78.02,182.55 78.17,182.31L79.08,180.71C79.11,180.66 79.14,180.62 79.19,180.6C79.23,180.57 79.28,180.55 79.34,180.54C79.39,180.54 79.44,180.55 79.49,180.57C79.54,180.59 79.59,180.62 79.62,180.66L82.08,183.37C83.98,182.59 86.02,182.19 88.08,182.19L92.44,184.36C92.73,184.48 92.98,184.68 93.15,184.94C93.33,185.2 93.42,185.51 93.43,185.82Z"
        android:fillColor="#424447"/>
    <path
        android:pathData="M78.81,184.04L79.68,186.53"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#808080"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M94.84,46L95.17,42.82L96.66,42.96C97.01,43.02 97.37,43 97.72,42.9C98.06,42.8 98.38,42.62 98.65,42.39C98.91,42.15 99.13,41.85 99.26,41.52C99.4,41.19 99.47,40.84 99.45,40.48L99.18,37.76C99.48,37.36 99.86,37.02 100.3,36.76C100.34,36.74 100.37,36.71 100.39,36.67C100.41,36.63 100.42,36.59 100.42,36.54C100.42,36.5 100.41,36.46 100.39,36.42C100.37,36.38 100.34,36.35 100.3,36.33C99.72,36.06 99.26,35.58 99,35C98.82,34.45 98.74,31.23 97.48,29.41C97.17,29 96.31,28 96.31,28"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#808080"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M89.05,41.84L88.66,44.73"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#808080"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M96.81,38.47C97.4,38.19 98.07,38.09 98.72,38.19C99.26,38.45 98.88,38.98 98.88,38.98C98.91,39.07 98.92,39.16 98.91,39.25C98.89,39.35 98.86,39.43 98.81,39.51C98.76,39.59 98.69,39.66 98.61,39.7C98.53,39.75 98.44,39.78 98.35,39.79C97.37,40 96.81,38.47 96.81,38.47Z"
        android:fillColor="#808080"/>
    <path
        android:pathData="M99.05,38.89C98.68,39.02 98.28,39.04 97.89,38.97C97.5,38.9 97.14,38.72 96.84,38.47"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#323739"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M97.34,34.26C97.34,34.65 97.48,34.92 97.67,34.89C97.86,34.86 97.92,34.57 97.92,34.23C97.92,33.89 97.78,33.57 97.59,33.59C97.4,33.61 97.33,33.87 97.34,34.26Z"
        android:fillColor="#323739"/>
    <path
        android:pathData="M96.59,33.42C96.73,33.23 96.89,33.05 97.08,32.9C97.18,32.83 97.29,32.79 97.41,32.77C97.53,32.76 97.65,32.77 97.76,32.82"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#323739"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M95.32,32.77C95.75,32.14 96.39,31.69 97.12,31.49C97.45,31.41 97.79,31.41 98.12,31.49"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#323739"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M83,75.2C83,75.18 83,75.16 83,75.14V75.2Z"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#DEDEDE"
        android:strokeColor="#808080"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M88.51,42.14C87.28,41.94 86.13,41.39 85.19,40.56C84.35,39.89 83.65,39.05 83.13,38.11C82.62,37.16 82.3,36.12 82.19,35.05C82.18,34.98 82.18,34.91 82.17,34.83L81.28,34.78C81.49,38.52 81.49,42.26 81.28,46C81.09,47.66 80.5,50.56 79.89,53.56C79.58,55.06 79.27,56.59 79,58C78.98,58.11 78.99,58.23 79.03,58.34C79.06,58.45 79.12,58.55 79.19,58.63C79.27,58.71 79.36,58.78 79.47,58.82C79.57,58.87 79.69,58.89 79.8,58.88H81.18L80.64,54.06V53.7C80.55,52.75 80.76,51.79 81.24,50.97C81.72,50.14 82.45,49.48 83.32,49.08C83.76,48.88 84.24,48.75 84.72,48.69L86.58,47.65H86.51L87.41,45.15C87.47,44.99 87.58,44.86 87.73,44.78C87.87,44.69 88.04,44.67 88.21,44.7L88.66,44.8L88.93,42.18L88.51,42.14Z"
        android:fillColor="#424447"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M93.76,101C93.97,102.08 94.42,104.76 94.42,104.76H93"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#808080"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M86.38,47.3C86.15,47.3 85.92,47.37 85.72,47.5L84.27,48.53C84.23,48.56 84.2,48.59 84.18,48.63C84.16,48.67 84.15,48.72 84.15,48.76C84.15,48.77 84.15,48.78 84.15,48.79C84.79,48.64 85.45,48.62 86.09,48.74C87.03,48.92 87.9,49.38 88.56,50.07C89.34,50.85 89.81,51.88 89.88,52.98C89.89,53.11 89.89,53.24 89.88,53.37C89.94,53.81 89.94,54.21 89.94,54.62V54.62C89.97,55.14 89.98,55.65 89.99,56.16C90.01,56.84 90.03,57.52 90.08,58.22C90.16,59.85 90.23,61.43 90.3,63.09C90.35,64.17 90.39,65.18 90.44,66.26C90.46,66.85 90.49,67.45 90.52,68.09C90.6,69.91 90.81,74.85 90.81,74.85C90.81,74.85 91.65,77.67 92.07,79.06L103.67,78.61C103.67,78.61 103.41,66.27 103.76,62.18C103.85,60.39 103.47,58.6 102.66,57L101.21,71.33C101.21,71.33 99.18,66.43 96.84,60.67C93.43,52.3 88.77,48.63 87.04,47.5C86.85,47.37 86.62,47.3 86.38,47.3Z"
        android:fillColor="#534D8A"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M174.66,112.79C174.36,110.48 174.76,108.13 175.83,106.06C176.85,104.32 178.05,102.7 179.43,101.23C180.8,99.75 181.86,98.02 182.55,96.12C183.44,98.62 183.56,101.33 182.91,103.9C182.25,106.47 180.85,108.78 178.87,110.55C177.57,111.71 176.22,112.71 175.03,114C174.75,113.79 174.7,113.13 174.66,112.79Z"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#808080"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M175.17,113.44C175.6,110.9 177.17,108.71 178.62,106.62C180.07,104.53 181.69,102.4 182.27,99.89"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#808080"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M177.79,122.7H172.27C171.21,122.7 170.39,122 170.56,121.22L171.69,116.06C171.81,115.73 172.02,115.45 172.31,115.26C172.6,115.07 172.94,114.97 173.29,114.99V114.3C173.29,114.15 173.35,114.01 173.45,113.91C173.55,113.81 173.7,113.75 173.84,113.75H176.22C176.29,113.75 176.37,113.76 176.43,113.79C176.5,113.82 176.56,113.86 176.61,113.91C176.66,113.96 176.7,114.02 176.73,114.09C176.76,114.15 176.77,114.23 176.77,114.3V115C177.12,114.98 177.46,115.08 177.75,115.27C178.04,115.46 178.26,115.74 178.38,116.07L179.51,121.23C179.67,122 178.86,122.7 177.79,122.7Z"
        android:fillColor="#534D8A"/>
    <path
        android:pathData="M166.86,114.94H141.21C140.57,114.94 140.06,115.46 140.06,116.09V121.55C140.06,122.18 140.57,122.7 141.21,122.7H166.86C167.5,122.7 168.01,122.18 168.01,121.55V116.09C168.01,115.46 167.5,114.94 166.86,114.94Z"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#808080"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M91.93,106.41C91.96,106.55 92.05,106.68 92.16,106.77C92.28,106.86 92.42,106.91 92.57,106.91H96.02C96.33,106.9 96.63,106.79 96.87,106.59C97.11,106.39 97.28,106.12 97.34,105.81L98.15,101.73C98.16,101.65 98.16,101.57 98.15,101.49C98.14,101.42 98.12,101.33 98.1,101.21L98.1,101.21L98.1,101.21C97.91,100.1 97.39,97.02 97.07,95.86C97.07,95.62 96.15,92.54 95.58,90.66L95.58,90.65C95.44,90.19 95.33,89.8 95.25,89.54C95.02,88.79 94.79,88.03 94.56,87.27L94.56,87.27C94.29,86.37 94.01,85.46 93.73,84.54C93.21,82.86 92.73,81.3 92.25,79.68C92.19,79.49 92.09,79.12 91.95,78.68L103.67,78.58L105.58,88.29C107.04,95.86 107.64,109.18 107.82,122.85H104.23C102.47,122.85 101.05,124.27 101.05,126.03C101.05,127.79 102.47,129.21 104.23,129.21H107.88C107.97,149.72 107.26,169.14 107.26,169.14L79.21,168.46C79.21,168.46 80.35,123.58 80.54,118.12C80.85,109 81.69,95.47 84,88.86L86.21,83.05L92.02,96.33C92.03,96.67 92.07,97 92.14,97.33C92.49,98.62 92.97,99.86 93.57,101.05C93.42,101.06 93.28,101.12 93.18,101.22C93.07,101.32 93.01,101.46 93,101.61L92.73,104.77L92.37,103.67C92.33,103.59 92.28,103.52 92.21,103.46C92.14,103.4 92.06,103.36 91.98,103.34C91.92,103.33 91.85,103.33 91.79,103.34C91.71,103.35 91.63,103.38 91.57,103.42C91.5,103.46 91.44,103.51 91.39,103.58C91.35,103.64 91.32,103.72 91.3,103.79C91.29,103.87 91.29,103.95 91.31,104.03L91.93,106.41Z"
        android:fillColor="#534D8A"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M198.05,122.85H104.23C102.47,122.85 101.05,124.27 101.05,126.03C101.05,127.79 102.47,129.21 104.23,129.21H198.05C199.81,129.21 201.23,127.79 201.23,126.03C201.23,124.27 199.81,122.85 198.05,122.85Z"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#808080"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M179.51,129.21V129.01C179.45,129.01 179.4,129.03 179.36,129.07C179.32,129.11 179.3,129.16 179.31,129.22L179.51,129.21ZM187.13,129.21H187.33C187.33,129.1 187.24,129.01 187.13,129.01V129.21ZM187.13,187.15V187.35C187.24,187.35 187.33,187.26 187.33,187.15H187.13ZM182.09,187.15L181.89,187.16C181.9,187.27 181.98,187.35 182.09,187.35V187.15ZM179.51,129.41H187.13V129.01H179.51V129.41ZM186.93,129.21V187.15H187.33V129.21H186.93ZM187.13,186.95H182.09V187.35H187.13V186.95ZM182.29,187.14L179.71,129.2L179.31,129.22L181.89,187.16L182.29,187.14Z"
        android:fillColor="#808080"/>
    <path
        android:pathData="M91.93,106.41C91.96,106.55 92.05,106.68 92.16,106.77C92.28,106.86 92.42,106.91 92.57,106.91H96.02C96.33,106.9 96.63,106.79 96.87,106.59C97.11,106.39 97.28,106.12 97.34,105.81L98.15,101.73C98.16,101.65 98.16,101.57 98.15,101.49C98.03,100.79 97.43,97.15 97.07,95.86C97.07,95.56 95.64,90.86 95.25,89.54C94.74,87.88 94.25,86.22 93.73,84.54C93.21,82.86 92.73,81.3 92.25,79.68C92.01,78.86 90.81,74.85 90.81,74.85C90.81,74.85 90.6,69.91 90.52,68.09C90.44,66.27 90.37,64.75 90.3,63.09C90.23,61.43 90.16,59.85 90.08,58.22C90,57 90,55.83 89.94,54.62C89.94,54.21 89.94,53.81 89.88,53.37C89.89,53.24 89.89,53.11 89.88,52.98C89.81,51.88 89.34,50.85 88.56,50.07C87.9,49.38 87.03,48.92 86.09,48.74C85.16,48.57 84.18,48.69 83.32,49.08C82.45,49.48 81.72,50.14 81.24,50.97C80.76,51.79 80.55,52.75 80.64,53.7V54.06L83,75.14C83,75.16 83,75.18 83,75.2C83.04,75.76 83.2,76.31 83.48,76.8L92.02,96.33C92.03,96.67 92.07,97 92.14,97.33C92.49,98.62 92.97,99.86 93.57,101.05C93.42,101.06 93.28,101.12 93.18,101.22C93.07,101.32 93.01,101.46 93,101.61L92.73,104.77L92.37,103.67C92.33,103.59 92.28,103.52 92.21,103.46C92.14,103.4 92.06,103.36 91.98,103.34C91.92,103.33 91.85,103.33 91.79,103.34C91.71,103.35 91.63,103.38 91.57,103.42C91.5,103.46 91.44,103.51 91.39,103.58C91.35,103.64 91.32,103.72 91.3,103.79C91.29,103.87 91.29,103.95 91.31,104.03L91.93,106.41Z"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#808080"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M115.77,129.21V129.01C115.66,129.01 115.57,129.1 115.57,129.21H115.77ZM123.39,129.21L123.59,129.22C123.6,129.16 123.57,129.11 123.54,129.07C123.5,129.03 123.45,129.01 123.39,129.01V129.21ZM120.82,187.15V187.35C120.93,187.35 121.01,187.27 121.02,187.16L120.82,187.15ZM115.77,187.15H115.57C115.57,187.26 115.66,187.35 115.77,187.35V187.15ZM115.77,129.41H123.39V129.01H115.77V129.41ZM121.02,187.16L123.59,129.22L123.19,129.2L120.62,187.14L121.02,187.16ZM115.77,187.35H120.82V186.95H115.77V187.35ZM115.57,129.21V187.15H115.97V129.21H115.57Z"
        android:fillColor="#808080"/>
    <path
        android:pathData="M132.32,51L162.32,51A2,2 0,0 1,164.32 53L164.32,53A2,2 0,0 1,162.32 55L132.32,55A2,2 0,0 1,130.32 53L130.32,53A2,2 0,0 1,132.32 51z"
        android:fillColor="#5680BF"/>
    <path
        android:pathData="M134.32,59.44L164.32,59.44A2,2 0,0 1,166.32 61.44L166.32,61.44A2,2 0,0 1,164.32 63.44L134.32,63.44A2,2 0,0 1,132.32 61.44L132.32,61.44A2,2 0,0 1,134.32 59.44z"
        android:strokeLineJoin="round"
        android:strokeWidth="0.6"
        android:fillColor="#00000000"
        android:strokeColor="#808080"/>
    <path
        android:pathData="M136.32,67.88L166.32,67.88A2,2 0,0 1,168.32 69.88L168.32,69.88A2,2 0,0 1,166.32 71.88L136.32,71.88A2,2 0,0 1,134.32 69.88L134.32,69.88A2,2 0,0 1,136.32 67.88z"
        android:fillColor="#A0BCE4"/>
    <path
        android:pathData="M141.57,27.88C141.94,28 142.34,27.81 142.46,27.44L143.74,23.65C143.86,23.28 143.66,22.89 143.3,22.76L139.51,21.49C139.14,21.36 138.74,21.56 138.62,21.93C138.5,22.29 138.69,22.69 139.06,22.81L141.2,23.53C137.96,25.37 135.77,28.85 135.77,32.85C135.77,38.76 140.56,43.55 146.47,43.55C149.46,43.55 152.17,42.32 154.11,40.34C154.38,40.06 154.38,39.62 154.1,39.35C153.83,39.08 153.38,39.08 153.11,39.36C151.42,41.08 149.07,42.15 146.47,42.15C141.34,42.15 137.17,37.98 137.17,32.85C137.17,29.38 139.07,26.35 141.89,24.75L141.13,26.99C141.01,27.36 141.21,27.76 141.57,27.88ZM151.52,23.78C151.52,24.27 151.12,24.68 150.62,24.68C150.12,24.68 149.72,24.27 149.72,23.78C149.72,23.28 150.12,22.88 150.62,22.88C151.12,22.88 151.52,23.28 151.52,23.78ZM153.43,26.48C153.93,26.48 154.33,26.07 154.33,25.58C154.33,25.08 153.93,24.68 153.43,24.68C152.93,24.68 152.53,25.08 152.53,25.58C152.53,26.07 152.93,26.48 153.43,26.48ZM146.54,27.38C146.88,27.38 147.16,27.64 147.18,27.98L147.41,32.31L149.66,34.87C149.88,35.13 149.87,35.51 149.63,35.75C149.39,35.99 149.01,36 148.75,35.77L145.82,33.2C145.71,33.09 145.65,32.95 145.65,32.79L145.91,27.98C145.93,27.64 146.21,27.38 146.54,27.38ZM157.23,31.25C157.23,31.75 156.83,32.15 156.33,32.15C155.83,32.15 155.43,31.75 155.43,31.25C155.43,30.75 155.83,30.35 156.33,30.35C156.83,30.35 157.23,30.75 157.23,31.25ZM156.33,35.28C156.83,35.28 157.23,34.88 157.23,34.38C157.23,33.88 156.83,33.48 156.33,33.48C155.83,33.48 155.43,33.88 155.43,34.38C155.43,34.88 155.83,35.28 156.33,35.28ZM155.43,38.25C155.93,38.25 156.33,37.85 156.33,37.35C156.33,36.85 155.93,36.45 155.43,36.45C154.93,36.45 154.53,36.85 154.53,37.35C154.53,37.85 154.93,38.25 155.43,38.25ZM156.33,28.28C156.33,28.78 155.93,29.18 155.43,29.18C154.93,29.18 154.53,28.78 154.53,28.28C154.53,27.78 154.93,27.38 155.43,27.38C155.93,27.38 156.33,27.78 156.33,28.28Z"
        android:fillColor="#A0BCE4"
        android:fillType="evenOdd"/>
  </group>
</vector>
