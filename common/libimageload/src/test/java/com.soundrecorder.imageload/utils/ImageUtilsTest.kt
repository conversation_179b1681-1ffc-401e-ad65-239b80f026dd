package com.soundrecorder.imageload.utils

import android.net.Uri
import android.os.Build
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.base.utils.FileUtils
import com.soundrecorder.imageload.shadows.ShadowFeatureOption
import com.soundrecorder.imageload.shadows.ShadowOS12FeatureUtil
import com.soundrecorder.imageload.utils.ImageUtils.uri2File
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.cancel
import kotlinx.coroutines.launch
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowOS12FeatureUtil::class, ShadowFeatureOption::class])
class ImageUtilsTest {
    private lateinit var mainScope: CoroutineScope
    @Before
    fun setUp() {
        mainScope = MainScope()
    }

    @After
    fun tearDown() {
        mainScope.cancel()
    }

    @Test
    fun uri2File() {
        val file = FileUtils.getAppFile()
        Assert.assertTrue(file.exists())
        mainScope.launch {
            val flag = Uri.fromFile(file).uri2File(FileUtils.getAppFile("", false))
            Assert.assertNull(flag)
        }
    }

    @Test
    fun test_getAppFile() {
        val file = FileUtils.getAppFile()
        Assert.assertTrue(file.exists())

        val fileNotExists = FileUtils.getAppFile("", false)
        Assert.assertFalse(fileNotExists.exists())

        val fileName1 = "IMG_SOUNDER_${System.currentTimeMillis()}.jpg"
        val file1 = FileUtils.getAppFile(fileName1, true)
        Assert.assertTrue(file1.name.equals(fileName1))
        Assert.assertTrue(file1.exists())

        val file2 = FileUtils.getAppFile(fileName1, false)
        Assert.assertTrue(file2.name.equals(fileName1))
        Assert.assertTrue(file2 == file1)
    }
}