apply from:"../../common_build.gradle"

android {
    namespace "com.oplus.recorderlogx"
}

dependencies {

    implementation libs.androidx.core.ktx
    implementation libs.androidx.appcompat

    //NearX-日志打捞sdk 主包
    implementation libs.oplus.log
    //NearX-日志打捞sdk 国内域名包（内销版本依赖）
    implementation libs.oplus.log.cn

    implementation project(':common:modulerouter')
    implementation project(':common:RecorderLogBase')

    // Koin for Android
    implementation(libs.koin)
}