/*********************************************************************
 * * Copyright (C), 2021, OPlus. All rights reserved.
 * * VENDOR_EDIT
 * * File        :  -
 * * Version     : 1.0
 * * Date        : 2021/10/8
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.common.sync

import android.text.TextUtils
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.FileUtils
import com.soundrecorder.common.constant.Constants
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.powermock.api.mockito.PowerMockito
import org.powermock.core.classloader.annotations.PrepareForTest
import org.powermock.modules.junit4.PowerMockRunner
import java.io.File
import java.io.FileNotFoundException
import java.io.FileOutputStream
import java.io.IOException
import java.nio.file.Files
import java.nio.file.Paths

@RunWith(PowerMockRunner::class)
@PrepareForTest(TextUtils::class, DebugUtil::class)
class SyncFileUtilsTest {
    private val TEST_PATH = "testPath"
    private val TEST_PATH_A = "testPath/A"
    private val A_txt = "a.txt"
    private val TEST_PATH_B_TXT = "testPath/B.txt"
    private val TEST_PATH_TXT = "testPath/test.txt"
    private val PATH_OF_FILE = "/path/of/file"

    @Before
    fun setUp() {
        PowerMockito.mockStatic(DebugUtil::class.java)
    }

    @Test
    fun should_file_name_no_ex_when_get_file_name_no_ex() {
        val fileName1 = FileUtils.getFileNameNoEx("test1.txt")
        Assert.assertEquals("test1", fileName1)
        val fileName2 = FileUtils.getFileNameNoEx("noEx")
        Assert.assertEquals("noEx", fileName2)
        val fileName3 = FileUtils.getFileNameNoEx("")
        Assert.assertEquals("", fileName3)
        val fileName4 = FileUtils.getFileNameNoEx(".test2.jpg")
        Assert.assertEquals(".test2", fileName4)
        val nullValue = FileUtils.getFileNameNoEx(null)
        Assert.assertEquals(null, nullValue)
    }

    @Test
    fun should_file_name_suffix_when_get_suffix() {
        val suffixName1 = FileUtils.getSuffix("test1.txt")
        Assert.assertEquals(".txt", suffixName1)
        val suffixName2 = FileUtils.getSuffix("noEx")
        Assert.assertEquals("", suffixName2)
        val suffixName3 = FileUtils.getSuffix("")
        Assert.assertEquals("", suffixName3)
        val suffixName4 = FileUtils.getSuffix(".test2.jpg")
        Assert.assertEquals(".jpg", suffixName4)
        val suffixName5 = FileUtils.getSuffix(".mp4")
        Assert.assertEquals("", suffixName5)
    }

    @Test
    fun should_string_getFileNameFromFullPath() {
        val fileName = FileUtils.getFileNameFromFullPath("path" + File.separator + "1.mp3")
        Assert.assertNotNull(fileName)
        Assert.assertEquals("1.mp3", fileName)
    }

    @Test
    fun should_returnEmpty_when_deleteFileList_different_input() {
        val list: MutableList<String> = java.util.ArrayList()
        var arrayList: ArrayList<String?> = FileUtils.deleteFileList(list)
        Assert.assertTrue(arrayList.isEmpty())
        val arrayList1: ArrayList<String> = FileUtils.deleteFileList(null)
        Assert.assertTrue(arrayList1.isEmpty())
        val file = File(TEST_PATH)
        val fileA = File(TEST_PATH_A)
        val fileB = File(TEST_PATH_B_TXT)
        file.mkdirs()
        fileA.mkdirs()
        try {
            fileB.createNewFile()
        } catch (e: IOException) {
        }
        list.add(TEST_PATH_A)
        list.add(TEST_PATH_B_TXT)
        arrayList = FileUtils.deleteFileList(list)
        Assert.assertFalse(arrayList.isEmpty())
        Assert.assertTrue(arrayList.contains(TEST_PATH_B_TXT))
        deleteAllFiles(file)
        file.delete()
    }

    @Test
    fun should_createCopyFile_when_copyFile_string() {
        val folder = File(TEST_PATH)
        folder.mkdirs()
        val file = File(TEST_PATH_B_TXT)
        try {
            file.createNewFile()
        } catch (e: IOException) {
        }
        val txtData = "test"
        writeToFile(file, txtData)
        Assert.assertNull(FileUtils.copyFile(null, TEST_PATH_A, A_txt))
//        Assert.assertNull(FileUtils.copyFile(file, TEST_PATH_A, ""))
        val result = FileUtils.copyFile(file, TEST_PATH_A, A_txt)
        Assert.assertTrue(result.exists())
        Assert.assertTrue(result.isFile)
        var copyFiletext: String? = null
        try {
            copyFiletext = String(Files.readAllBytes(Paths.get(result.path)))
        } catch (e: IOException) {
        }
        Assert.assertEquals(txtData, copyFiletext)
        deleteAllFiles(folder)
        folder.delete()
    }

    private fun writeToFile(file: File, data: String) {
        val sourceByte = data.toByteArray(Constants.UTF_8)
        if (null != sourceByte) {
            var outStream: FileOutputStream? = null
            try {
                outStream = FileOutputStream(file)
            } catch (e: FileNotFoundException) {
            }
            try {
                outStream!!.write(sourceByte)
            } catch (e: IOException) {
            }
            try {
                outStream!!.close()
            } catch (e: IOException) {
            }
        }
    }

    private fun deleteAllFiles(root: File) {
        val files = root.listFiles()
        if (files != null) {
            for (f in files) {
                if (f.isDirectory) {
                    deleteAllFiles(f)
                    try {
                        f.delete()
                    } catch (e: Exception) {
                    }
                } else {
                    if (f.exists()) {
                        deleteAllFiles(f)
                        try {
                            f.delete()
                        } catch (e: Exception) {
                        }
                    }
                }
            }
        }
    }
}