package com.soundrecorder.common.buryingpoint

import android.content.Context
import android.os.Build
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.oplus.statistics.OplusTrack
import com.soundrecorder.common.shadows.ShadowFeatureOption
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.ArgumentMatchers.*
import org.mockito.Mockito
import org.mockito.internal.verification.Times
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowFeatureOption::class])
class RecorderUserActionTest {

    @Test
    fun check_o_plus_track() {
        val context: Context = ApplicationProvider.getApplicationContext()
        val map = mapOf<String, String>()
        val mockedStatic = Mockito.mockStatic(OplusTrack::class.java)
        RecorderUserAction.addCommonUserAction(context, 1, 1, map)
        RecorderUserAction.addCommonUserAction(context, 1, 1, map, true)
        RecorderUserAction.addCommonUserAction(context, 1, 1, map, false)
        RecorderUserAction.addNewCommonUserAction(context, 1, "1", map, true)
        RecorderUserAction.addNewCommonUserAction(context, 1, "1", map, false)
        RecorderUserAction.addNewCommonUserAction(context, "1", "1", map, true)
        RecorderUserAction.addNewCommonUserAction(context, "1", "1", map, false)
        mockedStatic.verify({
            OplusTrack.onCommon(
                any(),
                anyString(),
                anyString(),
                anyMap()
            )
        }, Times(7))
        mockedStatic.close()
    }
}