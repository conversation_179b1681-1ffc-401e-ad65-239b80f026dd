/*********************************************************************
 * * Copyright (C), 2022, OPlus. All rights reserved.
 * * VENDOR_EDIT
 * * File        :  -
 * * Version     : 1.0
 * * Date        : 2022/4/26
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.common.widget;

import static org.mockito.Mockito.verify;

import android.content.Context;
import android.os.Build;
import android.view.MotionEvent;
import android.view.View;

import androidx.test.core.app.ApplicationProvider;
import androidx.test.ext.junit.runners.AndroidJUnit4;

import com.coui.appcompat.pressfeedback.COUIPressFeedbackHelper;
import com.soundrecorder.common.shadows.ShadowFeatureOption;
import com.soundrecorder.common.shadows.ShadowOS12FeatureUtil;

import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.powermock.reflect.Whitebox;
import org.robolectric.annotation.Config;

import java.util.ArrayList;

@RunWith(AndroidJUnit4.class)
@Config(sdk = Build.VERSION_CODES.S, shadows = {ShadowOS12FeatureUtil.class, ShadowFeatureOption.class})
public class ClickScaleCardViewTest {

    private Context mContext;

    @Before
    public void setUp() {
        mContext = ApplicationProvider.getApplicationContext();
    }

    @After
    public void tearDown() {
        mContext = null;
    }

    @Test
    public void should_return_not_null_when_getFeedbackHelperNotNull() throws Exception {
        ClickScaleCardView cardView = new ClickScaleCardView(mContext);
        COUIPressFeedbackHelper helper = Whitebox.invokeMethod(cardView, "getFeedbackHelperNotNull");
        Assert.assertNotNull(helper);
    }

    @Test
    public void verify_result_when_dispatchTouchEvent() {
        ClickScaleCardView cardView = new ClickScaleCardView(mContext);
        cardView.dispatchTouchEvent(MotionEvent.obtain(200, 300, MotionEvent.ACTION_DOWN, 15f, 16f, 0));
        Assert.assertTrue(Whitebox.getInternalState(cardView, "canUpAnimate"));
        Whitebox.setInternalState(cardView, "canUpAnimate", true);
        boolean result = cardView.dispatchTouchEvent(MotionEvent.obtain(200, 300, MotionEvent.ACTION_UP, 15f, 16f, 0));
        Assert.assertFalse(result);
    }

    @Test
    public void should_return_not_null_when_isTouchPointView() throws Exception {
        ClickScaleCardView cardView = Mockito.mock(ClickScaleCardView.class);
        boolean result = Whitebox.invokeMethod(cardView, "touchOnClickableView", (MotionEvent)null);
        Assert.assertFalse(result);
        ArrayList<String> excludeTagList = new ArrayList<>();
        excludeTagList.add("abc");
        Whitebox.setInternalState(cardView, "excludeTagLists", excludeTagList);
        Mockito.when(cardView.findViewWithTag("abc")).thenReturn(new View(mContext));
        result = Whitebox.invokeMethod(cardView, "touchOnClickableView", MotionEvent.obtain(200, 300, MotionEvent.ACTION_DOWN, 15f, 16f, 0));
        Assert.assertFalse(result);
    }

    @Test
    public void should_return_not_null_when_touchOnClickableView() throws Exception {
        ClickScaleCardView cardView = Mockito.mock(ClickScaleCardView.class);
        View mockView = new View(mContext);
        boolean result = Whitebox.invokeMethod(cardView, "isTouchPointView", mockView, 0f, 0f);
        Assert.assertTrue(result);
    }

    @Test
    public void should_return_null_when_onRelease() {
        ClickScaleCardView cardView = Mockito.mock(ClickScaleCardView.class);
        cardView.onRelease();
        verify(cardView, Mockito.times(1)).onRelease();
        Assert.assertNotNull(cardView);
    }
}
