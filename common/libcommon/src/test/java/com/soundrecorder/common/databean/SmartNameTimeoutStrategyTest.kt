/*********************************************************************************
 * Copyright (C), 2008-2025, Oplus, All rights reserved.
 *
 * File: - SmartNameTimeoutStrategyTest.kt
 * Description:
 * The test cases for com.soundrecorder.common.databean.SmartNameTimeoutStrategy
 *
 * Version: 1.0
 * Date: 2025-05-21
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * (author)                        (date)    (version)    (desc)
 * ------------------------------------------------------------------------------
 * <PERSON><PERSON><PERSON><PERSON>.<EMAIL>    2025-05-21   1.0    Create this module
 *********************************************************************************/
package com.soundrecorder.common.databean

import org.junit.Assert
import org.junit.Test

class SmartNameTimeoutStrategyTest : Assert() {

    @Test
    fun `should as expected when calculateGenerateTimeout with specific txt size`() {
        // Given
        val testCases = mapOf(
            2000 to 7000L,
            2999 to 7000L,
            3000 to 9000L,
            5000 to 9000L,
            9999 to 9000L,
            10000 to 13000L,
            20000 to 15000L,
            30000 to 16000L
        )

        testCases.forEach { (inputSize, expected) ->
            // When
            val result = SmartNameTimeoutStrategy.calculateGenerateTimeout(inputSize)

            // Then
            assertEquals(expected, result)
        }
    }
}