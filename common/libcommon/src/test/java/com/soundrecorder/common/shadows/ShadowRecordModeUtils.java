/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  ShadowRecordModeUtil
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/8/23
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.common.shadows;

import android.content.Context;
import com.soundrecorder.base.utils.BaseUtil;
import org.robolectric.annotation.Implementation;
import org.robolectric.annotation.Implements;

@Implements(BaseUtil.class)
public class ShadowRecordModeUtils {

    @Implementation
    public static boolean isSupportMultiRecordMode(Context context) {
        if (null != context) {
            return true;
        }
        return false;
    }
}
