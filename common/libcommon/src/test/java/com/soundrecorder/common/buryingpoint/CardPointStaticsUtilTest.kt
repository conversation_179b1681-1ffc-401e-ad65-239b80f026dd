/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: CardPointStaticsUtilTest
 Description:
 Version: 1.0
 Date: 2022/10/19
 Author: W9013204
 -----------Revision History-----------
 <author> <date> <version> <desc>
 W9013204 2022/10/19 1.0 create
 */

package com.soundrecorder.common.buryingpoint

import android.content.Context
import android.os.Build
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.common.shadows.ShadowFeatureOption
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.ArgumentMatchers
import org.mockito.MockedStatic
import org.mockito.Mockito
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowFeatureOption::class])
class CardPointStaticsUtilTest {
    private var mockedStatic: MockedStatic<RecorderUserAction>? = null
    private var mockedBaseApplication: MockedStatic<BaseApplication>? = null
    private var mContext: Context? = null

    @Before
    @Throws(Exception::class)
    fun setUp() {
        mockedStatic = Mockito.mockStatic(RecorderUserAction::class.java)
        mContext = ApplicationProvider.getApplicationContext()
        mockedBaseApplication = Mockito.mockStatic(BaseApplication::class.java)
        mockedBaseApplication!!.`when`<Context> { BaseApplication.getAppContext() }?.thenReturn(mContext)
    }

    @After
    @Throws(Exception::class)
    fun tearDown() {
        mockedStatic?.close()
        mockedStatic = null
        mockedBaseApplication?.close()
        mockedBaseApplication = null
        mContext = null
    }

    @Test
    fun dad() {
        CardPointStaticsUtil.addSmallCardEvent()
        mockedStatic?.verify({
            RecorderUserAction.addNewCommonUserAction(
                ArgumentMatchers.any(),
                ArgumentMatchers.anyString(),
                ArgumentMatchers.anyString(),
                ArgumentMatchers.anyMap<String, Boolean>(),
                ArgumentMatchers.anyBoolean()
            )
        }, Mockito.times(1))
    }

    @Test
    fun remove() {
        CardPointStaticsUtil.removeSmallCardEvent()
        mockedStatic?.verify({
            RecorderUserAction.addNewCommonUserAction(
                ArgumentMatchers.any(),
                ArgumentMatchers.anyString(),
                ArgumentMatchers.anyString(),
                ArgumentMatchers.anyMap<String, Boolean>(),
                ArgumentMatchers.anyBoolean()
            )
        }, Mockito.times(1))
    }
}