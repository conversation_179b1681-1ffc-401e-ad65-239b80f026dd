/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  UploadDbUtilTest
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/6/23
 * * Author      : W9013204
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.common.db

import android.content.Context
import android.os.Build
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.OS12FeatureUtil
import com.soundrecorder.common.databean.UploadRecord
import com.soundrecorder.common.shadows.ShadowFeatureOption
import com.soundrecorder.common.shadows.ShadowOS12FeatureUtil
import com.soundrecorder.common.utils.ConvertDbUtil
import com.soundrecorder.common.utils.FunctionOption
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.MockedStatic
import org.mockito.Mockito
import org.robolectric.annotation.Config
import org.robolectric.shadows.ShadowLog

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowLog::class, ShadowOS12FeatureUtil::class, ShadowFeatureOption::class])
class UploadDbUtilTest {
    private var mContext: Context? = null
    private var mMockBaseApplication: MockedStatic<BaseApplication>? = null

    @Before
    fun setUp() {
        mContext = Mockito.spy(ApplicationProvider.getApplicationContext<Context>())

        mMockBaseApplication = Mockito.mockStatic(BaseApplication::class.java)
        mMockBaseApplication?.`when`<Any> { BaseApplication.getAppContext() }?.thenReturn(mContext)

        Mockito.`when`(mContext!!.applicationContext).thenReturn(mContext)
    }

    @After
    fun tearDown() {
        mContext = null
        mMockBaseApplication?.close()
        mMockBaseApplication = null
    }

    @Test
    fun should_correct_when_insertUploadRecords() {
        val uploadRecords = ArrayList<UploadRecord>()
        val size = UploadDbUtil.insertUploadRecords(mContext, uploadRecords)
        Assert.assertEquals(size, 0)
    }

    @Test
    fun should_correct_when_getUploadRecordsByonlyId() {
        val list = UploadDbUtil.getUploadRecordsByonlyId(mContext, "1")
        Assert.assertNotNull(list)
    }

    @Test
    fun should_correct_when_updateEtagByOnlyIdAndSeqNum() {
        val list = UploadDbUtil.updateEtagByOnlyIdAndSeqNum(mContext, "1", 1, "2")
        Assert.assertNotNull(list)
    }

    @Test
    fun should_correct_when_deleteByOnlyId() {
        val list = UploadDbUtil.deleteByOnlyId(mContext, "1")
        Assert.assertNotNull(list)
    }

    @Test
    fun should_correct_when_getAllUploadRecords() {
        val list = UploadDbUtil.getAllUploadRecords(mContext)
        Assert.assertNotNull(list)
    }

    @Test
    fun should_check_service_plan_code() {
        val mockedStatic = Mockito.mockStatic(OS12FeatureUtil::class.java)
        mockedStatic.`when`<Boolean> { OS12FeatureUtil.isFindX4() }.thenReturn(true)
        Assert.assertTrue(OS12FeatureUtil.isFindX4())

        ConvertDbUtil.setConvertServicePlanCode(ConvertDbUtil.SERVER_PLAN_XUNFEI)
        Assert.assertTrue(FunctionOption.hasSupportXunFei())

        ConvertDbUtil.setConvertServicePlanCode(ConvertDbUtil.SERVER_PLAN_BEIYAN)
        Assert.assertTrue(FunctionOption.hasSupportXunFei())

        mockedStatic.close()
        ConvertDbUtil.setConvertServicePlanCode(ConvertDbUtil.SERVER_PLAN_XUNFEI)
        Assert.assertTrue(FunctionOption.hasSupportXunFei())
    }
}