/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: ContactCallRecordUtilTest
 * Description:
 * Version: 1.0
 * Date: 2024/3/18
 * Author: ********(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * ******** 2024/3/18 1.0 create
 */

package com.soundrecorder.common.utils.calling

import android.content.ContentProviderClient
import android.content.ContentResolver
import android.content.Context
import android.os.Build
import android.os.Bundle
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.common.databean.NoteData
import com.soundrecorder.common.shadows.ShadowFeatureOption
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.ArgumentMatchers.any
import org.mockito.ArgumentMatchers.anyString
import org.mockito.Mockito
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowFeatureOption::class])
class ContactCallRecordUtilTest {

    var context: Context? = null

    @Before
    fun setUp() {
        context = ApplicationProvider.getApplicationContext()
    }

    @After
    fun release() {
        context = null
    }

    @Test
    fun should_equals_when_queryCallRecordingUUID() {
        val context = Mockito.mock(Context::class.java)
        val mockContentResolver = Mockito.mock(ContentResolver::class.java)
        val mockContentProviderClient = Mockito.mock(ContentProviderClient::class.java)

        Mockito.`when`(mockContentResolver?.acquireUnstableContentProviderClient(anyString())).thenReturn(mockContentProviderClient)
        Mockito.`when`(context?.contentResolver).thenReturn(mockContentResolver)
        val resultBundle = Mockito.mock(Bundle::class.java)
        Mockito.`when`(resultBundle?.getString(anyString())).thenReturn(null, "1")
        Mockito.`when`(
            mockContentProviderClient?.call(
                Mockito.anyString(), any(), any())).thenReturn(resultBundle)
        Assert.assertNull(ContactCallRecordUtil.queryCallRecordingUUID(context, ""))
        Assert.assertEquals("1", ContactCallRecordUtil.queryCallRecordingUUID(context, ""))

        Mockito.`when`(resultBundle?.getString(anyString())).thenThrow(NullPointerException())
        Assert.assertNull(ContactCallRecordUtil.queryCallRecordingUUID(context, ""))
    }

    @Test
    fun should_equals_when_setCallRecordingFlag() {
        val context = Mockito.mock(Context::class.java)
        val mockContentResolver = Mockito.mock(ContentResolver::class.java)
        val mockContentProviderClient = Mockito.mock(ContentProviderClient::class.java)

        Mockito.`when`(mockContentResolver?.acquireUnstableContentProviderClient(anyString())).thenReturn(mockContentProviderClient)
        Mockito.`when`(context?.contentResolver).thenReturn(mockContentResolver)
        val resultBundle = Mockito.mock(Bundle::class.java)
        Mockito.`when`(resultBundle?.getBoolean(anyString())).thenReturn(true).thenThrow(NullPointerException())
        Mockito.`when`(
            mockContentProviderClient?.call(
                Mockito.anyString(), any(), any())).thenReturn(resultBundle)
        Assert.assertTrue(ContactCallRecordUtil.setCallRecordingFlag(context, "", true))
        Assert.assertFalse(ContactCallRecordUtil.setCallRecordingFlag(context, "", true))
    }

    @Test
    fun should_correct_when_clearCallRecordFlag() {
        val context = context ?: return
        val note = Mockito.mock(NoteData::class.java)
        ContactCallRecordUtil.clearCallRecordFlag(context, null)
        ContactCallRecordUtil.clearCallRecordFlag(context, listOf(note))
    }
}