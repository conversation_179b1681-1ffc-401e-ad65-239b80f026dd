/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: NoteDbUtilsTest
 * Description:
 * Version: 1.0
 * Date: 2024/3/29
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2024/3/29 1.0 create
 */

package com.soundrecorder.common.db

import android.content.ContentResolver
import android.content.Context
import android.database.Cursor
import android.database.sqlite.SQLiteDatabase
import android.net.Uri
import android.os.Build
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.common.databean.NoteData
import com.soundrecorder.common.shadows.ShadowBaseUtils
import com.soundrecorder.common.shadows.ShadowFeatureOption
import com.soundrecorder.common.shadows.ShadowOS12FeatureUtil
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.ArgumentMatchers.any
import org.mockito.ArgumentMatchers.anyInt
import org.mockito.ArgumentMatchers.anyString
import org.mockito.MockedStatic
import org.mockito.Mockito
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowOS12FeatureUtil::class, ShadowBaseUtils::class, ShadowFeatureOption::class])
class NoteDbUtilsTest {
    private var context: Context? = null
    private var mockBaseApplication: MockedStatic<BaseApplication>? = null
    private val summaryVersion = 11

    @Before
    fun setUp() {
        context = Mockito.spy(ApplicationProvider.getApplicationContext<Context>())
        mockBaseApplication = Mockito.mockStatic(BaseApplication::class.java)
        mockBaseApplication?.`when`<Any> { BaseApplication.getAppContext() }?.thenReturn(context)
        Mockito.`when`(context!!.applicationContext).thenReturn(context)
    }

    @After
    fun tearDown() {
        context = null
        mockBaseApplication?.close()
        mockBaseApplication = null
    }

    @Test
    fun should_correct_when_createNoteTable() {
        val db = Mockito.mock(SQLiteDatabase::class.java)
        NoteDbUtils.createNoteTable(db)
        Mockito.verify(db, Mockito.times(1)).execSQL(anyString())
    }

    @Test
    fun should_correct_when_dropNoteTable() {
        val db = Mockito.mock(SQLiteDatabase::class.java)
        NoteDbUtils.dropNoteTable(db)
        Mockito.verify(db, Mockito.times(1)).execSQL(anyString())
    }

    @Test
    fun should_correct_when_upgradeNoteTable() {
        val db = Mockito.mock(SQLiteDatabase::class.java)
        NoteDbUtils.upgradeNoteTable(db, summaryVersion, summaryVersion)
        NoteDbUtils.upgradeNoteTable(db, 9, summaryVersion)
        Mockito.verify(db, Mockito.times(1)).execSQL(anyString())
    }

    @Test
    fun should_correct_when_downgradeNoteTable() {
        val db = Mockito.mock(SQLiteDatabase::class.java)
        NoteDbUtils.upgradeNoteTable(db, summaryVersion, summaryVersion)
        NoteDbUtils.downgradeNoteTable(db, summaryVersion, summaryVersion - 1)
        Mockito.verify(db, Mockito.times(1)).execSQL(anyString())
    }

    @Test
    fun should_correct_when_addNote() {
        Assert.assertTrue(NoteDbUtils.addNote(getDefaultNoteData()))
    }

    @Test
    fun should_correct_when_queryNoteByUUID() {
        val mockContentResolver = Mockito.mock(ContentResolver::class.java)
        val mockCursor = Mockito.mock(Cursor::class.java)
        Mockito.`when`(context?.contentResolver)?.thenReturn(mockContentResolver)
        Mockito.`when`(mockContentResolver.query(any(Uri::class.java), any(), any(), any(), any())).thenReturn(mockCursor)
        Mockito.`when`(mockCursor.moveToFirst())?.thenReturn(true)
        Mockito.`when`(mockCursor.getColumnIndexOrThrow(anyString()))?.thenReturn(1)
        Mockito.`when`(mockCursor.getString(anyInt()))?.thenReturn("11")
        Mockito.`when`(mockCursor.getInt(anyInt()))?.thenReturn(1)

        Assert.assertNotNull(NoteDbUtils.queryNoteByUUID("1"))
        Assert.assertNotNull(NoteDbUtils.queryNoteByMediaId(""))

        Mockito.`when`(mockContentResolver.query(any(Uri::class.java), any(), any(), any(), any())).thenThrow(NullPointerException())
        Assert.assertNull(NoteDbUtils.queryNoteByMediaId("2"))
    }

    @Test
    fun should_correct_when_queryAllNotes() {
        val mockContentResolver = Mockito.mock(ContentResolver::class.java)
        val mockCursor = Mockito.mock(Cursor::class.java)
        Mockito.`when`(context?.contentResolver)?.thenReturn(mockContentResolver)
        Mockito.`when`(mockContentResolver.query(any(Uri::class.java), any(), any(), any(), any())).thenReturn(mockCursor)
        Mockito.`when`(mockCursor.moveToFirst())?.thenReturn(true)
        Mockito.`when`(mockCursor.moveToNext())?.thenReturn(true, false)
        Mockito.`when`(mockCursor.getColumnIndexOrThrow(anyString()))?.thenReturn(1)
        Mockito.`when`(mockCursor.getString(anyInt()))?.thenReturn("11")
        Mockito.`when`(mockCursor.getInt(anyInt()))?.thenReturn(1)
        Assert.assertNotNull(NoteDbUtils.queryAllNotes())
    }

    @Test
    fun should_correct_when_updateNote() {
        val mockContentResolver = Mockito.mock(ContentResolver::class.java)
        Mockito.`when`(context?.contentResolver)?.thenReturn(mockContentResolver)
        Mockito.`when`(mockContentResolver.update(any(Uri::class.java), any(), any(), any())).thenReturn(1).thenThrow(NullPointerException())
        Assert.assertEquals(1, NoteDbUtils.updateNoteByMediaId(getDefaultNoteData()))
        Assert.assertEquals(0, NoteDbUtils.updateNoteByMediaId(getDefaultNoteData()))
    }

    @Test
    fun should_correct_when_deleteNoteByMediaId() {
        val mockContentResolver = Mockito.mock(ContentResolver::class.java)
        Mockito.`when`(context?.contentResolver)?.thenReturn(mockContentResolver)
        Mockito.`when`(mockContentResolver.delete(any(Uri::class.java), any(), any())).thenReturn(1).thenThrow(NullPointerException())
        Assert.assertEquals(1, NoteDbUtils.deleteNoteByMediaId(""))
        Assert.assertEquals(0, NoteDbUtils.deleteNoteByMediaId(""))
    }

    @Test
    fun should_correct_when_deleteNotesByNoteId() {
        Assert.assertEquals(0, NoteDbUtils.deleteNotesByNoteId(listOf()))

        val mockContentResolver = Mockito.mock(ContentResolver::class.java)
        Mockito.`when`(context?.contentResolver)?.thenReturn(mockContentResolver)
        Mockito.`when`(mockContentResolver.delete(any(Uri::class.java), any(), any())).thenReturn(1).thenThrow(NullPointerException())
        Assert.assertEquals(1, NoteDbUtils.deleteNotesByNoteId(listOf("")))
        Assert.assertEquals(0, NoteDbUtils.deleteNotesByNoteId(listOf("")))
    }

    @Test
    fun should_correct_when_updateOrInsertNote() {
        Assert.assertEquals(10, NoteDbUtils.updateOrInsertNote(getDefaultNoteData()))
    }

    private fun getDefaultNoteData(): NoteData {
        return NoteData("11", 1, "4a", "", "1", "standarad/1.mp3", "")
    }
}