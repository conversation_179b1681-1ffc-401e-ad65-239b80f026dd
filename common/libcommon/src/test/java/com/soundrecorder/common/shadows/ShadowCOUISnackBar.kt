/*
Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
File: - null.java
Description: COUISnackBar shadow.
Version: 1.0
Date : 2024/1/19
Author: W9012748
--------------------- Revision History: ---------------------
<author>	<data> 	  <version >	   <desc>
W9012748  2024/1/19     1.0      create this file
*/
package com.soundrecorder.common.shadows

import android.content.Context
import android.view.View
import com.coui.appcompat.snackbar.COUISnackBar
import org.robolectric.annotation.Implementation
import org.robolectric.annotation.Implements
import org.robolectric.shadows.ShadowViewGroup

@Implements(COUISnackBar::class)
class ShadowCOUISnackBar : ShadowViewGroup() {

    @Implementation
    @Suppress("FunctionOnlyReturningConstant")
    fun isInSecondaryDisplay(context: Context): <PERSON><PERSON>an {
        return false
    }

    @Implementation
    @Suppress("EmptyFunctionBlock")
    fun setContentText(contentText: String?) {
    }

    @Implementation
    @Suppress("EmptyFunctionBlock")
    fun setOnAction(actionText: String?, listener: View.OnClickListener?) {
    }

    @Implementation
    @Suppress("EmptyFunctionBlock")
    fun show() {
    }
}
