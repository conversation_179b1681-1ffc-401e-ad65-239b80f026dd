/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: DeleteSoundPoolPlayerTest
 * Description:
 * Version: 1.0
 * Date: 2023/6/25
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2023/6/25 1.0 create
 */

package com.soundrecorder.common.utils.sound

import android.media.SoundPool
import android.os.Build
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.common.shadows.ShadowFeatureOption
import org.junit.Assert
import org.junit.Test
import org.junit.runner.RunWith
import org.powermock.reflect.Whitebox
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowFeatureOption::class])
class DeleteSoundPoolPlayerTest {

    @Test
    fun should_notNUll_when_initData() {
        val player = DeleteSoundPoolPlayer()
        player.initData()
        val soundPool = Whitebox.getInternalState<SoundPool>(player, "mSoundPool")
        Assert.assertNotNull(soundPool)
    }

    @Test
    fun should_when_release() {
        val player = DeleteSoundPoolPlayer()
        player.initData()
        var soundPool = Whitebox.getInternalState<SoundPool>(player, "mSoundPool")
        Assert.assertNotNull(soundPool)

        player.release()
        soundPool = Whitebox.getInternalState(player, "mSoundPool")
        Assert.assertNull(soundPool)
    }

    @Test
    fun should_when_playDeleteSound() {
        val player = DeleteSoundPoolPlayer()
        player.playDeleteSound()
        val soundPool = Whitebox.getInternalState<SoundPool>(player, "mSoundPool")
        Assert.assertNotNull(soundPool)
    }
}