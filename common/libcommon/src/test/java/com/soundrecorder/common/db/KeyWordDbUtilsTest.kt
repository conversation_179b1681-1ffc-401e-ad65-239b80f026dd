/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  KeyWordDbUtilsText
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/11/29
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.common.db

import android.content.ContentResolver
import android.content.Context
import android.os.Build
import android.util.Pair
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.common.databean.KeyWord
import com.soundrecorder.common.shadows.ShadowFeatureOption
import com.soundrecorder.common.shadows.ShadowOS12FeatureUtil
import com.soundrecorder.common.shadows.ShadowOplusUsbEnvironment
import org.junit.*
import org.junit.runner.RunWith
import org.mockito.MockedStatic
import org.mockito.Mockito
import org.robolectric.annotation.Config
import org.robolectric.shadows.ShadowLog

@RunWith(AndroidJUnit4::class)
@Config(
    sdk = [Build.VERSION_CODES.S],
    shadows = [ShadowLog::class,
        ShadowOplusUsbEnvironment::class,
        ShadowOS12FeatureUtil::class,
        ShadowFeatureOption::class
    ]
)
@Ignore
class KeyWordDbUtilsTest {

    private var mContext: Context? = null
    private var mMockBaseApplication: MockedStatic<BaseApplication>? = null
    private val recordId = 1L

    @Before
    fun setUp() {
        mContext = Mockito.spy(ApplicationProvider.getApplicationContext())

        mMockBaseApplication = Mockito.mockStatic(BaseApplication::class.java)
        mMockBaseApplication?.`when`<Context> { BaseApplication.getAppContext() }?.thenReturn(mContext)
        val contentResolver = Mockito.mock(ContentResolver::class.java)
        mMockBaseApplication?.`when`<ContentResolver> { BaseApplication.getAppContext().contentResolver }
            ?.thenReturn(contentResolver)

        Mockito.`when`(mContext!!.applicationContext).thenReturn(mContext)

        RecorderDatabaseHelper(mContext).writableDatabase.also {
            KeyWordDbUtils.createKeyWordTable(it)
        }
    }

    @After
    fun tearDown() {
        RecorderDatabaseHelper(mContext).writableDatabase.also {
            KeyWordDbUtils.dropKeyWordTable(it)
            it.close()
        }
        mContext = null
        mMockBaseApplication?.close()
        mMockBaseApplication = null
    }

    @Test
    fun should_return_void_when_addKeyWord() {
        val keyWord = KeyWord("测试", 0.5f)
        KeyWordDbUtils.addKeyWord(keyWord, recordId)

        val list = KeyWordDbUtils.queryKeyWords(recordId)
        println("addKeyWord list:$list")
        Assert.assertEquals(1, list.size)
    }

    private fun randomInsertKeyWords(count: Int, recordId: Long) {
        val keyWords = mutableListOf<KeyWord>()
        for (i in 0 until count) {
            keyWords.add(KeyWord("测试$i", 0.5f))
        }
        KeyWordDbUtils.addKeyWords(keyWords, recordId)
    }

    @Test
    fun should_return_void_when_addKeyWords() {
        val keyWords = mutableListOf<KeyWord>()
        KeyWordDbUtils.addKeyWords(keyWords, recordId)
        var queryResult = KeyWordDbUtils.queryKeyWords(recordId)
        println("addKeyWords list:$queryResult")
        Assert.assertEquals(0, queryResult.size)

        keyWords.add(KeyWord("测试1", 0.5f))
        keyWords.add(KeyWord("测试2", 0.5f))
        keyWords.add(KeyWord("测试3", 0.5f))
        keyWords.add(KeyWord("测试4", 0.5f))

        KeyWordDbUtils.addKeyWords(keyWords, recordId)
        queryResult = KeyWordDbUtils.queryKeyWords(recordId)
        println("addKeyWords list:$queryResult")
        Assert.assertEquals(4, queryResult.size)
    }

    @Test
    fun should_return_when_deleteKeyWords() {
        var deleteCount = KeyWordDbUtils.deleteKeyWords(recordId)
        println("deleteKeyWords count:$deleteCount")
        Assert.assertEquals(0, deleteCount)

        randomInsertKeyWords(4, recordId)
        deleteCount = KeyWordDbUtils.deleteKeyWords(recordId)
        println("deleteKeyWords count:$deleteCount")
        Assert.assertEquals(4, deleteCount)
    }

    @Test
    fun should_return_when_deleteKeyWord() {
        var deleteCount = KeyWordDbUtils.deleteKeyWord(0)
        println("deleteKeyWords count:$deleteCount")
        Assert.assertEquals(0, deleteCount)

        randomInsertKeyWords(4, recordId)
        var queryList = KeyWordDbUtils.queryKeyWords(recordId)
        println("deleteKeyWords query list:$queryList")
        val size = queryList.size
        if (size > 0) {
            val id = queryList[0].id ?: return
            deleteCount = KeyWordDbUtils.deleteKeyWord(id)
            println("deleteKeyWords count:$deleteCount")
            Assert.assertEquals(1, deleteCount)

            queryList = KeyWordDbUtils.queryKeyWords(recordId)
            println("deleteKeyWords query list:$queryList")
            Assert.assertEquals(queryList.size, size - 1)
        }
    }

    @Test
    fun should_return_list_when_queryKeyWords() {
        var list = KeyWordDbUtils.queryKeyWords(recordId)
        Assert.assertEquals(0, list.size)

        randomInsertKeyWords(4, recordId)
        list = KeyWordDbUtils.queryKeyWords(recordId)
        Assert.assertEquals(4, list.size)
    }

    @Test
    fun should_return_list_when_queryAllKeyWords() {
        var list = KeyWordDbUtils.queryAllKeyWords()
        Assert.assertEquals(0, list.size)

        randomInsertKeyWords(4, recordId)
        list = KeyWordDbUtils.queryAllKeyWords()
        Assert.assertEquals(4, list.size)
    }

    @Test
    fun should_return_int_when_updateRecordId() {
        randomInsertKeyWords(4, recordId)
        val count = KeyWordDbUtils.updateRecordId(recordId, 2)
        Assert.assertEquals(4, count)

        val list = KeyWordDbUtils.queryKeyWords(2)
        Assert.assertEquals(4, list.size)

        KeyWordDbUtils.deleteKeyWords(2)
    }

    @Test
    fun should_return_int_when_batchUpdateRecordId() {
        randomInsertKeyWords(2, recordId)
        randomInsertKeyWords(3, 2)
        randomInsertKeyWords(4, 3)

        val updateList: ArrayList<Pair<Long, Long>> = ArrayList<Pair<Long, Long>>()
        var count = KeyWordDbUtils.batchUpdateRecordId(updateList)
        Assert.assertEquals(0, count)

        updateList.add(Pair(2, 5))
        count = KeyWordDbUtils.batchUpdateRecordId(updateList)
        println("batchUpdateRecordId count:$count")
        Assert.assertEquals(1, count)

        var list = KeyWordDbUtils.queryKeyWords(2)
        Assert.assertEquals(0, list.size)

        list = KeyWordDbUtils.queryKeyWords(5)
        Assert.assertEquals(3, list.size)

        KeyWordDbUtils.deleteKeyWords(3)
        KeyWordDbUtils.deleteKeyWords(5)
    }
}