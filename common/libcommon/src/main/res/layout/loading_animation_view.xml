<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/color_load_view"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <TextView
        android:id="@+id/loadingTip"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:layout_centerVertical="true"
        android:textColor="@color/percent_30_black"
        android:textSize="@dimen/sp14"
        android:text="@string/oplus_loading_dialog_text_view" />

    <com.oplus.anim.EffectiveAnimationView
        android:id="@+id/loadingView"
        app:anim_loop="true"
        android:layout_width="@dimen/default_height"
        android:layout_height="@dimen/default_height"
        android:layout_above="@id/loadingTip"
        android:layout_centerHorizontal="true"
        android:layout_marginBottom="16dp" />
</RelativeLayout>