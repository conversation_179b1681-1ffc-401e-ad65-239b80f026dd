/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: OnRealtimeSubtitleUpdateListener
 * Description:
 * Version: 1.0
 * Date: 2025/3/6
 * Author: W9085798
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9085798 2025/5/18 1.0 create
 */
package com.soundrecorder.common.realtimeasr

import androidx.annotation.WorkerThread

/**
 * 实时ASR回调接口
 *
 * 注意：所有回调方法都在后台线程中执行，不是主线程。
 * 如需更新UI，请确保切换到主线程。
 */
interface OnRealtimeListener {
    /**
     * 通知数据有更新
     *
     * 注意：此方法在后台线程中调用，如需更新UI，请切换到主线程
     *
     * @param cache 字幕缓存对象
     */
    @WorkerThread
    fun onSubtitleUpdated(cache: IRealtimeSubtitleCache)

    /**
     * ASR异常
     *
     * 注意：此方法在后台线程中调用，如需更新UI，请切换到主线程
     *
     * @param code 状态码
     */
    @WorkerThread
    fun onAsrStatus(code: Int)

    /**
     * 获取支持语言的错误回调
     *
     * 注意：此方法在后台线程中调用，如需更新UI，请切换到主线程
     *
     * @param errorCode 错误码
     * @param errorMsg 错误信息
     */
    @WorkerThread
    fun onTranslationCfgError(errorCode: Int, errorMsg: String?)

    /**
     * 获取支持语言的成功回调
     *
     * 注意：此方法在后台线程中调用，如需更新UI，请切换到主线程
     *
     * @param data 支持的语言映射
     */
    @WorkerThread
    fun onTranslationCfgSuccess(data: Map<String, String>)
}