/*********************************************************************
 * * Copyright (C), 2023, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  CallGroupModel.kt
 * * Description :
 * * Version     : 1.0
 * * Date        : 2024/8/20
 * * Author      : W9035969
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.common.databean

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

@Parcelize
data class CallGroupModel(
    var callerName: String? = null,
    var avatarColor: String? = null,
    var groupInfo: GroupInfo? = null
) : Parcelable