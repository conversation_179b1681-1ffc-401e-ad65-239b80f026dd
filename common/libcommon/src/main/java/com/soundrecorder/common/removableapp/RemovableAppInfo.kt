/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : RemovableAppInfo
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/12/30 10:13
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2022/12/30       1.0      create
 ***********************************************************************/
package com.soundrecorder.common.removableapp

data class RemovableAppInfo(
    val packageName: String,
    var label: String = "",
    var icon: ByteArray? = null
) {

    override fun toString(): String {
        return super.toString()
    }

    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as RemovableAppInfo

        if (packageName != other.packageName) return false

        return true
    }

    override fun hashCode(): Int {
        return packageName.hashCode()
    }
}
