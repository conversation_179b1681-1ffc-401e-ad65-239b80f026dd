/***********************************************************
 * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 * File: AudioNameUtils.java
 * Description:
 * Version: 1.0
 * Date : 2024/7/18
 * Author: <EMAIL>
 * -------------------Revision History: --------------------
 * <author>            <date>      <version>   <desc>
 * <EMAIL>   2022/7/1    1.0         create
 * <EMAIL>   2024/7/18   1.1         支持计算定向录音文件名
 **********************************************************/

package com.soundrecorder.common.utils;

import android.content.ContentResolver;
import android.content.res.Resources;
import android.database.Cursor;
import android.provider.MediaStore;
import android.text.TextUtils;

import androidx.annotation.StringRes;

import com.soundrecorder.base.BaseApplication;
import com.soundrecorder.base.utils.DebugUtil;
import com.soundrecorder.common.R;
import com.soundrecorder.common.constant.DatabaseConstant;
import com.soundrecorder.common.constant.RecordModeConstant;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class AudioNameUtils {
    private static final String TAG = "AudioNameUtils";

    /**
     * 录音文件命名支持最大长度
     */
    public static final int MAX_CHAR_LENGTH = 50;
    /**
     * the new file name not support require, cut the count length per time
     * the length for '_x'
     */
    public static final int SKIP_CHAR_LENGTH = 2;
    private static final String SPLIT_SPACE = " ";
    private static final String SPLIT_UNDERLINE = "_";
    private static final String SPLIT_ESCAPE = "@";

    public static String genDefaultFileName(String relativePath, String originalName) {
        String title = "";
        int index = originalName.lastIndexOf(".");
        String suffix = originalName.substring(index);
        /*still use origin name when crop file if origin file name is max*/
        if (originalName.substring(0, index).length() >= MAX_CHAR_LENGTH) {
            String newNme = originalName.substring(0, MAX_CHAR_LENGTH - SKIP_CHAR_LENGTH);
            DebugUtil.i(TAG, "genDefaultFileName newNme :" + newNme);
            return genDefaultFileName(relativePath, newNme + suffix);
        }
        if (originalName.contains(SPLIT_UNDERLINE)) {
            title = originalName.substring(0, originalName.lastIndexOf(SPLIT_UNDERLINE))
                    + SPLIT_SPACE;
        } else {
            title = originalName.replace(suffix, "") + SPLIT_SPACE;
        }
        return genDefaultTitle(relativePath, title, false) + suffix;
    }

    public static String genDefaultFileTitle(String relativePath, String originalName) {
        String title = "";
        int index = originalName.lastIndexOf(".");
        String suffix = originalName.substring(index);
        if (originalName.contains(SPLIT_UNDERLINE)) {
            int underlineIndex = originalName.lastIndexOf(SPLIT_UNDERLINE);
            String enString = originalName.substring(underlineIndex + 1, index);
            if (isNumeric(enString)) {
                title = originalName.substring(0, underlineIndex) + SPLIT_UNDERLINE;
            } else {
                title = originalName.replace(suffix, "") + SPLIT_UNDERLINE;
            }

        } else {
            title = originalName.replace(suffix, "") + SPLIT_UNDERLINE;
        }
        return genDefaultTitle(relativePath, title, true);
    }

    /**
     * 判断str是否全是数字
     */
    public static boolean isNumeric(String str) {
        if (str == null) {
            return false;
        }

        return str.matches("\\d+");
    }

    /**
     * 根据录制模式返回对应的模式描述
     *
     * @param recordType
     * @return 标准模式/会议模式/采访模式
     */
    public static String getRecordModeName(int recordType) {
        Resources res = BaseApplication.getAppContext().getResources();
        switch (recordType) {
            case RecordModeConstant.RECORD_TYPE_STANDARD:
                return res.getString(R.string.standard_mode);
            case RecordModeConstant.RECORD_TYPE_INTERVIEW:
                return res.getString(R.string.interview_mode);
            case RecordModeConstant.RECORD_TYPE_CONFERENCE:
                return res.getString(R.string.conference_mode);
            default:
                return "";
        }
    }

    public static String genSaveFileName(int recordType, String suffix, boolean isActivity) {
        String prefix = "";
        String relativePath = RecordModeUtil.getRelativePathByRecordType(recordType, true);
        Resources res = BaseApplication.getAppContext().getResources();
        switch (recordType) {
            case RecordModeConstant.RECORD_TYPE_STANDARD:
                prefix = isActivity ? res.getString(R.string.normal_record)
                                    : res.getString(R.string.recording_mode_standard);
                break;
            case RecordModeConstant.RECORD_TYPE_INTERVIEW:
                prefix = isActivity ? res.getString(R.string.interview_record)
                                    : res.getString(R.string.recording_mode_interview);
                break;
            case RecordModeConstant.RECORD_TYPE_CONFERENCE:
                prefix = isActivity ? res.getString(R.string.meeting_record)
                                    : res.getString(R.string.recording_mode_conference);
                break;
            default:
                break;
        }

        String title = genDefaultTitle(relativePath, prefix + SPLIT_SPACE, false);
        //TODO
        String name = title + suffix;
        DebugUtil.i(TAG, "gen save file name :" + name);
        return name;
    }

    /**
     * 定向录音音频名称
     *
     * @param suffix 文件后缀，如：.mp3/.wav
     * @return 如：定向录音 1.mp3
     */
    public static String genDirectRecordingName(String suffix) {

        String prefix = getResString(R.string.specified_direct_record);
        ArrayList<String> relativePathList = new ArrayList<String>();
        for (int recordType : RecordModeConstant.INSTANCE.getALL_RECORD_TYPE_LIST()) {
            String relativePath = RecordModeUtil.getRelativePathByRecordType(recordType, true);
            relativePathList.add(relativePath);
        }
        int no = getLastSuffixNum(relativePathList, prefix, false);

        String name = prefix + " " + no + suffix;
        DebugUtil.i(TAG, "genDirectRecordingName :" + name);
        return name;
    }

    private static String getResString(@StringRes int id) {
        return BaseApplication.getAppContext().getResources().getString(id);
    }

    private static String genDefaultTitle(String relativePath, String prefixName,
                                          boolean hasUnderline) {
        int lastSuffixNum = getLastSuffixNum(relativePath, prefixName, hasUnderline);
        DebugUtil.i(TAG, "genDefaultTitle = " + prefixName + lastSuffixNum);
        if ((prefixName + lastSuffixNum).length() >= MAX_CHAR_LENGTH) {
            String newName = prefixName.trim()
                                       .substring(0, prefixName.trim().length() - SKIP_CHAR_LENGTH);
            DebugUtil.i(TAG, "genDefaultTitle second newName = " + newName);
            return genDefaultTitle(relativePath,
                    newName + (hasUnderline ? SPLIT_UNDERLINE : SPLIT_SPACE), hasUnderline);
        }
        return prefixName + lastSuffixNum;
    }

    private static int getLastSuffixNum(String relativePath, String prefixName,
                                        boolean hasUnderline) {
        ArrayList<String> pathList = new ArrayList<>() {{
            add(relativePath);
        }};
        return getLastSuffixNum(pathList, prefixName, hasUnderline);
    }

    private static int getLastSuffixNum(List<String> relativePathList, String prefixName,
                                        boolean hasUnderline) {
        String[] projection = new String[]{MediaStore.Audio.Media.DISPLAY_NAME};
        String whereClause = null;
        String[] whereArgs = null;
        String escapeArgs = " escape '" + SPLIT_ESCAPE + "'";
        String noUnderline = " and instr(" + MediaStore.Audio.Media.DISPLAY_NAME + ",'"
                + SPLIT_UNDERLINE + "') = 0";
        String tmpPrefixName = (hasUnderline) ? prefixName.replace(SPLIT_UNDERLINE,
                SPLIT_ESCAPE + SPLIT_UNDERLINE) : prefixName;

        StringBuilder questionMarkBuilder = new StringBuilder();
        ArrayList<String> pathSet = new ArrayList<>();
        if (relativePathList != null) {
            for (int i = 0; i < relativePathList.size(); i++) {
                String path = relativePathList.get(i);
                pathSet.add(path);
                questionMarkBuilder.append("?");
                if (i != relativePathList.size() - 1) {
                    questionMarkBuilder.append(",");
                }
            }
        }
        String questionMark = questionMarkBuilder.toString();

        tmpPrefixName = RecordModeUtil.checkSingleQuotes(tmpPrefixName);

        String likeArgs = " like '" + tmpPrefixName + "%'";
        likeArgs = (hasUnderline) ? (likeArgs + escapeArgs) : (likeArgs + noUnderline);
        whereClause = MediaStore.Audio.Media.RELATIVE_PATH + " COLLATE NOCASE in (" + questionMark
                + ") "
                + "AND " + MediaStore.Audio.Media.DISPLAY_NAME + likeArgs;
        whereArgs = new String[pathSet.size()];
        pathSet.toArray(whereArgs);
        return getDefaultNum(projection, whereClause, whereArgs, null, prefixName, hasUnderline);
    }

    private static int getDefaultNum(String[] projection, String whereClause, String[] whereArgs,
                                     String orderBy, String prefixName, boolean hasUnderline) {
        int defaultNum = 1;
        String displayName = null;
        ContentResolver resolver = BaseApplication.getAppContext().getContentResolver();
        Cursor cursor = null;
        try {
            cursor = resolver.query(MediaStore.Audio.Media.EXTERNAL_CONTENT_URI, projection,
                    whereClause, whereArgs, orderBy);
            DebugUtil.i(TAG,
                    "getDefaultNum: " + whereClause + ", whereArgs: " + Arrays.toString(whereArgs));
            if (cursor != null) {
                while (cursor.moveToNext()) {
                    displayName = cursor.getString(
                            cursor.getColumnIndexOrThrow(MediaStore.Audio.Media.DISPLAY_NAME));
                    int tmpNum = getLastNum(displayName, prefixName, hasUnderline);
                    if (tmpNum >= defaultNum) {
                        defaultNum = tmpNum + 1;
                    }
                }
            }
        } catch (Exception e) {
            DebugUtil.e(TAG, "query exception", e);
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }

        return defaultNum;
    }

    private static int getRecycleFileNum(String prefixName, boolean hasUnderline) {
        int defaultNum = 1;
        ContentResolver resolver = BaseApplication.getAppContext().getContentResolver();
        Cursor cursor = null;
        String[] projection = {DatabaseConstant.RecorderColumn.COLUMN_NAME_DISPLAY_NAME};
        String whereClause = "is_recycle = ?";
        String[] args = {"1"};
        String displayName = null;
        try {
            cursor = resolver.query(DatabaseConstant.RecordUri.RECORD_CONTENT_URI, projection,
                    whereClause, args, null);
            if (cursor != null) {
                while (cursor.moveToNext()) {
                    displayName = cursor.getString(
                            cursor.getColumnIndexOrThrow(DatabaseConstant.RecorderColumn.COLUMN_NAME_DISPLAY_NAME));
                    //displayName非纯数字才处理，否则新增的默认文件名计数有误
                    String title = captureTitle(displayName);
                    boolean isNumber = isNumeric(title);
                    if (!isNumber) {
                        int tmpNum = getLastNum(displayName, prefixName, hasUnderline);
                        if (tmpNum >= defaultNum) {
                            defaultNum = tmpNum + 1;
                        }
                    }
                }
            }
        } catch (Exception e) {
            DebugUtil.e(TAG, "query exception", e);
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        DebugUtil.d(TAG, "getRecycleFileNum defaultNum = " + defaultNum);
        return defaultNum;
    }


    private static int getLastNum(String displayName, String prefixName, boolean hasUnderline) {
        int lastSuffixNum = 1;
        if (!TextUtils.isEmpty(displayName)) {
            String title = captureTitle(displayName);
            String lastNum = title.replace(prefixName, "");
            if (hasUnderline) {
                int lastIndexUnderline = lastNum.lastIndexOf(SPLIT_UNDERLINE);
                if ((lastIndexUnderline > -1) && (lastNum.length() > lastIndexUnderline + 1)) {
                    lastNum = lastNum.substring(lastIndexUnderline + 1);
                }
            } else {
                if (lastNum.contains(SPLIT_SPACE)) {
                    for (String item : lastNum.split(SPLIT_SPACE)) {
                        if (item == null || item.isEmpty()) {
                            continue;
                        }

                        lastNum = item;
                        break;
                    }
                }
            }
            try {
                if (!TextUtils.isEmpty(lastNum)) {
                    lastSuffixNum = Integer.parseInt(lastNum);
                }
            } catch (NumberFormatException e) {
                DebugUtil.e(TAG, "genDefaultTitle lastNum is no number.", e);
                lastSuffixNum = 0;
            }
        }
        return lastSuffixNum;
    }

    private static String captureTitle(String name) {
        String title = "";
        if (!TextUtils.isEmpty(name)) {
            int lastDot = name.lastIndexOf(".");
            if (lastDot > -1) {
                title = name.substring(0, lastDot);
            }
        }
        return title;
    }
}
