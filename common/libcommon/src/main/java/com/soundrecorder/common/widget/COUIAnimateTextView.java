/*********************************************************************
 ** Copyright (C), 2024-2034 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : COUIAnimateTextView
 ** Description :
 ** Version     : 1.0
 ** Date        : 2024/7/17 14:46
 ** Author      : 80342011
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 ** 80262777        2024/7/17       1.0      create
 ***********************************************************************/
package com.soundrecorder.common.widget;

import android.animation.Animator;
import android.animation.ValueAnimator;
import android.content.Context;
import android.content.res.TypedArray;
import android.os.SystemClock;
import android.text.Spannable;
import android.text.SpannableString;
import android.util.AttributeSet;
import android.util.Log;

import androidx.annotation.Keep;
import androidx.annotation.Nullable;

import com.coui.appcompat.contextutil.COUIContextUtil;
import com.coui.appcompat.textview.COUITextView;
import com.soundrecorder.common.R;
import com.soundrecorder.common.widget.animspan.COUIAnimateSpan;
import com.soundrecorder.common.widget.animspan.COUIAnimateSpan2;
import com.soundrecorder.common.widget.animspan.COUIAnimateSpanParam;

import java.util.HashMap;
import java.util.Objects;

@Keep
public class COUIAnimateTextView extends COUITextView {

    public static final int ANIMATE_TYPE_1 = 1;
    public static final int ANIMATE_TYPE_2 = 2;

    public static final int TYPE_SEQUENCE = 1;
    public static final int TYPE_GRADIENT = 2;
    public static final int TYPE_OFFSET = 4;
    private static final String TAG = "COUIAnimateTextView";

    private static final float DURATION_DEFAULT = 300;
    private static final float DELAY_DEFAULT = 8;
    private static final int UNSET = -1;
    private static final float OFFSET = 10f;

    private final ValueAnimator mTriggerAnimator = ValueAnimator.ofFloat(0f, 1f);
    private final HashMap<Integer, COUIAnimateSpan> mSpanMap = new HashMap<>();

    private TextAnimationListener mListener;

    private CharSequence mAnimateCharSequence;
    private int mStartColor;
    private int mEndColor;
    private int mStableColor;
    private long mDuration;
    private long mDelay;
    private float mTranslationOffset;
    private int mType;
    private int mAnimateStyle = ANIMATE_TYPE_1;

    private long mStartTime = UNSET;
    private long mPauseDeltaTime = UNSET;
    private int mCurReadyPos = UNSET;
    private int mStartPos = 0;
    private boolean mContinue = false;
    private boolean mLastReady = false;

    public COUIAnimateTextView(Context context) {
        this(context, null);
    }

    public COUIAnimateTextView(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, android.R.attr.textViewStyle);
    }

    public COUIAnimateTextView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);

        TypedArray a = context.obtainStyledAttributes(attrs, R.styleable.COUIAnimateTextView, defStyleAttr, 0);
        mAnimateStyle = a.getInteger(R.styleable.COUIAnimateTextView_couiAnimateStyle, ANIMATE_TYPE_1);
        mType = a.getInteger(R.styleable.COUIAnimateTextView_couiAnimateTextType, TYPE_SEQUENCE | TYPE_GRADIENT | TYPE_OFFSET);
        mStartColor = a.getColor(R.styleable.COUIAnimateTextView_couiAnimateTextStartColor,
                COUIContextUtil.getColor(context, R.color.coui_animate_text_start_color_default));
        mEndColor = a.getColor(R.styleable.COUIAnimateTextView_couiAnimateTextEndColor,
                COUIContextUtil.getColor(context, R.color.coui_animate_text_end_color_default));
        mStableColor = a.getColor(R.styleable.COUIAnimateTextView_couiAnimateTextStableColor, getTextColors().getDefaultColor());
        mDuration = (long) a.getFloat(R.styleable.COUIAnimateTextView_couiAnimateTextDuration, DURATION_DEFAULT);
        mDelay = (long) a.getFloat(R.styleable.COUIAnimateTextView_couiAnimateTextDelay, DELAY_DEFAULT);
        mTranslationOffset = a.getFloat(R.styleable.COUIAnimateTextView_couiAnimateTextOffset, OFFSET);
        a.recycle();

        mTriggerAnimator.addUpdateListener(animation -> COUIAnimateTextView.this.postInvalidate());
        mTriggerAnimator.addListener(new Animator.AnimatorListener() {
            @Override
            public void onAnimationStart(Animator animation) {
                //do nothing
            }

            @Override
            public void onAnimationEnd(Animator animation) {
                if (mListener != null) {
                    mListener.onAnimationEnd();
                }
            }

            @Override
            public void onAnimationCancel(Animator animation) {
                if (mListener != null) {
                    mListener.onAnimationEnd();
                }
            }

            @Override
            public void onAnimationRepeat(Animator animation) {
                //do nothing
            }
        });
    }

    /**
     * set text and start animation
     *
     * @param text the text who will play animation
     * @param isReset: 是否每次都从0开始播放
     */
    public void setAnimateText(CharSequence text, boolean isReset) {
        if (text == null || text.length() == 0) {
            return;
        }

        mContinue = false;
        mLastReady = false;
        if (isReset) {
            //智能标题每次都从0开始播放
            mStartPos = 0;
        } else {
            if (mAnimateCharSequence != null && mAnimateCharSequence.length() > 0
                    && text.length() >= mAnimateCharSequence.length()
                    && text.subSequence(0, mAnimateCharSequence.length()).equals(mAnimateCharSequence)) {
                //新文本是基于老文本的扩充
                mContinue = true;
            }

            if (mContinue) {
                if (mCurReadyPos == mAnimateCharSequence.length() - 1) {
                    //老文本动画已经全部结束, 从新文本增加的部分开始做动画
                    mLastReady = true;
                    mStartPos = mCurReadyPos + 1;
                }
            } else {
                //无法接续时，从头开始做动画
                mStartPos = 0;
            }
        }
        Log.d(TAG, "setAnimateText: mContinue:" + mContinue
                + ",mLastReady:" + mLastReady
                + ",mCurReadyPos:" + mCurReadyPos
                + ",mReadyPos:" + mStartPos);

        mAnimateCharSequence = text;
        showTextWithAnimation(mStartPos, mAnimateCharSequence.length() - 1, !mContinue);
    }

    /**
     * get text
     *
     * @return the text set by {@link #setAnimateText}
     */
    public CharSequence getAnimateCharSequence() {
        return mAnimateCharSequence;
    }

    /**
     * start animation between startPos and endPos
     *
     * @param startPos  the start position in {@link #mAnimateCharSequence} for play animation
     * @param endPos    the end position in {@link #mAnimateCharSequence} for play animation
     */
    public void showTextWithAnimation(int startPos, int endPos) {
        showTextWithAnimation(startPos, endPos, true);
    }

    private void showTextWithAnimation(int startPos, int endPos, boolean forceAnim) {
        if (mAnimateCharSequence == null || mAnimateCharSequence.length() == 0) {
            return;
        }

        if (startPos > endPos || startPos > mAnimateCharSequence.length() - 1 || endPos > mAnimateCharSequence.length() - 1) {
            return;
        }

        if (forceAnim) {
            //主动触发动画，刷新所有标记
            reset();
        }

        if (forceAnim || mStartTime == UNSET || !mContinue || mLastReady) {
            /*下面几种情况需要刷新开始时间, 否则接续即可
            (1)主动触发动画
            (2)第一次开始动画
            (3)文本无接续
            (4)文本有接续,但上个文本完成动画*/
            mStartTime = SystemClock.uptimeMillis();
        } else if (mPauseDeltaTime != UNSET) {
            //如果不需要刷新时间, 且之前有暂停动画, 从暂停进度接续
            calcStartTimeWithPauseDelta();
        }

        startAnimation(startPos, endPos, forceAnim);
    }

    void reset() {
        mContinue = false;
        mLastReady = false;
        mStartPos = 0;
        mCurReadyPos = UNSET;
        mPauseDeltaTime = UNSET;
        mSpanMap.clear();
    }

    private void startAnimation(int startPos, int endPos, boolean forceAnim) {
        SpannableString spanString = new SpannableString(mAnimateCharSequence);
        for (int i = startPos; i <= endPos; i ++) {
            final int index = i;
            final long totalDelay = mDelay * (index - startPos);
            if (!mSpanMap.containsKey(index)) {
                COUIAnimateSpanParam param = new COUIAnimateSpanParam();
                param.duration = mDuration;
                param.delay = totalDelay;
                param.textSize = getTextSize();
                param.offset = mTranslationOffset;
                param.startColor = mStartColor;
                param.endColor = mEndColor;
                param.stableColor = mStableColor;
                param.runEndRunnable = () -> {
                    mCurReadyPos = index;
                    if (mCurReadyPos == mAnimateCharSequence.length() - 1) {
                        mTriggerAnimator.cancel();
                    }
                };
                if (mAnimateStyle == ANIMATE_TYPE_2) {
                    mSpanMap.put(index, new COUIAnimateSpan2(param));
                } else {
                    mSpanMap.put(index, new COUIAnimateSpan(param));
                }
            } else {
                Objects.requireNonNull(mSpanMap.get(index)).resetAnimateParams(mDuration, totalDelay, mTranslationOffset);
            }
            spanString.setSpan(mSpanMap.get(index), index, index + 1, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
            Objects.requireNonNull(mSpanMap.get(index)).playAnimation(mType, mStartTime);
        }
        setText(spanString);
        mTriggerAnimator.setDuration((endPos - startPos) * mDelay + mDuration);
        if (forceAnim || !mTriggerAnimator.isRunning()) {
            mTriggerAnimator.start();
        } else if (mTriggerAnimator.isRunning() && mTriggerAnimator.isPaused()) {
            mTriggerAnimator.resume();
        }
    }

    /**
     * cancel animation and set text with stable status
     */
    public void cancelAnimation() {
        if (mAnimateCharSequence == null || mAnimateCharSequence.length() == 0) {
            return;
        }

        for (int i = 0; i < mAnimateCharSequence.length() ; i ++) {
            if (mSpanMap.containsKey(i)) {
                Objects.requireNonNull(mSpanMap.get(i)).cancelAnimation();
            }
        }
        mTriggerAnimator.cancel();
        postInvalidate();
        reset();
    }

    /**
     * pause animation and keep text status during animation
     */
    public void pauseAnimation() {
        if (mAnimateCharSequence == null || mAnimateCharSequence.length() == 0 || mPauseDeltaTime != UNSET) {
            return;
        }

        if (mTriggerAnimator.isRunning() && !mTriggerAnimator.isPaused()) {
            mPauseDeltaTime = SystemClock.uptimeMillis() - mStartTime;
            mTriggerAnimator.pause();
        }
        for (int i = 0; i < mAnimateCharSequence.length() ; i ++) {
            if (mSpanMap.containsKey(i)) {
                Objects.requireNonNull(mSpanMap.get(i)).pauseAnimation();
            }
        }
        postInvalidate();
    }

    public boolean isAnimating() {
        return mTriggerAnimator.isRunning() && !mTriggerAnimator.isPaused();
    }

    /**
     * resume animation if it has paused
     */
    public void resumeAnimation() {
        if (mAnimateCharSequence == null || mAnimateCharSequence.length() == 0 || mPauseDeltaTime == UNSET) {
            return;
        }

        if (mTriggerAnimator.isRunning() && mTriggerAnimator.isPaused()) {
            calcStartTimeWithPauseDelta();
            mTriggerAnimator.resume();
        }
        for (int i = 0; i < mAnimateCharSequence.length() ; i ++) {
            if (mSpanMap.containsKey(i)) {
                Objects.requireNonNull(mSpanMap.get(i)).resumeAnimation(mStartTime);
            }
        }
        postInvalidate();
    }

    private void calcStartTimeWithPauseDelta() {
        //恢复暂停时保存的动画进度
        mStartTime = SystemClock.uptimeMillis() - mPauseDeltaTime;
        mPauseDeltaTime = UNSET;
    }

    /**
     * set type for animation
     * TYPE_SEQUENCE = 1;
     * TYPE_GRADIENT = 2;
     * TYPE_OFFSETY = 4;
     */
    public void setType(int type) {
        mType = type;
    }

    /**
     * set start color for gradient-color animation
     *
     * color will change like: startColor -> endColor -> stableColor
     */
    public void setStartColor(int color) {
        mStartColor = color;
    }

    /**
     * set end color for gradient-color animation
     *
     * color will change like: startColor -> endColor -> stableColor
     */
    public void setEndColor(int color) {
        mEndColor = color;
    }

    /**
     * set stable color for gradient-color animation
     *
     * color will change like: startColor -> endColor -> stableColor
     */
    public void setStableColor(int color) {
        mStableColor = color;
    }

    /**
     * set offset for translationY animation
     */
    public void setTranslationOffset(float offset) {
        mTranslationOffset = offset;
    }

    /**
     * set duration for single character
     */
    public void setDuration(long duration) {
        mDuration = duration;
    }

    /**
     * set delay between two characters' animation
     */
    public void setDelay(long delay) {
        mDelay = delay;
    }

    public void setAnimationListener(TextAnimationListener listener) {
        mListener = listener;
    }

    @Keep
    public interface TextAnimationListener {
        void onAnimationEnd();
    }

    public void setSubtitleAnimateText(CharSequence text) {
        if (text == null || text.length() == 0) {
            return;
        }
        mContinue = false;
        mLastReady = false;
        /*
         * 实时ASR正在识别中的语句会被持续修正，导致存在吞字重出的现象。
         * 因此针对字幕去掉文字刷新后的内容接续校验，当字数没减少时均从已完成动画的位置继续做上屏动画。
         */
        if (mAnimateCharSequence != null && mAnimateCharSequence.length() > 0
                && text.length() >= mAnimateCharSequence.length()) {
            mContinue = true;
        }
        if (mContinue) {
            if (mCurReadyPos == mAnimateCharSequence.length() - 1) {
                mLastReady = true;
                mStartPos = mCurReadyPos + 1;
            }
        } else {
            /*
             * 当实时ASR持续修正导致字幕字数减少时，从更新后的最后一个字开始做动画，
             * 以防因持续的修正导致频繁从0开始做字幕上屏动画产生闪烁的效果。
             */
            mStartPos = Math.max(0, text.length() - 1);
        }
        mAnimateCharSequence = text;
        showTextWithAnimation(mStartPos, mAnimateCharSequence.length() - 1, !mContinue);
    }

}