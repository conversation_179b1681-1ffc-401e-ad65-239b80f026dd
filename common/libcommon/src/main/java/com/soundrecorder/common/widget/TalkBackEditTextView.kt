/******************************************************************
 * Copyright (C), 2020-2021, OPPO Mobile Comm Corp., Ltd
 * VENDOR_EDIT
 * File: - TalkBackEditTextView.kt
 * Description:
 * Version: 1.0
 * Date :  2020/7/31
 * Author: <EMAIL>
 **
 * ---------------- Revision History: ---------------------------
 *      <author>        <data>        <version >        <desc>
 *    Yongjiang,Lu      2020/7/31          1.0         build this module
 ********************************************************************/

package com.soundrecorder.common.widget

import android.content.Context
import android.text.TextUtils
import android.util.AttributeSet
import android.view.View
import androidx.core.view.ViewCompat
import androidx.core.view.accessibility.AccessibilityNodeInfoCompat
import com.coui.appcompat.edittext.COUIEditText

class TalkBackEditTextView : COUIEditText {

    constructor(context: Context?) : super(context) {}
    constructor(context: Context?, attrs: AttributeSet?) : super(context, attrs) {}
    constructor(context: Context?, attrs: AttributeSet?, defStyleAttr: Int) : super(context, attrs, defStyleAttr) {}

    fun setAccessibilityTouchHelper(text: String?) {
        ViewCompat.setAccessibilityDelegate(this, object : AccessibilityTouchHelper(this) {
            override fun onInitializeAccessibilityNodeInfo(host: View, info: AccessibilityNodeInfoCompat) {
                super.onInitializeAccessibilityNodeInfo(host, info)
                if (!TextUtils.isEmpty(text)) {
                    info.text = text
                }
                info.hintText = ""
                info.className = TalkBackEditTextView@ this.javaClass.name
            }
        })
    }
}