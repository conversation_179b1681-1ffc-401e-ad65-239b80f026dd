package com.soundrecorder.common.utils

import android.content.Context
import android.content.res.Configuration
import android.util.DisplayMetrics
import android.view.WindowManager
import androidx.annotation.DimenRes
import com.soundrecorder.base.BaseApplication


object DensityHelper {
    private const val SIZE_720 = "720P"
    private const val SIZE_1080 = "1080P"
    private const val SIZE_2K = "2K"

    private val mDpiSize = lazy {
        val wm = BaseApplication.getAppContext().getSystemService(Context.WINDOW_SERVICE) as WindowManager
        val display = wm.defaultDisplay
        val metrics = DisplayMetrics()
        display.getRealMetrics(metrics)
        val configuration = BaseApplication.getAppContext().resources.configuration
        val pixel = if (configuration.orientation == Configuration.ORIENTATION_LANDSCAPE) {
            metrics.heightPixels
        } else {
            metrics.widthPixels
        }
        when (pixel) {
            in 0..720 -> SIZE_720
            in 721..1080 -> SIZE_1080
            in 1081..1440 -> SIZE_2K
            else -> SIZE_1080
        }
    }

    private fun getDpiSize() = mDpiSize.value

    /**
     * Get a stable ratio for convert from dp to px.The will no change with system display size changing.
     */
    private fun getStableRatio(): Float {
        return when (getDpiSize()) {
            SIZE_720 -> 2f
            SIZE_1080 -> 3f
            SIZE_2K -> 4f
            else -> 3f
        }
    }

    fun getDefaultConfigDimension(@DimenRes id: Int): Int {
        val resources = BaseApplication.getAppContext().resources
        return (resources.getDimension(id) / resources.displayMetrics.density * getStableRatio()).toInt()
    }


    fun dip2px(context: Context, dpValue: Float): Int {
        val scale = context.resources.displayMetrics.density
        return (dpValue * scale + 0.5f).toInt()
    }


    fun px2dip(context: Context, pxValue: Float): Int {
        val scale = context.resources.displayMetrics.density
        return (pxValue / scale + 0.5f).toInt()
    }

    fun px2dip(context: Context, pxValue: Int): Int {
        val scale = context.resources.displayMetrics.density
        return (pxValue / scale + 0.5f).toInt()
    }
}