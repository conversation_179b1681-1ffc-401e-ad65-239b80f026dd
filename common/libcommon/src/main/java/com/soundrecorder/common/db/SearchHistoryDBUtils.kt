/***********************************************************
 * * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * * File:   SearchHistoryDBUtils
 * * Description: SearchHistoryDBUtils
 * * Version:1.0
 * * Date :2025/05/08
 * * Author:renjiahao
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>      <data>        <version>       <desc>
 * *   renjiahao                    1.0           create
 ****************************************************************/
package com.soundrecorder.common.db

import android.content.ContentValues
import android.content.Context
import android.database.sqlite.SQLiteDatabase
import android.net.Uri
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.constant.DatabaseConstant
import com.soundrecorder.common.constant.DatabaseConstant.SearchHistoryColumn.CONTENT
import com.soundrecorder.common.constant.DatabaseConstant.SearchHistoryColumn.DATE
import com.soundrecorder.common.databean.SearchHistory

object SearchHistoryDBUtils {

    private const val TAG = "SearchHistoryDBUtils"
    private const val SEVEN_DAYS = 7
    private const val NUMBER_SIXTY = 60
    private const val NUMBER_TWENTY_FOUR = 24
    private const val NUMBER_ONE_THOUSAND = 1000
    val searchHistoryUri: Uri = DatabaseConstant.getContentUri(DatabaseConstant.TABLE_NAME_SEARCH_HISTORY)

    @JvmStatic
    fun createSearchHistoryTable(db: SQLiteDatabase) {
        db.execSQL(
            ("CREATE TABLE IF NOT EXISTS " + DatabaseConstant.TABLE_NAME_SEARCH_HISTORY + " ("
                    + DatabaseConstant.SearchHistoryColumn.COLUMN_NAME_ID + " INTEGER PRIMARY KEY AUTOINCREMENT,"
                    + CONTENT + " TEXT UNIQUE,"
                    + DATE + " BIGINT NOT NULL"
                    + ");")
        )
    }

    @JvmStatic
    fun insertSearchHistory(context: Context, content: String, date: Long) {
        kotlin.runCatching {
            val contentValues = ContentValues().apply {
                put(CONTENT, content)
                put(DATE, date)
            }
            context.contentResolver.insert(searchHistoryUri, contentValues)
        }.onFailure {
            DebugUtil.e(TAG, "insertSearchHistory content:$content, $it")
        }
    }

    @JvmStatic
    fun updateSearchHistory(context: Context, content: String, date: Long) {
        val where = " $CONTENT = ? "
        val args = arrayOf(content)

        kotlin.runCatching {
            val contentValues = ContentValues().apply {
                put(CONTENT, content)
                put(DATE, date)
            }
            context.contentResolver.update(searchHistoryUri, contentValues, where, args)
        }.onFailure {
            DebugUtil.e(TAG, "updateSearchHistory content:$content, $it")
        }
    }

    @JvmStatic
    fun querySearchHistoryInSevenDays(context: Context): MutableList<SearchHistory> {
        val currentTime = System.currentTimeMillis()
        val timeArgs = currentTime - SEVEN_DAYS * NUMBER_TWENTY_FOUR * NUMBER_SIXTY * NUMBER_SIXTY * NUMBER_ONE_THOUSAND
        val selectionArgs = arrayOf(timeArgs.toString())
        val selection = " $DATE > ? "
        val sortOrder = " $DATE DESC "
        val projection = arrayOf(CONTENT, DATE)
        val result: MutableList<SearchHistory> = mutableListOf()
        kotlin.runCatching {
            context.contentResolver.query(searchHistoryUri, projection, selection, selectionArgs, sortOrder)?.use {
                while (it.moveToNext()) {
                    val content = it.getString(0)
                    val date = it.getLong(1)
                    result.add(SearchHistory(content, date))
                }
            }
        }.onFailure {
            DebugUtil.e(TAG, "querySearchHistoryInSevenDays, $it")
        }
        return result
    }

    @JvmStatic
    fun deleteSearchHistory(context: Context, content: String): Boolean {
        val where = " $CONTENT = ? "
        val args = arrayOf(content)
        kotlin.runCatching {
            val result = context.contentResolver.delete(searchHistoryUri, where, args)
            return result > 0
        }.onFailure {
            DebugUtil.e(TAG, "deleteSearchHistory, data: $content $it")
        }
        return false
    }

    @JvmStatic
    fun clearHistory(context: Context) {
        kotlin.runCatching {
            context.contentResolver.delete(searchHistoryUri, null, null)
        }.onFailure {
            DebugUtil.e(TAG, "deleteSearchHistory, $it")
        }
    }
}