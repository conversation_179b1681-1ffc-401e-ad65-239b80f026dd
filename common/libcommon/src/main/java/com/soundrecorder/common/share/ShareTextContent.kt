/***********************************************************
 * * Copyright (C), 2010-2024, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:  TextContentItem
 * * Description: TextContentItem
 * * Version: 1.0
 * * Date : 2025/3/6
 * * Author: W9066446
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>    <date>    <version>    <desc>
 * *  W9066446    2025/3/6   1.0    build this module
 ****************************************************************/
package com.soundrecorder.common.share

import com.soundrecorder.common.databean.ConvertContentItem

data class ShareTextContent(
    val mediaRecordId: Long,
    val isShowSpeaker: Boolean,
    val covertFileName: String,
    val convertFileSize: Long,
    var textContentItems: List<ConvertContentItem>
) {
    override fun toString(): String {
        return "mediaRecordId:$mediaRecordId" +
                " isShowSpeaker:$isShowSpeaker"
    }
}