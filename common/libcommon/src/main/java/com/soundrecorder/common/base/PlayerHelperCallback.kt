package com.soundrecorder.common.base

import androidx.lifecycle.MutableLiveData

interface PlayerHelperBasicCallback {

    fun getPlayerName(): MutableLiveData<String>?

    fun getPlayerMimeType(): String? {
        return ""
    }

    fun getKeyId(): String {
        return ""
    }

    fun onCurTimeChanged(curTime: Long) {
        // do nothing
    }
}

interface PlayerHelperCallback : PlayerHelperBasicCallback {
    fun hasPaused(): Boolean
    fun getDuration(): Long
    fun getCurrentPlayerTime(): Long
    fun playBtnClick()
}