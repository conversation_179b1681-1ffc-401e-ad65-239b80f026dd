
/***********************************************************
 ** Copyright (C), 2008-2017, OPPO Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File: RecordDataSync
 ** Description: sync records from MediaDB to Recorder DB
 ** Version: 1.0
 ** Date : 2019-07-11
 ** Author: huangyuanwang
 **
 ** v1.0, 2019-3-12, huangyuanwang, create
 ****************************************************************/
package com.soundrecorder.common.db;

import static android.content.ContentResolver.QUERY_ARG_LIMIT;
import static android.content.ContentResolver.QUERY_ARG_OFFSET;
import static android.content.ContentResolver.QUERY_ARG_SQL_SELECTION;
import static android.content.ContentResolver.QUERY_ARG_SQL_SELECTION_ARGS;
import static android.content.ContentResolver.QUERY_ARG_SQL_SORT_ORDER;
import static com.soundrecorder.base.BaseApplication.getAppContext;
import static com.soundrecorder.base.utils.SyncTimeUtils.MAX_COUNT;
import static com.soundrecorder.common.constant.DatabaseConstant.RecordUri.RECORD_CONTENT_URI;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLOUM_NAME_SYNC_DOWNLOAD_STATUS;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_AMP_DATA;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_AMP_FILE_PATH;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_BUCKET_DISPLAY_NAME;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_BUCKET_ID;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_CALLER_NAME;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_CALL_AVATAR_COLOR;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_CALL_NAME;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_CLOUD_CHECK_PAYLOAD;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_CLOUD_SYS_VERSION;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_DATA;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_DATE_CREATED;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_DATE_MODIFIED;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_DELETE;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_DELETE_TIME;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_DIRECT_TIME;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_DIRTY;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_DISPLAY_NAME;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_DURATION;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_ERROR_CODE;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_FAIL_COUNT;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_FILE_ID;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_FILE_RECYCLE_PATH;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_GLOBAL_ID;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_GROUP_ID;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_GROUP_UUID;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_ID;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_IS_DIRECT_ON;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_IS_MARKLIST_SHOWING;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_IS_RECYCLE;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_LAST_FAIL_TIME;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_LEVEL;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_LOCAL_EDIT_STATUS;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_MARK_DATA;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_MD5;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_MIGRATE_STATUS;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_MIMETYPE;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_ORIGINAL_NAME;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_PARSE_CALL;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_PRIVATE_STATUS;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_RECORD_TYPE;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_RELATIVE_PATH;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_SCAN_OSHARE_TEXT;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_SIZE;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_SYNC_DATE;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_SYNC_TYPE;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_SYNC_UPLOAD_STATUS;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_UUID;
import static com.soundrecorder.common.constant.RecordConstant.LOCAL_EDITING;
import static com.soundrecorder.common.constant.RecordConstant.LOCAL_EDIT_COMPLET;
import static com.soundrecorder.common.constant.RecordConstant.MIMETYPE_ACC;
import static com.soundrecorder.common.constant.RecordConstant.MIMETYPE_ACC_ADTS;
import static com.soundrecorder.common.constant.RecordConstant.MIMETYPE_AMR;
import static com.soundrecorder.common.constant.RecordConstant.MIMETYPE_AMR_WB;
import static com.soundrecorder.common.constant.RecordConstant.MIMETYPE_MP3;
import static com.soundrecorder.common.constant.RecordConstant.MIMETYPE_WAV;
import static com.soundrecorder.common.constant.RecordConstant.RECORD_DELETED;
import static com.soundrecorder.common.constant.RecordConstant.RECORD_DIRTY_FILE_AND_MEGA;
import static com.soundrecorder.common.constant.RecordConstant.RECORD_DIRTY_MEGA_ONLY;
import static com.soundrecorder.common.constant.RecordConstant.RECORD_NOT_DIRTY;
import static com.soundrecorder.common.constant.RecordConstant.RECORD_PRIVATE_NORMAL;
import static com.soundrecorder.common.constant.RecordConstant.RECORD_PRIVETE_ENCRYPT;
import static com.soundrecorder.common.constant.RecordConstant.SYNC_STATUS_BACKUPING_FILE;
import static com.soundrecorder.common.constant.RecordConstant.SYNC_STATUS_BACKUP_FILE_FAILED;
import static com.soundrecorder.common.constant.RecordConstant.SYNC_STATUS_BACKUP_FILE_SUC;
import static com.soundrecorder.common.constant.RecordConstant.SYNC_STATUS_BACKUP_START;
import static com.soundrecorder.common.constant.RecordConstant.SYNC_STATUS_LOCALLY_EXISTS_METADATA;
import static com.soundrecorder.common.constant.RecordConstant.SYNC_STATUS_RECOVERYING_FILE;
import static com.soundrecorder.common.constant.RecordConstant.SYNC_STATUS_RECOVERY_FILE_FAILED;
import static com.soundrecorder.common.constant.RecordConstant.SYNC_STATUS_RECOVERY_FILE_JUMP;
import static com.soundrecorder.common.constant.RecordConstant.SYNC_STATUS_RECOVERY_FILE_SUC;
import static com.soundrecorder.common.constant.RecordConstant.SYNC_STATUS_RECOVERY_MEGADATA_SUC;
import static com.soundrecorder.common.constant.RecordConstant.SYNC_TYPE_BACKUP;
import static com.soundrecorder.common.constant.RecordConstant.SYNC_TYPE_RECOVERY;
import static com.soundrecorder.common.db.CursorHelper.DEFAULT_DIR;

import android.app.RecoverableSecurityException;
import android.content.ContentProviderOperation;
import android.content.ContentProviderResult;
import android.content.ContentResolver;
import android.content.ContentUris;
import android.content.ContentValues;
import android.content.Context;
import android.content.OperationApplicationException;
import android.database.Cursor;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.RemoteException;
import android.provider.MediaStore;
import android.text.TextUtils;

import androidx.annotation.Nullable;

import com.soundrecorder.base.BaseApplication;
import com.soundrecorder.base.utils.BaseUtil;
import com.soundrecorder.base.utils.DebugUtil;
import com.soundrecorder.base.utils.FileUtils;
import com.soundrecorder.base.utils.MD5Utils;
import com.soundrecorder.base.utils.NumberConstant;
import com.soundrecorder.common.R;
import com.soundrecorder.common.constant.Constants;
import com.soundrecorder.common.constant.DatabaseConstant;
import com.soundrecorder.common.constant.RecordConstant;
import com.soundrecorder.common.constant.RecordModeConstant;
import com.soundrecorder.common.databean.DirectRecordStatus;
import com.soundrecorder.common.databean.GroupInfo;
import com.soundrecorder.common.databean.MediaCounter;
import com.soundrecorder.common.databean.Record;
import com.soundrecorder.common.databean.markdata.MarkDataBean;
import com.soundrecorder.common.sync.db.RecordDataSync;
import com.soundrecorder.common.utils.AmpFileUtil;
import com.soundrecorder.common.utils.MarkProcessUtil;
import com.soundrecorder.common.utils.MarkSerializUtil;
import com.soundrecorder.common.utils.RecordModeUtil;
import com.soundrecorder.common.utils.SubRecorderTextUtils;
import com.soundrecorder.common.widget.CircleTextImageUtil;
import com.soundrecorder.modulerouter.cloudkit.CloudKitInterface;
import com.soundrecorder.modulerouter.playback.PlayBackInterface;
import com.soundrecorder.modulerouter.utils.KoinInterfaceHelper;

import java.io.File;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.UUID;

/**
 * 更新recorder-table工具类，仅操作数据库信息，
 * 不涉及云同步逻辑，原云同步逻辑移到CloudSyncRecorderDbUtil.java
 * 若需更新数据库的同时触发云同步，请移步CloudSyncRecorderDbUtil.java
 */
public class RecorderDBUtil {


    public static final int IS_SHOWING_TRUE = 1;
    public static final int IS_SHOWING_FALSE = 0;
    public static final int TIME_ONE_SECOND = 1000;

    private static final String TAG = "RecorderDBUtil";
    private static final int MAX_OPERATIONS = 200;
    private static final int OPERATIONS_SLEEPTIME = 50;
    private static final String[] RECDOORD_PROJECTION = new String[]{
            COLUMN_NAME_ID,
            COLUMN_NAME_UUID,
            COLUMN_NAME_DATA,
            COLUMN_NAME_SIZE,
            COLUMN_NAME_DISPLAY_NAME,
            COLUMN_NAME_MIMETYPE,
            COLUMN_NAME_DATE_CREATED,
            COLUMN_NAME_DATE_MODIFIED,
            COLUMN_NAME_RECORD_TYPE,
            COLUMN_NAME_MARK_DATA,
            COLUMN_NAME_AMP_DATA,
            COLUMN_NAME_DURATION,
            COLUMN_NAME_BUCKET_ID,
            COLUMN_NAME_BUCKET_DISPLAY_NAME,
            COLUMN_NAME_DIRTY,
            COLUMN_NAME_DELETE,
            COLUMN_NAME_MD5,
            COLUMN_NAME_FILE_ID,
            COLUMN_NAME_GLOBAL_ID,
            COLUMN_NAME_SYNC_TYPE,
            COLUMN_NAME_SYNC_UPLOAD_STATUS,
            COLOUM_NAME_SYNC_DOWNLOAD_STATUS,
            COLUMN_NAME_ERROR_CODE,
            COLUMN_NAME_LEVEL,
            COLUMN_NAME_LOCAL_EDIT_STATUS,
            COLUMN_NAME_SYNC_DATE,
            COLUMN_NAME_FAIL_COUNT,
            COLUMN_NAME_LAST_FAIL_TIME,
            COLUMN_NAME_RELATIVE_PATH,
            COLUMN_NAME_AMP_FILE_PATH,
            COLUMN_NAME_PRIVATE_STATUS,
            COLUMN_NAME_MIGRATE_STATUS,
            COLUMN_NAME_IS_MARKLIST_SHOWING,
            COLUMN_NAME_CLOUD_SYS_VERSION,
            COLUMN_NAME_CLOUD_CHECK_PAYLOAD,
            COLUMN_NAME_IS_DIRECT_ON,
            COLUMN_NAME_DIRECT_TIME,
            COLUMN_NAME_IS_RECYCLE,
            COLUMN_NAME_FILE_RECYCLE_PATH,
            COLUMN_NAME_DELETE_TIME,
            COLUMN_NAME_GROUP_ID,
            COLUMN_NAME_GROUP_UUID,
            COLUMN_NAME_CALLER_NAME,
            COLUMN_NAME_ORIGINAL_NAME,
            COLUMN_NAME_CALL_AVATAR_COLOR,
            COLUMN_NAME_SCAN_OSHARE_TEXT
    };
    private final static String RECORDER_QUERY_ORDER = COLUMN_NAME_DATE_CREATED + " DESC," + COLUMN_NAME_DISPLAY_NAME + " DESC";

    private final static String WECHAT_EXP_PREFIX = "WeChat";

    private static volatile RecorderDBUtil sInstance;
    private Context mContext;

    public static RecorderDBUtil getInstance(Context context) {
        if (sInstance == null) {
            synchronized (RecorderDBUtil.class) {
                if (sInstance == null) {
                    sInstance = new RecorderDBUtil(context);
                }
            }
        }
        return sInstance;
    }


    private RecorderDBUtil(Context mContext) {
        this.mContext = mContext.getApplicationContext();
    }


    public boolean insertOrUpdateNewRecord(File file, String ampString, List<MarkDataBean> markDataBeans,
                                           int recordType, GroupInfo currentGroupInfo, DirectRecordStatus directRecordStatus) {
        if (file == null) {
            DebugUtil.i(TAG, "file is empty");
            return false;
        }
        DebugUtil.d(TAG, "insertOrUpdateNewRecord to syncDB start !");
        DebugUtil.d(TAG, "file Name = " + file.getName());
        ContentResolver resolver = mContext.getContentResolver();
        Uri base = MediaDBUtils.BASE_URI;
        String mFilePath = file.getAbsolutePath();
        long fileSize = file.length();
        String data = null;
        String where = null;
        if (mFilePath.contains("'")) {
            data = mFilePath.replace("'", "''");
        } else {
            data = mFilePath;
        }
        if (!TextUtils.isEmpty(data)) {
            where = MediaStore.Audio.Media.DATA + " COLLATE NOCASE = ? ";
        }
        Cursor cr = null;
        Record record = null;
        try {
            Bundle queryBundle = createSqlQueryBundle(where, new String[]{data}, null, null, null);
            cr = resolver.query(base, null, queryBundle, null);
            if ((cr != null) && (cr.getCount() > 0) && cr.moveToFirst()) {
                record = new Record(cr, Record.TYPE_FROM_MEDIA);
                DebugUtil.i(TAG, "size in media : " + record.getFileSize() + ", size in new file: " + fileSize);
            }
        } catch (Exception e) {
            DebugUtil.e(TAG, "insertOrUpdateNewRecord query get Exception ", e);
        } finally {
            if (cr != null) {
                cr.close();
            }
        }
        if (record == null) {
            DebugUtil.i(TAG, "insertOrUpdateNewRecord no media record found in MediaDb , return ");
            return false;
        }
        if (currentGroupInfo != null) {
            record.setGroupId(currentGroupInfo.getMId());
            record.setGroupUuid(currentGroupInfo.getMUuId());
        }
        record.setFileSize(fileSize);
        if (!TextUtils.isEmpty(ampString)) {
            record.checkAndSaveAmpFile(mContext, ampString);
        }
        String markString = MarkSerializUtil.INSTANCE.convertStoredDBStringForMarkDataBeanList(markDataBeans);
        if (!TextUtils.isEmpty(markString)) {
            record.setMarkData(markString.getBytes(Charset.forName("UTF-8")));
        }
        record.setMarkDataBeanList(markDataBeans);

        setRecordParams(file, record, recordType, directRecordStatus);
        return insertOrUpdateNewRecord(record);
    }

    private static void setRecordParams(File file, Record record, int recordType, DirectRecordStatus directRecordStatus) {
        record.setUuid(UUID.randomUUID().toString());
        if (recordType != -1) {
            record.setRecordType(recordType);
        }
        String relativePath = getRelativePathForData(record.getData(), record.getDisplayName());
        record.setRelativePath(relativePath);
        String md5 = MD5Utils.getMD5(file);
        record.setMD5(md5);
        record.setDirty(RECORD_DIRTY_FILE_AND_MEGA);
        record.setSyncType(SYNC_TYPE_BACKUP);
        record.setSyncUploadStatus(SYNC_STATUS_BACKUP_START);
        if (directRecordStatus != null) {
            record.setDirectOn(directRecordStatus.isDirectOn());
            record.setDirectTime(directRecordStatus.getDirectTime());
        }
    }

    public boolean insertOrUpdateCallRecord(Uri uriFromMedia, String ampString) {
        if (uriFromMedia == null) {
            DebugUtil.i(TAG, "file is empty");
            return false;
        }
        DebugUtil.d(TAG, "insertOrUpdateNewRecord to syncDB start !");
        Record recordFromMedia = getCallRecordByMediaUri(uriFromMedia);
        if (recordFromMedia == null) {
            DebugUtil.i(TAG, "record from media is null, can not update or insert");
            return false;
        }
        ContentResolver resolver = mContext.getContentResolver();
        Uri base = RECORD_CONTENT_URI;
        String data = recordFromMedia.getData();
        String selection = null;
        String[] selectionArgs = null;
        if (!TextUtils.isEmpty(data)) {
            selection = DatabaseConstant.RecorderColumn.COLUMN_NAME_DATA + " COLLATE NOCASE = ?";
            selectionArgs = new String[]{data};
        }
        Cursor cr = null;
        ContentValues cv = null;
        try {
            cr = resolver.query(base, null, selection, selectionArgs, null);
            if ((cr != null) && (cr.getCount() > 0) && cr.moveToFirst()) {
                Record recordFromDb = new Record(cr, Record.TYPE_FROM_RECORD);
                if (!TextUtils.isEmpty(ampString)) {
                    recordFromDb.checkAndSaveAmpFile(mContext, ampString);
                }
                cv = recordFromDb.convertToContentValues();
                int result = resolver.update(base, cv, selection, selectionArgs);
                return result > 0;
            } else {
                if (!TextUtils.isEmpty(ampString)) {
                    recordFromMedia.checkAndSaveAmpFile(mContext, ampString);
                }
                cv = recordFromMedia.convertToContentValues();
                Uri insertUri = resolver.insert(base, cv);
                if (insertUri != null) {
                    return true;
                }
            }
        } catch (Exception e) {
            DebugUtil.e(TAG, "addToMediaDB query get Exception ", e);
        } finally {
            if (cr != null) {
                cr.close();
            }
        }
        return false;
    }

    private Record getCallRecordByMediaUri(Uri uriFromMedia) {
        Record recordFromMedia = MediaDBUtils.getRecordFromMediaByUriId(uriFromMedia);
        if (recordFromMedia == null) {
            return null;
        }
        if (TextUtils.isEmpty(recordFromMedia.getRelativePath())) {
            String relativePath = getRelativePathForData(recordFromMedia.getData(), recordFromMedia.getDisplayName());
            recordFromMedia.setRelativePath(relativePath);
            DebugUtil.i(TAG, "getCallRecordByMediaUri setRelativePath: $relativePath");
        }
        long duration = MediaDBUtils.getDurationFromUri(uriFromMedia);
        long fileSize = FileUtils.getFileSize(uriFromMedia);
        recordFromMedia.setFileSize(fileSize);
        recordFromMedia.setDuration(duration);
        recordFromMedia.setUuid(UUID.randomUUID().toString());
        recordFromMedia.setRecordType(RecordModeConstant.RECORD_TYPE_CALL);
        String md5 = MD5Utils.getMD5(uriFromMedia);
        recordFromMedia.setMD5(md5);
        recordFromMedia.setDirty(RECORD_DIRTY_FILE_AND_MEGA);
        recordFromMedia.setSyncType(SYNC_TYPE_BACKUP);
        recordFromMedia.setSyncUploadStatus(SYNC_STATUS_BACKUP_START);
        return recordFromMedia;
    }


    public boolean insertOrUpdateNewRecord(Uri uriFromMedia, String ampString,
                                           List<MarkDataBean> markDataBeans,
                                           int recordType,
                                           GroupInfo currentGroupInfo,
                                           boolean isReadySync,
                                           DirectRecordStatus directRecordStatus,
                                           String existedUUID) {
        if (uriFromMedia == null) {
            DebugUtil.i(TAG, "file is empty");
            return false;
        }
        DebugUtil.d(TAG, "insertOrUpdateNewRecord to syncDB start !");
        Record recordFromMedia = MediaDBUtils.getRecordFromMediaByUriId(uriFromMedia);
        long duration = MediaDBUtils.getDurationFromUri(uriFromMedia);
        long fileSize = FileUtils.getFileSize(uriFromMedia);
        if (recordFromMedia == null) {
            DebugUtil.i(TAG, "insertOrUpdateNewRecord no media record found in MediaDb , return ");
            return false;
        }
        if (currentGroupInfo != null) {
            recordFromMedia.setGroupId(currentGroupInfo.getMId());
            recordFromMedia.setGroupUuid(currentGroupInfo.getMUuId());
        }
        recordFromMedia.setFileSize(fileSize);
        recordFromMedia.setDuration(duration);
        if (!TextUtils.isEmpty(ampString)) {
            recordFromMedia.checkAndSaveAmpFile(mContext, ampString);
        }
        //这个地方存放的是全量标记列表中的text标记列表
        String markString = MarkSerializUtil.INSTANCE.convertStoredDBStringForMarkDataBeanList(markDataBeans);
        if (!TextUtils.isEmpty(markString)) {
            recordFromMedia.setMarkData(markString.getBytes(Charset.forName("UTF-8")));
        }
        //这个是全量的标记列表
        recordFromMedia.setMarkDataBeanList(markDataBeans);
        String uuid = (!TextUtils.isEmpty(existedUUID)) ? existedUUID : UUID.randomUUID().toString();
        recordFromMedia.setUuid(uuid);
        if (recordType != -1) {
            recordFromMedia.setRecordType(recordType);
        }
        String md5 = MD5Utils.getMD5(uriFromMedia);
        recordFromMedia.setMD5(md5);
        if (isReadySync) {
            recordFromMedia.setDirty(RECORD_DIRTY_FILE_AND_MEGA);
            recordFromMedia.setSyncType(SYNC_TYPE_BACKUP);
            recordFromMedia.setSyncUploadStatus(SYNC_STATUS_BACKUP_START);
        }
        correctDirectTime(directRecordStatus, recordFromMedia);
        return insertOrUpdateNewRecord(recordFromMedia);
    }

    /**
     * 定向录音最后结束时间和 record总时长不一致，进行矫正
     * @param directRecordStatus
     * @param recordFromMedia
     */
    private static void correctDirectTime(DirectRecordStatus directRecordStatus, Record recordFromMedia) {
        if (directRecordStatus != null) {
            recordFromMedia.setDirectOn(directRecordStatus.isDirectOn());
            if (!TextUtils.isEmpty(directRecordStatus.getDirectTime())) {
                /*1761-5417,8513-14070,19269-31326*/
                String newDirectTime = null;
                int lastSplitIndex = directRecordStatus.getDirectTime().lastIndexOf("-") + 1;
                String originDirectTime = directRecordStatus.getDirectTime().substring(0, lastSplitIndex);
                String lastEndTimeStr = directRecordStatus.getDirectTime().substring(lastSplitIndex);
                if (!TextUtils.isEmpty(lastEndTimeStr)) {
                    long lastEndTime = Long.parseLong(lastEndTimeStr);
                    if (recordFromMedia.getDuration() > 0 && lastEndTime >= recordFromMedia.getDuration()) {
                        newDirectTime = originDirectTime + recordFromMedia.getDuration();
                    } else {
                        newDirectTime = originDirectTime + lastEndTime;
                    }
                }
                DebugUtil.d(TAG, "correctDirectTime, newDirectTime:" + newDirectTime);
                recordFromMedia.setDirectTime(newDirectTime);
            }
        }
    }


    private boolean insertOrUpdateNewRecord(Record inputRecord) {
        if (inputRecord == null) {
            DebugUtil.i(TAG, "input record is null, can not update or insert");
            return false;
        }
        ContentResolver resolver = mContext.getContentResolver();
        String[] selectionArr = new String[]{DatabaseConstant.RecorderColumn.COLUMN_NAME_DATA};
        Uri base = RECORD_CONTENT_URI;
        String data = inputRecord.getData();
        String uuid = inputRecord.getUuid();
        String where = null;
        String[] whereArgs = null;
        if (!TextUtils.isEmpty(data)) {
            data = RecordModeUtil.checkSingleQuotes(data);
            where = selectionArr[0] + " COLLATE NOCASE =" + " ? ";
            whereArgs = new String[]{data};
        }
        /*
        修复保存文件时重命名，原有默认名称的数据库记录变为脏数据的问题。
        由于重命名后_data会改变，用uuid和_data字段的 or查询records表是否已有记录
         */
        if (!TextUtils.isEmpty(uuid)) {
            uuid = RecordModeUtil.checkSingleQuotes(uuid);
            String whereUuid = COLUMN_NAME_UUID + " = ? ";
            boolean isDataEmpty = TextUtils.isEmpty(data);
            where = (!isDataEmpty) ? (where + "OR " + whereUuid) : whereUuid;
            whereArgs = (!isDataEmpty) ? (new String[]{data, uuid}) : (new String[]{uuid});
        }
        DebugUtil.i(TAG, ">>> where： " + where + ", args: data = " + data + ", uuid = " + uuid);

        Cursor cr = null;
        ContentValues cv = null;
        boolean result = false;
        List<MarkDataBean> markDataBeans = inputRecord.getMarkDataBeanList();
        try {
            cv = inputRecord.convertToContentValues();
            cr = resolver.query(base, null, where, whereArgs, null);
            if ((cr != null) && (cr.getCount() > 0) && cr.moveToFirst()) {
                String globalId = "";
                int globalIdIndex = cr.getColumnIndex(COLUMN_NAME_GLOBAL_ID);
                if (globalIdIndex >= 0) {
                    globalId = cr.getString(globalIdIndex);
                }
                String md5 = "";
                int md5Index = cr.getColumnIndex(COLUMN_NAME_MD5);
                if (md5Index >= 0) {
                    md5 = cr.getString(md5Index);
                }
                /*
                    If the metadata in the recording database comes from the cloud,
                    And MD5 is not the same,
                    Insert a new metadata into the database, otherwise update the database.
                 */
                if (!TextUtils.isEmpty(globalId) && !inputRecord.getMD5().equals(md5)) {
                    Uri insertUri = resolver.insert(base, cv);
//                    DebugUtil.i(TAG, ">>> insert recorder db " + inputRecord.getDisplayName() + "insertUri: " + insertUri);
                    result = (insertUri != null);
                    if (result) {
                        //插入新的Mark表格
                        long recordId = ContentUris.parseId(insertUri);
                        String keyId = recordId + "";
                        updateOrInsertMarkInNewTable(keyId, true, markDataBeans, false);
                    }
                } else {
                    int updateCount = resolver.update(base, cv, where, whereArgs);
//                    DebugUtil.i(TAG, ">>> update recorder db " + inputRecord.getDisplayName() + " count: " + updateCount);
                    result = updateCount > 0;
                    long recordId = -1;
                    int idIndex = cr.getColumnIndex(COLUMN_NAME_ID);
                    if (idIndex >= 0) {
                        recordId = cr.getLong(idIndex);
                    }
                    String keyId = String.valueOf(recordId);
                    //插入或更新Mark表格
                    updateOrInsertMarkInNewTable(keyId, false, markDataBeans, true);
                }
            } else {
                Uri insertUri = resolver.insert(base, cv);
                result = (insertUri != null);
                if (result) {
                    //插入Mark表格
                    long recordId = ContentUris.parseId(insertUri);
                    String keyId = recordId + "";
                    updateOrInsertMarkInNewTable(keyId, true, markDataBeans, false);
                }
            }
        } catch (Exception e) {
            DebugUtil.e(TAG, "addToMediaDB query get Exception ", e);
        } finally {
            if (cr != null) {
                cr.close();
            }
        }
        return result;
    }

    public boolean updateDisplayName(String originalName, String newName, String relativePath, boolean setNeedUpload, String realOriginalName) {
        Record record = getRecordByRelativePathAndDisplayName(relativePath, originalName);
        DebugUtil.i(TAG, "relativePath: " + relativePath + ", originalName: " + originalName + ", Record: " + record);
        if (record != null) {
            String where = COLUMN_NAME_ID + " = ?";
            String[] whereArgs = new String[]{String.valueOf(record.getId())};
            ContentValues contentValues = new ContentValues();
            String originalData = record.getData();
            String newPath = originalData;
            int lastIndexOfFileDiscriptor = originalData.lastIndexOf(File.separator);
            if (lastIndexOfFileDiscriptor > 0) {
                newPath = originalData.substring(0, lastIndexOfFileDiscriptor) + File.separator + newName;
            }
            contentValues.put(DatabaseConstant.RecorderColumn.COLUMN_NAME_DATA, newPath);
            contentValues.put(COLUMN_NAME_DISPLAY_NAME, newName);
            if (realOriginalName == null) {
                // realOriginalName可能因为升级版本等原因为空，这里需要赋上。
                contentValues.put(COLUMN_NAME_ORIGINAL_NAME, originalName);
            }
            if (setNeedUpload) {
                contentValues.put(DatabaseConstant.RecorderColumn.COLUMN_NAME_DIRTY, RECORD_DIRTY_MEGA_ONLY);
                contentValues.put(DatabaseConstant.RecorderColumn.COLUMN_NAME_SYNC_TYPE, SYNC_TYPE_BACKUP);
                contentValues.put(COLUMN_NAME_SYNC_UPLOAD_STATUS, SYNC_STATUS_BACKUP_START);
                /*重命名后，清除上次失败记录*/
                contentValues.put(COLUMN_NAME_FAIL_COUNT, 0);
                contentValues.put(COLUMN_NAME_LAST_FAIL_TIME, 0);
            }
            int updateResult = updateRecordData(mContext, contentValues, where, whereArgs);
//            if (updateResult > 0) {
//                DebugUtil.i(TAG, "rename trig backup");
//                SyncTriggerManager.getInstance(mContext).trigBackupNow();
//            }
            DebugUtil.d(TAG, "updateDisplayName, newName:" + newName + ", updateResult:" + updateResult);
            return updateResult > 0;
        }
        return false;
    }

    public boolean updateDisplayNameByRecordId(String rowId, String newName, int recordType, boolean setNeedUpload) {
        String relativePath = RecordModeUtil.getRelativePathByRecordType(recordType, true);
        Record record = getRecordById(rowId);
        DebugUtil.i(TAG, "relativePath: " + relativePath + ", newName: " + newName + ", Record: " + record);
        if (record != null) {
            String where = COLUMN_NAME_ID + " = ?";
            String[] whereArgs = new String[]{String.valueOf(record.getId())};
            ContentValues contentValues = new ContentValues();
            String originalData = record.getData();
            String newPath = originalData;
            int lastIndexOfFileDiscriptor = originalData.lastIndexOf(File.separator);
            if (lastIndexOfFileDiscriptor > 0) {
                newPath = originalData.substring(0, lastIndexOfFileDiscriptor) + File.separator + newName;
            }
            contentValues.put(DatabaseConstant.RecorderColumn.COLUMN_NAME_DATA, newPath);
            contentValues.put(COLUMN_NAME_DISPLAY_NAME, newName);
            if (setNeedUpload) {
                contentValues.put(DatabaseConstant.RecorderColumn.COLUMN_NAME_DIRTY, RECORD_DIRTY_MEGA_ONLY);
                contentValues.put(DatabaseConstant.RecorderColumn.COLUMN_NAME_SYNC_TYPE, SYNC_TYPE_BACKUP);
                contentValues.put(COLUMN_NAME_SYNC_UPLOAD_STATUS, SYNC_STATUS_BACKUP_START);
            }
            int updateResult = updateRecordData(mContext, contentValues, where, whereArgs);
//            if (updateResult > 0) {
//                DebugUtil.i(TAG, "rename trig backup");
//                SyncTriggerManager.getInstance(mContext).trigBackupNow();
//            }
            return updateResult > 0;
        }
        return false;
    }


    public boolean updateRecordAmplitudeByPath(String fullpath, String inputString) {
        if (TextUtils.isEmpty(inputString)) {
            DebugUtil.i(TAG, "inputString is empty, not update amp data");
            return false;
        }
        int updateCount = 0;
        try {
            ContentValues cv = new ContentValues();
            File ampFile = AmpFileUtil.saveAmpFile(mContext, inputString);
            if (ampFile != null) {
                cv.put(COLUMN_NAME_AMP_FILE_PATH, ampFile.getAbsolutePath());
                String where = DatabaseConstant.RecorderColumn.COLUMN_NAME_DATA + " COLLATE NOCASE =?";
                updateCount = mContext.getContentResolver().update(RECORD_CONTENT_URI, cv, where, new String[]{fullpath});
            }
        } catch (Exception e) {
            DebugUtil.e(TAG, "updateRecordAmplitudeByPath error " + e);
        }

        DebugUtil.i(TAG, "updateRecordAmplitudeByPath name: " + FileUtils.getDisplayNameByPath(fullpath) + ", ampString size: " + inputString.length() + ", updateCount: " + updateCount);
        return updateCount > 0;
    }

    public boolean updateRecordAmplitudeById(long id, String inputString) {
        if (TextUtils.isEmpty(inputString)) {
            DebugUtil.i(TAG, "inputString is empty, not update amp data");
            return false;
        }
        int updateCount = 0;
        try {
            ContentValues cv = new ContentValues();
            File ampFile = AmpFileUtil.saveAmpFile(mContext, inputString);
            if (ampFile != null) {
                cv.put(COLUMN_NAME_AMP_FILE_PATH, ampFile.getAbsolutePath());
                String where = COLUMN_NAME_ID + "=?";
                updateCount = mContext.getContentResolver().update(RECORD_CONTENT_URI,
                        cv, where, new String[]{String.valueOf(id)});
            }
        } catch (Exception e) {
            DebugUtil.e(TAG, "updateRecordAmplitudeById error " + e);
        }

        DebugUtil.i(TAG, "updateRecordAmplitudeByPath id: " + id + ", ampString size: " + inputString.length() + ", updateCount: " + updateCount);
        return updateCount > 0;
    }


    public boolean updateRecordIsMarkListShowingByPath(String fullpath, int isShowing) {
        if ((isShowing != 0) && (isShowing != 1)) {
            DebugUtil.i(TAG, "isShowing is not available, not update Db");
            return false;
        }
        if (fullpath == null || fullpath.isEmpty()) {
            DebugUtil.i(TAG, "fullpath is not available, not update Db");
            return false;
        }

        int updateCount = 0;
        ContentValues cv = new ContentValues();
        cv.put(COLUMN_NAME_IS_MARKLIST_SHOWING, isShowing);
        String where = COLUMN_NAME_DATA + "=?";
        try {
            updateCount = mContext.getContentResolver().update(RECORD_CONTENT_URI,
                    cv, where, new String[]{fullpath});
        } catch (Exception e) {
            DebugUtil.e(TAG, "updateRecordIsMarkListShowingByPath error " + e);
        }
        DebugUtil.i(TAG, "updateRecordIsMarkListShowingById fullpath: " + fullpath + ", isShowing = " + isShowing + ", updateCount: " + updateCount);
        return updateCount > 0;
    }


    public boolean updateRecordMarkByPath(String fullpath, String inputString) {
        if (TextUtils.isEmpty(inputString)) {
            DebugUtil.i(TAG, "inputString is empty, not update amp data");
            return false;
        }
        DebugUtil.i(TAG, "updateRecordMarkByPath name: " + FileUtils.getDisplayNameByPath(fullpath) + ", markString " + inputString);
        try {
            ContentValues cv = new ContentValues();
            byte[] markBytes = inputString.getBytes(Charset.forName("UTF-8"));
            cv.put(DatabaseConstant.RecorderColumn.COLUMN_NAME_DIRTY, RECORD_DIRTY_FILE_AND_MEGA);
            cv.put(DatabaseConstant.RecorderColumn.COLUMN_NAME_SYNC_UPLOAD_STATUS, SYNC_STATUS_BACKUP_START);
            cv.put(DatabaseConstant.RecorderColumn.COLUMN_NAME_MARK_DATA, markBytes);
            String where = DatabaseConstant.RecorderColumn.COLUMN_NAME_DATA + " COLLATE NOCASE =?";
            int updateCount = mContext.getContentResolver().update(RECORD_CONTENT_URI, cv, where, new String[]{fullpath});
            return updateCount > 0;
        } catch (Exception e) {
            DebugUtil.e(TAG, "updateRecordMarkById, e=" + e);
        }
        return false;
    }


    public boolean updateRecordMarkById(long id, String inputString) {
        try {
            ContentValues cv = new ContentValues();
            byte[] markBytes = inputString.getBytes(Charset.forName("UTF-8"));
            cv.put(DatabaseConstant.RecorderColumn.COLUMN_NAME_DIRTY, RECORD_DIRTY_FILE_AND_MEGA);
            cv.put(DatabaseConstant.RecorderColumn.COLUMN_NAME_SYNC_UPLOAD_STATUS, SYNC_STATUS_BACKUP_START);
            cv.put(DatabaseConstant.RecorderColumn.COLUMN_NAME_MARK_DATA, markBytes);
            String where = COLUMN_NAME_ID + "=?";
            int updateCount = mContext.getContentResolver().update(RECORD_CONTENT_URI,
                    cv, where, new String[]{String.valueOf(id)});
            return updateCount > 0;
        } catch (Exception e) {
            DebugUtil.e(TAG, "updateRecordMarkById, e=" + e);
        }
        return false;
    }


    /**
     * only delete record db , not delete the file.
     *
     * @param relativePath
     * @param displayName
     * @param fromCloud
     * @return
     */
    public boolean deleteRecordByRelativePathAndDisplayName(String relativePath, String displayName, boolean fromCloud) {
        if (TextUtils.isEmpty(relativePath) || TextUtils.isEmpty(displayName)) {
            return true;
        }
        boolean deleteResult = deleteRecordByRelativePathAndDisplayName(relativePath, displayName);
        DebugUtil.i(TAG, "deleteRecordByRelativePathAndDisplayName input relativepath is " + relativePath + " , input displayName is " + displayName + ", fromCloud is " + fromCloud);
        return deleteResult;
    }

    public boolean deleteRecordByPath(String path) throws Exception {
        if (TextUtils.isEmpty(path)) {
            return true;
        }
        DebugUtil.i(TAG, "deleteRecordByPath input name is " + FileUtils.getDisplayNameByPath(path));
        boolean deleteResult = directDeleteFileAndRecord(path);
        return deleteResult;
    }

    public boolean directDeleteFileAndRecord(String path) throws Exception {
        try {
            String where = COLUMN_NAME_DATA + " COLLATE NOCASE =?";
            String[] whereArgs = new String[]{path};
            Uri recordMediaUri = MediaDBUtils.getMediaUriForAbsolutePath(path);
            if (recordMediaUri != null) {
                int deleteResult = MediaDBUtils.delete(recordMediaUri);
                if (deleteResult > 0) {
                    int deleteCount = deleteRecordData(mContext, where, whereArgs);
                    return deleteCount > 0;
                } else {
                    return false;
                }
            } else {
                int deleteCount = deleteRecordData(mContext, where, whereArgs);
                return deleteCount > 0;
            }
        } catch (Exception e) {
            DebugUtil.e(TAG, "deleteCurrentCursor, The e is " + e);
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                if (e instanceof RecoverableSecurityException) {
                    throw e;
                }
            }
        }
        return false;
    }


    public boolean markRecordAsDeleted(String path) {
        if (TextUtils.isEmpty(path)) {
            DebugUtil.i(TAG, "inputString is empty, not update delete flag");
            return false;
        }
        ContentValues cv = new ContentValues();
        cv.put(DatabaseConstant.RecorderColumn.COLUMN_NAME_DELETE, RECORD_DELETED);
        String where = DatabaseConstant.RecorderColumn.COLUMN_NAME_DATA + " COLLATE NOCASE =?";
        try {
            int updateCount = mContext.getContentResolver().update(RECORD_CONTENT_URI, cv, where, new String[]{path});
            return updateCount > 0;
        } catch (Exception e) {
            DebugUtil.e(TAG, "markRecordAsDeleted error " + e);
        }
        return false;
    }


    /**
     * 删除录音进入到回收站，更新record对应数据
     * @param record
     * @return
     */
    public boolean updateRecycleDataToRecord(Record record, long recordId, String newDisplayName, boolean isCausedByRemoveGroup) {
        if (TextUtils.isEmpty(record.getRecycleFilePath())) {
            DebugUtil.i(TAG, "updateRecycleDataToRecord path is empty.");
            return false;
        }
        ContentValues cv = new ContentValues();
        cv.put(DatabaseConstant.RecorderColumn.COLUMN_NAME_DELETE, RECORD_DELETED);
        cv.put(DatabaseConstant.RecorderColumn.COLUMN_NAME_IS_RECYCLE, RECORD_DIRTY_MEGA_ONLY);
        cv.put(COLUMN_NAME_SYNC_TYPE, RecordConstant.SYNC_TYPE_BACKUP);
        cv.put(COLUMN_NAME_DIRTY, 1); //云同步开关开启后，依靠此字段触发云同步
        cv.put(DatabaseConstant.RecorderColumn.COLUMN_NAME_DELETE_TIME, System.currentTimeMillis());
        cv.put(DatabaseConstant.RecorderColumn.COLUMN_NAME_FILE_RECYCLE_PATH, record.getRecycleFilePath());
        cv.put(COLUMN_NAME_DISPLAY_NAME, newDisplayName);
        cv.put(COLUMN_NAME_DATA, record.getData() + System.currentTimeMillis()); //把data增加一个时间戳，防止 启动全量对比时，record表被删除
        //分组列表界面删除自定义分组时，如果涉及录音记录的删除，需要更新record表的groupId和groupUuid到其对应的默认分组
        if (isCausedByRemoveGroup) {
            cv.put(COLUMN_NAME_GROUP_ID, record.getGroupId());
            cv.put(COLUMN_NAME_GROUP_UUID, record.getGroupUuid());
        }

        String where = COLUMN_NAME_ID + " = ? AND " + COLUMN_NAME_DISPLAY_NAME + " =? ";
        try {
            ContentResolver resolver = mContext.getContentResolver();
            //传递id是为了保证只更新一条数据
            long id = record.getId();
            if (recordId > 0L) {
                id = recordId;
            }
            String[] args = new String[]{String.valueOf(id), record.getDisplayName()};
            int updateCount = resolver.update(RECORD_CONTENT_URI, cv, where, args);
            DebugUtil.d(TAG, "updateCount = " + updateCount + ", ContentValues = " + cv + " args = " + args);
            return updateCount > 0;
        } catch (Exception e) {
            DebugUtil.e(TAG, "markRecordAsDeleted error " + e);
        }
        return false;
    }

    /**
     * 删除录音从回收站恢复到媒体库，更新record对应数据
     * @param record
     * @return
     */
    public boolean updateRecoveryDataToRecord(Record record) {
        if (TextUtils.isEmpty(record.getRecycleFilePath())) {
            DebugUtil.i(TAG, "updateRecycleDataToRecord path is empty.");
            return false;
        }

        /*
        由于第一期没有做云同步，所以重新生成uuid等字段，作为一个新的文件进行备份
        record.setUuid(UUID.randomUUID().toString());
        record.setSyncType(SYNC_TYPE_BACKUP);
        record.setSyncUploadStatus(SYNC_STATUS_BACKUP_START);
         */

        ContentValues cv = new ContentValues();
        cv.put(COLUMN_NAME_DIRTY, RECORD_DIRTY_FILE_AND_MEGA);
        cv.put(COLUMN_NAME_UUID, UUID.randomUUID().toString());
        cv.put(COLUMN_NAME_SYNC_TYPE, SYNC_TYPE_BACKUP);
        cv.put(COLUMN_NAME_SYNC_UPLOAD_STATUS, SYNC_STATUS_BACKUP_START);

        cv.put(DatabaseConstant.RecorderColumn.COLUMN_NAME_DELETE, 0);
        cv.put(DatabaseConstant.RecorderColumn.COLUMN_NAME_IS_RECYCLE, 0);
        cv.put(DatabaseConstant.RecorderColumn.COLUMN_NAME_DELETE_TIME, 0);
        cv.put(DatabaseConstant.RecorderColumn.COLUMN_NAME_FILE_RECYCLE_PATH, "");
        cv.put(COLUMN_NAME_DISPLAY_NAME, record.getDisplayName());
        cv.put(COLUMN_NAME_DATA, record.getData());

        /*
        record被移动到自定义分组后，可能在整个自定义分组删除前被删除到回收站，在被恢复前原来所在的自定分组可能被整组删除
        此时再恢复数据的话，需要根据record的路径分类重置其默认分组：通话分组或者普通分组
         */
        String currentGroupUUID = record.getGroupUuid();
        if (currentGroupUUID != null && GroupInfoDbUtil.isCustomGroup(currentGroupUUID)) {
            boolean isGroupExist = GroupInfoManager.getInstance(mContext).checkGroupExistByUUID(currentGroupUUID);
            if (!isGroupExist) {
                GroupInfoManager.getInstance(mContext).resetGroupInfoForRecord(record);
                cv.put(COLUMN_NAME_GROUP_ID, record.getGroupId());
                cv.put(COLUMN_NAME_GROUP_UUID, record.getGroupUuid());
                DebugUtil.d(TAG, "updateRecoveryDataToRecord, custome group was deleted, resetGroupInfoForRecord");
            }
        }

        if (record.getMimeType() == null) {
            Record mediaRecord = MediaDBUtils.queryRecordByRelativePathAndDisplayName(record.getRelativePath(), record.getDisplayName());
            if (mediaRecord != null) {
                record.mMimeType = mediaRecord.getMimeType();
                cv.put(DatabaseConstant.RecorderColumn.COLUMN_NAME_MIMETYPE, record.mMimeType);
            }
        }

        String where = COLUMN_NAME_ID + " =? ";
        try {
            int updateCount = mContext.getContentResolver().update(RECORD_CONTENT_URI, cv, where, new String[]{String.valueOf(record.getId())});
            DebugUtil.d(TAG, "updateRecoveryDataToRecord updateCount = " + updateCount + ", cv = " + cv);
            return updateCount > 0;
        } catch (Exception e) {
            DebugUtil.e(TAG, "updateRecoveryDataToRecord error " + e);
        }
        return false;
    }

    public synchronized boolean updateRecordCallerNameAvatarColor(Record record, String color, String callerName) {
        if (TextUtils.isEmpty(color) && TextUtils.isEmpty(callerName)) {
            DebugUtil.i(TAG, "updateRecordsCallerNameAvatarColor: params are empty, not update color data");
            return false;
        }
        Record recordInDb = qureyRecordByPath(record.getData());
        if (recordInDb != null) {
            String callerNameFromDb = recordInDb.getCallerName();
            if (color != null && color.equals(recordInDb.getCallAvatarColor())
                    && callerName != null && callerName.equals(callerNameFromDb)) {
                DebugUtil.d(TAG, "updateRecordsCallerNameAvatarColor, not update color data");
                return true;
            }
            String where = COLUMN_NAME_ID + " = ?";
            String[] whereArgs = new String[]{String.valueOf(recordInDb.getId())};
            ContentValues contentValues = new ContentValues();
            contentValues.put(COLUMN_NAME_CALL_AVATAR_COLOR, color);
            //如果数据库里已有联系人名称，则不允许修改
            if (callerNameFromDb == null || TextUtils.isEmpty(callerNameFromDb)) {
                contentValues.put(COLUMN_NAME_CALLER_NAME, callerName);
            }

            if (record.getOriginalName() != null) {
                contentValues.put(COLUMN_NAME_ORIGINAL_NAME, record.getOriginalName());
            }

            int updateCount = mContext.getContentResolver().update(RECORD_CONTENT_URI,
                    contentValues, where, whereArgs);
            DebugUtil.d(TAG, "updateRecordsCallerNameAvatarColor updateCount = " + updateCount + ", cv = " + contentValues);
            return updateCount > 0;
        }
        return false;
    }

    private static boolean displayNameStartWithWxOrQQ(String displayName) {
        if (TextUtils.isEmpty(displayName)) {
            return false;
        }
        String thirdWxPrefix = BaseApplication.getAppContext().getString(R.string.tencentName);
        String thirdQQPrefix = BaseApplication.getAppContext().getString(R.string.qqName);
        if (displayName.startsWith(thirdWxPrefix) || displayName.startsWith(thirdQQPrefix)) {
            return true;
        }
        String wxPrefix = BaseApplication.getAppContext().getString(R.string.support_app_name_wechat);
        String qqPrefix = BaseApplication.getAppContext().getString(R.string.support_app_name_qq);
        if (displayName.startsWith(wxPrefix) || displayName.startsWith(WECHAT_EXP_PREFIX)
                || displayName.startsWith(qqPrefix)) {
            return true;
        }
        return false;
    }

    /**
     * 1,先解析文件末尾的联系人名字，如果能拿到就直接用这个作为callerName。
     * 2,如果拿不到，再按照我们现在的三段式或者两段式标题解析联系人名字.
     * 注：相较于本地截取“-”、“_”解析，通过文件去拿取callerName，稍显耗时。ps：几毫秒 和 几毫米~几十毫秒的差别。
     */
    public static String getCallerName(Record record, Long mediaId) {
        PlayBackInterface playBackInterface = KoinInterfaceHelper.INSTANCE.getPlaybackAction();
        String callerName = playBackInterface.parseCallName(getAppContext(), record.getData(), mediaId, record.mMimeType);
        if (callerName == null) {
            if (TextUtils.isEmpty(record.getOriginalName())) {
                // 文件初次加入，还未将originalName插入db，使用displayName解析。
                callerName = getCallerNameByCustom(record.getDisplayName());
            } else {
                callerName = getCallerNameByCustom(record.getOriginalName());
            }
        }
        return callerName;
    }

    public static String getCallerNameByCustom(String name) {
        if (TextUtils.isEmpty(name)) {
            DebugUtil.e(TAG, "displayName is empty !");
            return null;
        }
        String firstChar = CircleTextImageUtil.subFirstCharacter(name);
        if (SubRecorderTextUtils.containsEmoji(firstChar)
                || SubRecorderTextUtils.containsIllegalCharFileName(firstChar)) {
            DebugUtil.d(TAG, "getCallerNameByDisplayname, firstChar:" + firstChar);
            return BaseApplication.getAppContext().getString(R.string.unknown);
        }
        String callerName = BaseApplication.getAppContext().getString(R.string.unknown);
        if (name.contains("-")) {
            String[] callerNames = name.split("-");
            if (callerNames != null && callerNames.length > 0) {
                if (callerNames.length > 2 && displayNameStartWithWxOrQQ(name)) {
                    callerName = callerNames[1];
                } else {
                    callerName = callerNames[0];
                }
            }
        } else if (name.contains("_")) {
            String[] callerNames = name.split("_");
            if (callerNames != null && callerNames.length > 0 && displayNameStartWithWxOrQQ(name)) {
                String[] childNames = callerNames[0].split(" ");
                if (childNames != null && childNames.length > 0) {
                    if (childNames.length > 1) {
                        callerName = childNames[1];
                    } else {
                        callerName = childNames[0];
                    }
                }
            }
        } else {
            callerName = BaseApplication.getAppContext().getString(R.string.unknown);
        }
        return callerName;
    }

    public boolean markRecordAsDeleted(String relativePath, String displayName) {
        if (TextUtils.isEmpty(relativePath) || TextUtils.isEmpty(displayName)) {
            DebugUtil.i(TAG, "relativePath is empty or displayname is empty, not update delete flag");
            return false;
        }
        try {
            ContentValues cv = new ContentValues();
            cv.put(DatabaseConstant.RecorderColumn.COLUMN_NAME_DELETE, RECORD_DELETED);
            String where = COLUMN_NAME_RELATIVE_PATH + " COLLATE NOCASE = ? AND " + COLUMN_NAME_DISPLAY_NAME + " = ?";
            int updateCount = mContext.getContentResolver().update(RECORD_CONTENT_URI,
                    cv, where, new String[]{relativePath, displayName});
            DebugUtil.i(TAG, "markRecordAsDeleted relativePath : " + relativePath + ", displayName: "
                    + displayName + ", updateCount: " + updateCount, true);
            return updateCount > 0;
        } catch (Exception e) {
            DebugUtil.e(TAG, "markRecordAsDeleted error " + e);
        }
        return false;
    }

    public boolean deleteRecordByRelativePathAndDisplayName(String relativePath, String displayName) {
        if (TextUtils.isEmpty(relativePath) || TextUtils.isEmpty(displayName)) {
            DebugUtil.i(TAG, "relativePath is empty or displayname is empty, not update delete flag");
            return false;
        }
        String where = COLUMN_NAME_RELATIVE_PATH + " COLLATE NOCASE = ? AND " + COLUMN_NAME_DISPLAY_NAME + " = ?";
        int updateCount = deleteRecordData(mContext, where, new String[]{relativePath, displayName});
        return updateCount > 0;
    }

    public boolean deleteRecordByPathExcludeAudioFile(String path) {
        if (TextUtils.isEmpty(path)) {
            DebugUtil.i(TAG, "path is empty ,not update delete flag");
            return false;
        }
        String where = COLUMN_NAME_DATA + " COLLATE NOCASE = ?";
        int updateCount = deleteRecordData(mContext, where, new String[]{path});
        return updateCount > 0;
    }


    public Record qureyRecordByPath(String path) {
        if (TextUtils.isEmpty(path)) {
            DebugUtil.i(TAG, "inputString is empty, can not find the record");
            return null;
        }
        String where = DatabaseConstant.RecorderColumn.COLUMN_NAME_DATA + " COLLATE NOCASE =?";
        Cursor cursor = null;
        Record record = null;
        try {
            cursor = mContext.getContentResolver().query(RECORD_CONTENT_URI, RECDOORD_PROJECTION, where, new String[]{path}, RECORDER_QUERY_ORDER);
            if ((cursor != null) && (cursor.getCount() > 0)) {
                cursor.moveToFirst();
                record = new Record(cursor, Record.TYPE_FROM_RECORD);
            }
            return record;
        } catch (Exception e) {
            DebugUtil.e(TAG, "qureyRecordByPath : " + e.getMessage(), e);
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return null;
    }

    /**
     * 获取非回收站的去重后的所有记录分组信息
     * @return
     */
    public HashMap<String, Integer> queryAllRecordsGroupTypeWithoutRecycleBin() {
        String where = DatabaseConstant.RecorderColumn.COLUMN_NAME_IS_RECYCLE + " != ? AND " + COLUMN_NAME_SIZE + " > 0";
        Cursor cursor = null;
        HashMap<String, Integer> groupTypeMap = new HashMap<>();
        try {
            String[] projection = new String[]{"DISTINCT " + COLUMN_NAME_DATA, COLUMN_NAME_GROUP_UUID};
            cursor = mContext.getContentResolver().query(RECORD_CONTENT_URI, projection,
                    where, new String[]{"1"}, RECORDER_QUERY_ORDER);
            if ((cursor != null) && (cursor.getCount() > 0)) {
                int dataIndex = cursor.getColumnIndex(MediaStore.Audio.Media.DATA);
                int groupUuidIndex = cursor.getColumnIndex(COLUMN_NAME_GROUP_UUID);
                while (cursor.moveToNext()) {
                    if (dataIndex >= 0 && groupUuidIndex >= 0) {
                        String filePath = cursor.getString(dataIndex);
                        String groupUuid = cursor.getString(groupUuidIndex);
                        int groupType = GroupInfoDbUtil.getGroupTypeByGroupUUID(groupUuid);
                        groupTypeMap.put(filePath, groupType);
                    }
                }
            }
            return groupTypeMap;
        } catch (Exception e) {
            DebugUtil.e(TAG, "queryAllRecordsGroupTypeWithoutRecycleBin: " + e.getMessage(), e);
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return null;
    }

    public List<Record> queryAllRecordsWithoutRecycleBin() {
        String where = DatabaseConstant.RecorderColumn.COLUMN_NAME_IS_RECYCLE + " != ? AND " + COLUMN_NAME_SIZE + " > 0";
        Cursor cursor = null;
        Record record = null;
        List<Record> list = new ArrayList<>();
        try {
            String[] projection = DatabaseConstant.getDistinctRecordProjection(COLUMN_NAME_DATA);
            cursor = mContext.getContentResolver().query(RECORD_CONTENT_URI, projection,
                    where, new String[]{"1"}, RECORDER_QUERY_ORDER);
            if ((cursor != null) && (cursor.getCount() > 0)) {
                while (cursor.moveToNext()) {
                    record = new Record(cursor, Record.TYPE_FROM_RECORD);
                    list.add(record);
                }
            }
            return list;
        } catch (Exception e) {
            DebugUtil.e(TAG, "queryAllRecordsWithoutRecycleBin: " + e.getMessage(), e);
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return null;
    }

    public List<Record> queryAllRecycleRecords() {
        String where = DatabaseConstant.RecorderColumn.COLUMN_NAME_IS_RECYCLE + " = ?";
        Cursor cursor = null;
        Record record = null;
        List<Record> list = new ArrayList<>();
        try {
            cursor = mContext.getContentResolver().query(RECORD_CONTENT_URI, RECDOORD_PROJECTION,
                    where, new String[]{"1"}, RECORDER_QUERY_ORDER);
            if ((cursor != null) && (cursor.getCount() > 0)) {
                while (cursor.moveToNext()) {
                    record = new Record(cursor, Record.TYPE_FROM_RECORD);
                    list.add(record);
                }
            }
            return list;
        } catch (Exception e) {
            DebugUtil.e(TAG, "getAllRecycleRecords: " + e.getMessage(), e);
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return null;
    }

    public Record qureyRecycleRecordByPath(String recyclePath) {
        if (TextUtils.isEmpty(recyclePath)) {
            DebugUtil.i(TAG, "inputString is empty, can not find the record");
            return null;
        }
        String where = COLUMN_NAME_FILE_RECYCLE_PATH + " =?";
        Cursor cursor = null;
        Record record = null;
        try {
            cursor = mContext.getContentResolver().query(RECORD_CONTENT_URI, RECDOORD_PROJECTION,
                    where, new String[]{recyclePath}, RECORDER_QUERY_ORDER);
            if ((cursor != null) && (cursor.getCount() > 0)) {
                cursor.moveToFirst();
                record = new Record(cursor, Record.TYPE_FROM_RECORD);
            }
            return record;
        } catch (Exception e) {
            DebugUtil.e(TAG, "qureyRecycleRecordByPath : " + e.getMessage(), e);
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return null;
    }

    public Record getRecordFromDBbyMediaUri(Uri mediaUri) {
        if (mediaUri == null) {
            return null;
        }
        Record result = null;
        Record recordFromMedia = MediaDBUtils.getRecordFromMediaByUriId(mediaUri);
        if (recordFromMedia != null) {
            String relativePath = recordFromMedia.getRelativePath();
            String disPlayName = recordFromMedia.getDisplayName();
            result = getRecordByRelativePathAndDisplayName(relativePath, disPlayName);
        }
        return result;
    }

    public Record getRecordByRelativePathAndDisplayName(String relativePath, String displayName) {
        Record record = null;
        if (TextUtils.isEmpty(relativePath) || TextUtils.isEmpty(displayName)) {
            DebugUtil.e(TAG, "input fileId is null");
            return null;
        }
        String where = COLUMN_NAME_RELATIVE_PATH + " COLLATE NOCASE = ? AND " + COLUMN_NAME_DISPLAY_NAME + " = ?";
        List<Record> result = getSimpleRecordData(mContext, null, where, relativePath, displayName, null);
        if ((result != null) && (!result.isEmpty())) {
            DebugUtil.i(TAG, "getRecordByRelativePathAndDisplayName result.size " + result.size());
            for (Record record1 : result) {
                // 优先返回数据库中size > 0 的
                if (record1.getFileSize() > 0) {
                    return record1;
                }
            }
            record = result.get(0);
        }
        return record;
    }

    /**
     * SQL注入风险
     * @param context
     * @param projection
     * @param selection
     * @param relativePath
     * @param displayName
     * @param order
     * @return
     */
    public static List<Record> getSimpleRecordData(Context context, String[] projection, String selection,
                                                   String relativePath, String displayName, String order) {
        if (context == null) {
            DebugUtil.i(TAG, "inputString is empty, can not find the record");
            return null;
        }
        Cursor cursor = null;
        List<Record> result = new ArrayList<>();
        try {
            Bundle queryBundle = createSqlQueryBundle(selection, new String[]{relativePath, displayName}, order, null, null);
            cursor = context.getContentResolver().query(RECORD_CONTENT_URI,
                    projection, queryBundle, null);
            if ((cursor != null) && (cursor.getCount() > 0)) {
                while (cursor.moveToNext()) {
                    Record record = new Record(cursor, Record.TYPE_FROM_RECORD);
                    result.add(record);
                }
            }
        } catch (Exception e) {
            DebugUtil.e(TAG, "qureyRecordBySame : " + e.getMessage(), e);
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return result;
    }

    public Record qureyRecordByPathAndMd5(String path, String mD5) {
        if (TextUtils.isEmpty(path)) {
            DebugUtil.i(TAG, "inputString is empty, can not find the record");
            return null;
        }
        String where = DatabaseConstant.RecorderColumn.COLUMN_NAME_DATA + " COLLATE NOCASE =? and " + DatabaseConstant.RecorderColumn.COLUMN_NAME_MD5 + "=?";
        Cursor cursor = null;
        Record record = null;
        try {
            ContentResolver resolver = mContext.getContentResolver();
            cursor = resolver.query(RECORD_CONTENT_URI, RECDOORD_PROJECTION, where, new String[]{path, mD5}, RECORDER_QUERY_ORDER);
            if ((cursor != null) && (cursor.getCount() > 0)) {
                cursor.moveToFirst();
                record = new Record(cursor, Record.TYPE_FROM_RECORD);
            }
            return record;
        } catch (Exception e) {
            DebugUtil.e(TAG, "qureyRecordByPath : " + e.getMessage(), e);
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return null;
    }


    public String getAmpStringByPath(String path, boolean isRecycle) {
        if (TextUtils.isEmpty(path)) {
            DebugUtil.i(TAG, "inputString is empty, can not find the record");
            return null;
        }
        String where = null;
        if (isRecycle) {
            where = DatabaseConstant.RecorderColumn.COLUMN_NAME_FILE_RECYCLE_PATH + " COLLATE NOCASE =?";
        } else  {
            where = DatabaseConstant.RecorderColumn.COLUMN_NAME_DATA + " COLLATE NOCASE =?";
        }

        Cursor cursor = null;
        String ampString = null;
        try {
            cursor = mContext.getContentResolver().query(RECORD_CONTENT_URI,
                    RECDOORD_PROJECTION, where, new String[]{path}, RECORDER_QUERY_ORDER);
            if ((cursor != null) && (cursor.getCount() > 0)) {
                cursor.moveToFirst();
                String ampFilePath = "";
                int filePathIndex = cursor.getColumnIndex(COLUMN_NAME_AMP_FILE_PATH);
                if (filePathIndex >= 0) {
                    ampFilePath = cursor.getString(filePathIndex);
                }
                if (!TextUtils.isEmpty(ampFilePath)) {
                    ampString = AmpFileUtil.parseAmpFile(mContext, ampFilePath);
                }
            }
            return ampString;
        } catch (Exception e) {
            DebugUtil.e(TAG, "qureyRecordByPath,  path = " + path + ", error: " + e.getMessage(), e);
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return null;
    }

    /**
     * check 本地是否有音频文件的波形缓存数据
     * @param path 音频文件的全路径
     * @return
     */
    public boolean checkLocalHasAmp(String path) {
        if (TextUtils.isEmpty(path)) {
            DebugUtil.i(TAG, "inputString is empty, can not find the record");
            return false;
        }
        String where = DatabaseConstant.RecorderColumn.COLUMN_NAME_DATA + " COLLATE NOCASE =?";
        String[] projection = new String[]{COLUMN_NAME_AMP_FILE_PATH};
        String[] args = new String[]{path};
        Cursor cursor = null;
        try {
            cursor = mContext.getContentResolver().query(RECORD_CONTENT_URI, projection,
                    where, args, RECORDER_QUERY_ORDER);
            if ((cursor != null) && (cursor.getCount() > 0)) {
                cursor.moveToFirst();
                String ampFilePath = "";
                int filePathIndex = cursor.getColumnIndex(COLUMN_NAME_AMP_FILE_PATH);
                if (filePathIndex >= 0) {
                    ampFilePath = cursor.getString(filePathIndex);
                }
                return !TextUtils.isEmpty(ampFilePath);
            }
        } catch (Exception e) {
            DebugUtil.e(TAG, "checkLocalHasAmp : " + e.getMessage(), e);
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return false;
    }

    public String getMarkStringByPath(String path) {
        if (TextUtils.isEmpty(path)) {
            DebugUtil.i(TAG, "inputString is empty, can not find the record");
            return null;
        }
        String where = DatabaseConstant.RecorderColumn.COLUMN_NAME_DATA + " COLLATE NOCASE =?";
        Cursor cursor = null;
        String markString = null;
        try {
            cursor = mContext.getContentResolver().query(RECORD_CONTENT_URI, RECDOORD_PROJECTION, where, new String[]{path}, RECORDER_QUERY_ORDER);
            if ((cursor != null) && (cursor.getCount() > 0)) {
                cursor.moveToFirst();
                byte[] markData = null;
                int markDataIndex = cursor.getColumnIndex(COLUMN_NAME_MARK_DATA);
                if (markDataIndex >= 0) {
                    markData = cursor.getBlob(markDataIndex);
                }
                if (markData != null) {
                    DebugUtil.i(TAG, "markData size : " + markData.length);
                    markString = new String(markData, Charset.forName("UTF-8"));
                }
            }
            return markString;
        } catch (Exception e) {
            DebugUtil.e(TAG, "qureyRecordByPath : " + e.getMessage(), e);
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return null;
    }

    public static String getKeyIdByPath(String path) {
        String keyId = "";
        if (TextUtils.isEmpty(path)) {
            DebugUtil.i(TAG, "inputString is empty, can not find the record");
            return keyId;
        }
        String[] projection = new String[]{COLUMN_NAME_ID};
        String selection = DatabaseConstant.RecorderColumn.COLUMN_NAME_DATA + " COLLATE NOCASE =?";
        String[] selectionArg = new String[]{path};
        Cursor cursor = null;
        try {
            cursor = BaseApplication.getAppContext().getContentResolver().query(RECORD_CONTENT_URI,
                    projection, selection, selectionArg, RECORDER_QUERY_ORDER);
            if ((cursor != null) && (cursor.getCount() > 0)) {
                int idIndex = cursor.getColumnIndex(COLUMN_NAME_ID);
                cursor.moveToFirst();
                if (idIndex >= 0) {
                    keyId = String.valueOf(cursor.getLong(idIndex));
                }
            }
        } catch (Exception e) {
            DebugUtil.e(TAG, "getKeyIdByPath : " + e.getMessage(), e);
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return keyId;
    }

    public static String getKeyIdByPath(String relativePath, String displayName) {
        String keyId = "";
        if (TextUtils.isEmpty(relativePath) || TextUtils.isEmpty(displayName)) {
            DebugUtil.i(TAG, "relativePath or displayName is empty, can not find the record");
            return keyId;
        }
        Record record = getInstance(BaseApplication.getAppContext()).getRecordByRelativePathAndDisplayName(relativePath, displayName);
        if (record != null) {
            keyId = String.valueOf(record.getId());
        }
        return keyId;
    }

    public long getCreateTimeByPath(long id, boolean isAmr) {
        if (id == -1) {
            DebugUtil.i(TAG, "inputString is empty, can not find the record");
            return 0;
        }
        Uri queryUri = MediaDBUtils.BASE_URI;
        String where = MediaStore.Audio.Media._ID + "=?";
        Cursor cursor = null;
        long createTime = 0;
        long modifyTime = 0;
        try {
            cursor = mContext.getContentResolver().query(queryUri, null, where, new String[]{String.valueOf(id)}, null);
            if ((cursor != null) && (cursor.getCount() > 0)) {
                cursor.moveToFirst();
                int dateAddIndex = cursor.getColumnIndex(MediaStore.Audio.Media.DATE_ADDED);
                if (dateAddIndex >= 0) {
                    createTime = cursor.getLong(dateAddIndex);
                }
                int modifyTimeIndex = cursor.getColumnIndex(MediaStore.Audio.Media.DATE_MODIFIED);
                if (modifyTimeIndex >= 0) {
                    modifyTime = cursor.getLong(modifyTimeIndex);
                }
            }
            //矫正云同步下来后文件createTime不能修改为新时间，而modifyTime为上传云时的时间（老时间）
            if (modifyTime > 0 && createTime > modifyTime) {
                createTime = modifyTime;
            }
            return createTime * NumberConstant.NUM_1000;
        } catch (Exception e) {
            DebugUtil.e(TAG, "qureyRecordByPath : " + e.getMessage(), e);
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return 0;
    }


    public Record qureyRecordByUUid(String uuid) {
        if (TextUtils.isEmpty(uuid)) {
            DebugUtil.i(TAG, "inputString is empty, can not find the record");
            return null;
        }
        String where = DatabaseConstant.RecorderColumn.COLUMN_NAME_UUID + "=?";
        Cursor cursor = null;
        Record record = null;
        try {
            cursor = mContext.getContentResolver().query(RECORD_CONTENT_URI, RECDOORD_PROJECTION, where, new String[]{uuid}, RECORDER_QUERY_ORDER);
            if ((cursor != null) && (cursor.moveToFirst())) {
                record = new Record(cursor, Record.TYPE_FROM_RECORD);
                DebugUtil.i(TAG, "qureyRecordByUUid : record:" + record + ", cursor count: " + cursor.getCount());
            }
            return record;
        } catch (Exception e) {
            DebugUtil.e(TAG, "qureyRecordByPath : " + e.getMessage(), e);
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return null;
    }


    public long qureyAllVisibleRecordsCount() {
        String where = COLUMN_NAME_DELETE + " != " + RECORD_DELETED;
        Cursor cursor = null;
        try {
            cursor = mContext.getContentResolver().query(RECORD_CONTENT_URI, RECDOORD_PROJECTION, where, null, RECORDER_QUERY_ORDER);
            if (cursor != null) {
                return cursor.getCount();
            }
        } catch (Exception e) {
            DebugUtil.e(TAG, "qureyRecordByPath : " + e.getMessage(), e);
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return 0;
    }

    public boolean hasDirtyData() {
        String where = "(" + COLUMN_NAME_DIRTY + " != " + RECORD_NOT_DIRTY + " or " + COLUMN_NAME_DELETE + " = " + RECORD_DELETED + ")"
                + " and " + COLUMN_NAME_LOCAL_EDIT_STATUS + " != " + LOCAL_EDITING;
        Cursor cursor = null;
        try {
            cursor = mContext.getContentResolver().query(RECORD_CONTENT_URI, RECDOORD_PROJECTION, where, null, RECORDER_QUERY_ORDER);
            if (cursor != null) {
                return cursor.getCount() > 0;
            }
        } catch (Exception e) {
            DebugUtil.e(TAG, "qureyRecordByPath : " + e.getMessage(), e);
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return false;
    }

    /**
     * STEP3
     * 待上传文件数据
     *
     * @return
     */
    public List<Record> getDirtyDataForBatchUploadFile() {
        String where = COLUMN_NAME_DIRTY + " != " + RECORD_NOT_DIRTY + " and " + COLUMN_NAME_LOCAL_EDIT_STATUS + " != " + LOCAL_EDITING + " and ("
                + COLUMN_NAME_FILE_ID + " is null or " + COLUMN_NAME_FILE_ID + " = '')" + "and (" + COLUMN_NAME_SYNC_UPLOAD_STATUS + " = "
                + SYNC_STATUS_BACKUP_START + " or " + COLUMN_NAME_SYNC_UPLOAD_STATUS + " = " + SYNC_STATUS_BACKUP_FILE_FAILED + ")"
                + " and (" + MediaStore.Audio.Media.MIME_TYPE + " in ('" + MIMETYPE_AMR + "', '" + MIMETYPE_WAV + "', '" + MIMETYPE_MP3
                + "', '" + MIMETYPE_AMR_WB + "', '" + MIMETYPE_ACC_ADTS + "', '" + MIMETYPE_ACC + "'))";
        String order = COLUMN_NAME_SIZE + " ASC" + "," + COLUMN_NAME_DATE_CREATED + " DESC";
        List<Record> dirtyDatas = new ArrayList<>();
        Cursor cursor = null;
        try {
            cursor = mContext.getContentResolver().query(RECORD_CONTENT_URI, RECDOORD_PROJECTION, where, null, order);
            if ((cursor != null) && (cursor.getCount() > 0)) {
                while (cursor.moveToNext()) {
                    Record record = new Record(cursor, Record.TYPE_FROM_RECORD);
                    dirtyDatas.add(record);
                }
            }
        } catch (Exception e) {
            DebugUtil.e(TAG, "qureyRecordByPath : " + e.getMessage(), e);
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return dirtyDatas;
    }

    /**
     * STEP1:查询 已上传文件，待上传元数据db（无globalId、有fileId）- create
     *
     * @return
     */
    public List<Record> getDirtyDataForUnCommitMetadata() {
        String dirtyWhere = "(" + COLUMN_NAME_DIRTY + " != " + RECORD_NOT_DIRTY + " and " + COLUMN_NAME_LOCAL_EDIT_STATUS + " != " + LOCAL_EDITING + " and "
                + COLUMN_NAME_FILE_ID + " not null and " + COLUMN_NAME_FILE_ID + " !='' and ("
                + " " + COLUMN_NAME_GLOBAL_ID + " is null or  " + COLUMN_NAME_GLOBAL_ID + "  = ''))"
                // 加这个是为了处理老版本本地删除数据（globalId为空fileId 不为空）也没有将本地记录删除
                + " and " + COLUMN_NAME_DELETE + "!=" + RECORD_DELETED;
        String fileSizeWhere = COLUMN_NAME_SIZE + " > 0";
        String mimeType = COLUMN_NAME_MIMETYPE + CursorHelper.getsAcceptableAudioTypesSQL();
        String where = dirtyWhere + " and " + fileSizeWhere + " and " + mimeType;
        String[] args = CursorHelper.getsAcceptableAudioTypes();
        List<Record> dirtyDatas = new ArrayList<>();
        Cursor cursor = null;
        try {
            cursor = mContext.getContentResolver().query(RECORD_CONTENT_URI, RECDOORD_PROJECTION, where, args, RECORDER_QUERY_ORDER);
            if ((cursor != null) && (cursor.getCount() > 0)) {
                while (cursor.moveToNext()) {
                    Record record = new Record(cursor, Record.TYPE_FROM_RECORD);
                    dirtyDatas.add(record);
                }
            }
        } catch (Exception e) {
            DebugUtil.e(TAG, "qureyRecordByPath : " + e.getMessage(), e);
        } finally {
            DebugUtil.d(TAG, "query dirtyDataForUnCommitMetadata:" + dirtyDatas.size());
            if (cursor != null) {
                cursor.close();
            }
        }
        return dirtyDatas;
    }

    /**
     * STEP2:
     * 已同步过的元数据，有globalId\fileId
     * 本地元数据需更新-update/delete
     *
     * @return
     */
    public List<Record> getDirtyDataForBatchUploadMegaData() {
        String dirtyWhere = "(" + COLUMN_NAME_DIRTY + " != " + RECORD_NOT_DIRTY + " and " + COLUMN_NAME_LOCAL_EDIT_STATUS + " != " + LOCAL_EDITING + " and "
                + COLUMN_NAME_GLOBAL_ID + " not null and " + COLUMN_NAME_GLOBAL_ID + " !='' )";
        String deleteWhere = "(" + COLUMN_NAME_DELETE + " = " + RECORD_DELETED + " and " + COLUMN_NAME_LOCAL_EDIT_STATUS + " != " + LOCAL_EDITING + ")";
        String encryptWhere = "(" + COLUMN_NAME_DIRTY + " != " + RECORD_NOT_DIRTY + " and " + COLUMN_NAME_LOCAL_EDIT_STATUS + " != " + LOCAL_EDITING + " and "
                + COLUMN_NAME_PRIVATE_STATUS + " = " + RECORD_PRIVETE_ENCRYPT + ")";
        String fileSizeWhere = COLUMN_NAME_SIZE + " > 0";
        String mimeType = COLUMN_NAME_MIMETYPE + CursorHelper.getsAcceptableAudioTypesSQL();
        String where = dirtyWhere + " or " + deleteWhere + " or " + encryptWhere + " and " + fileSizeWhere + " and " + mimeType;
        String[] args = CursorHelper.getsAcceptableAudioTypes();
        List<Record> dirtyDatas = new ArrayList<>();
        Cursor cursor = null;
        try {
            cursor = mContext.getContentResolver().query(RECORD_CONTENT_URI, RECDOORD_PROJECTION, where, args, RECORDER_QUERY_ORDER);
            if ((cursor != null) && (cursor.getCount() > 0)) {
                while (cursor.moveToNext()) {
                    Record record = new Record(cursor, Record.TYPE_FROM_RECORD);
                    DebugUtil.e(TAG, "getDirtyDataForBatchUploadMegaData  record =  " + record.toString());

                    dirtyDatas.add(record);
                }
            }
        } catch (Exception e) {
            DebugUtil.e(TAG, "qureyRecordByPath : " + e.getMessage(), e);
        } finally {
            DebugUtil.d(TAG, "query dirtyDataForBatchUploadMegaData:" + dirtyDatas.size());
            if (cursor != null) {
                cursor.close();
            }
        }
        return dirtyDatas;
    }

    public List<Record> getDirtyDataForBatchUploadMegaDataForOldCloud() {
        String dirtyWhere = "(" + COLUMN_NAME_DIRTY + " != " + RECORD_NOT_DIRTY + " and " + COLUMN_NAME_LOCAL_EDIT_STATUS + " != " + LOCAL_EDITING + " and "
                + COLUMN_NAME_FILE_ID + " not null and " + COLUMN_NAME_FILE_ID + " !='' )";
        String deleteWhere = "(" + COLUMN_NAME_DELETE + " = " + RECORD_DELETED + " and " + COLUMN_NAME_LOCAL_EDIT_STATUS + " != " + LOCAL_EDITING + ")";
        String encryptWhere = "(" + COLUMN_NAME_DIRTY + " != " + RECORD_NOT_DIRTY + " and " + COLUMN_NAME_LOCAL_EDIT_STATUS + " != " + LOCAL_EDITING + " and "
                + COLUMN_NAME_PRIVATE_STATUS + " = " + RECORD_PRIVETE_ENCRYPT + ")";
        String fileSizeWhere = COLUMN_NAME_SIZE + " > 0";
        String mimeType = COLUMN_NAME_MIMETYPE + CursorHelper.getsAcceptableAudioTypesSQL();
        String where = dirtyWhere + " or " + deleteWhere + " or " + encryptWhere + " and " + fileSizeWhere + " and " + mimeType;
        String[] args = CursorHelper.getsAcceptableAudioTypes();
        List<Record> dirtyDatas = new ArrayList<>();
        Cursor cursor = null;
        try {
            cursor = mContext.getContentResolver().query(RECORD_CONTENT_URI, RECDOORD_PROJECTION, where, args, RECORDER_QUERY_ORDER);
            if ((cursor != null) && (cursor.getCount() > 0)) {
                while (cursor.moveToNext()) {
                    Record record = new Record(cursor, Record.TYPE_FROM_RECORD);
                    dirtyDatas.add(record);
                }
            }
        } catch (Exception e) {
            DebugUtil.e(TAG, "qureyRecordByPath : " + e.getMessage(), e);
        } finally {
            DebugUtil.d(TAG, "query dirtyDataForBatchUploadMegaData:" + dirtyDatas.size());
            if (cursor != null) {
                cursor.close();
            }
        }
        return dirtyDatas;
    }


    public List<Record> getDataForBatchDownloadFile() {
        String where = COLUMN_NAME_SYNC_TYPE + " = ? and " + COLUMN_NAME_LOCAL_EDIT_STATUS + " != ? and "
                + COLUMN_NAME_GLOBAL_ID + " not null and " + COLUMN_NAME_GLOBAL_ID + " != '' and "
                + COLUMN_NAME_FILE_ID + " !='' and " + COLUMN_NAME_FILE_ID + " not null and "
                + COLOUM_NAME_SYNC_DOWNLOAD_STATUS + " in (?,?,?,?) ";
        String[] selectionArgs = new String[]{
                String.valueOf(SYNC_TYPE_RECOVERY),
                String.valueOf(LOCAL_EDITING),
                String.valueOf(SYNC_STATUS_RECOVERY_MEGADATA_SUC),
                String.valueOf(SYNC_STATUS_RECOVERY_FILE_FAILED),
                String.valueOf(SYNC_STATUS_LOCALLY_EXISTS_METADATA),
                String.valueOf(SYNC_STATUS_RECOVERY_FILE_JUMP)
        };
        String order = COLUMN_NAME_SIZE + " ASC" + "," + COLUMN_NAME_DATE_CREATED + " DESC";
        List<Record> downloadDatas = new ArrayList<>();
        Cursor cursor = null;
        try {
            cursor = mContext.getContentResolver().query(RECORD_CONTENT_URI, RECDOORD_PROJECTION, where, selectionArgs, order);
            if ((cursor != null) && (cursor.getCount() > 0)) {
                while (cursor.moveToNext()) {
                    Record record = new Record(cursor, Record.TYPE_FROM_RECORD);
                    downloadDatas.add(record);
                }
            }
        } catch (Exception e) {
            DebugUtil.e(TAG, "qureyRecordByPath : " + e.getMessage(), e);
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return downloadDatas;
    }

    public List<Record> getDataForBatchDownloadFile(List<String> globalIdList) {
        if ((globalIdList == null) || (globalIdList.isEmpty())) {
            DebugUtil.i(TAG, "getDataForBatchDownloadFile, list is empty");
            return new ArrayList<>();
        }
        DebugUtil.i(TAG, "getDataForBatchDownloadFile byGlocalId, " + globalIdList.size());
        String whereGlobalId = COLUMN_NAME_GLOBAL_ID + " not null and " + COLUMN_NAME_GLOBAL_ID + " != '' ";
        List<String> selectionArgs = new ArrayList<>();
        String globalWhereStr = "";
        for (String globalId : globalIdList) {
            selectionArgs.add(globalId);
            globalWhereStr += "?,";
        }
        whereGlobalId = COLUMN_NAME_GLOBAL_ID + " in (" + globalWhereStr.substring(0, globalWhereStr.length() - 1) + ") ";
        String where = COLUMN_NAME_SYNC_TYPE + " = ? and " + COLUMN_NAME_LOCAL_EDIT_STATUS + " != ? and "
                + whereGlobalId + " and "
                + COLUMN_NAME_FILE_ID + " !='' and " + COLUMN_NAME_FILE_ID + " not null and "
                + COLOUM_NAME_SYNC_DOWNLOAD_STATUS + " in (?,?,?,?) ";
        selectionArgs.add(0, String.valueOf(SYNC_TYPE_RECOVERY));
        selectionArgs.add(1, String.valueOf(LOCAL_EDITING));
        selectionArgs.add(String.valueOf(SYNC_STATUS_RECOVERY_MEGADATA_SUC));
        selectionArgs.add(String.valueOf(SYNC_STATUS_RECOVERY_FILE_FAILED));
        selectionArgs.add(String.valueOf(SYNC_STATUS_LOCALLY_EXISTS_METADATA));
        selectionArgs.add(String.valueOf(SYNC_STATUS_RECOVERY_FILE_JUMP));
        String order = COLUMN_NAME_SIZE + " ASC" + "," + COLUMN_NAME_DATE_CREATED + " DESC";
        List<Record> downloadDatas = new ArrayList<>();
        Cursor cursor = null;
        try {
            cursor = mContext.getContentResolver().query(RECORD_CONTENT_URI, RECDOORD_PROJECTION
                    , where, (String[]) selectionArgs.toArray(new String[0]), order);
            if ((cursor != null) && (cursor.getCount() > 0)) {
                DebugUtil.i(TAG, "getDataForBatchDownloadFile count is " + cursor.getCount());
                while (cursor.moveToNext()) {
                    Record record = new Record(cursor, Record.TYPE_FROM_RECORD);
                    downloadDatas.add(record);
                }
            }
        } catch (Exception e) {
            DebugUtil.e(TAG, "qureyRecordByPath : " + e.getMessage(), e);
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return downloadDatas;
    }

    public void checkAndUpdateRecordsMimeTypeIssue() {
        String where = COLUMN_NAME_MIMETYPE + " ='' or " + COLUMN_NAME_MIMETYPE + " is null";
        List<Record> issueRecordList = new ArrayList<>();
        Cursor cursor = null;
        try {
            cursor = mContext.getContentResolver().query(RECORD_CONTENT_URI, RECDOORD_PROJECTION,
                    where, null, null);
            if ((cursor != null) && (cursor.getCount() > 0)) {
                DebugUtil.i(TAG, "checkAndUpdateRecordsMimeTypeIssue count is " + cursor.getCount());
                while (cursor.moveToNext()) {
                    Record record = new Record(cursor, Record.TYPE_FROM_RECORD);
                    issueRecordList.add(record);
                }
            }
        } catch (Exception e) {
            DebugUtil.e(TAG, "checkAndUpdateRecordsMimeTypeIssue : " + e.getMessage(), e);
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        if (!issueRecordList.isEmpty()) {
            for (Record issueRecord : issueRecordList) {
                if (issueRecord.mMimeType == null) {
                    Record mediaRecord = MediaDBUtils.queryRecordByRelativePathAndDisplayName(issueRecord.getRelativePath(),
                            issueRecord.getDisplayName());
                    if (mediaRecord != null) {
                        issueRecord.mMimeType = mediaRecord.getMimeType();
                        updateRecordData(mContext, issueRecord.convertToContentValues(),
                                DatabaseConstant.RecorderColumn.COLUMN_NAME_ID + "=?", new String[]{String.valueOf(issueRecord.getId())});
                        DebugUtil.i(TAG, "checkAndUpdateRecordsMimeTypeIssue issueRecord: " + issueRecord);
                    }
                }
            }
        }
    }


    private boolean updateRecordByUUid(String inputUUid, ContentValues cv) {
        if (TextUtils.isEmpty(inputUUid)) {
            return false;
        }
        String where = COLUMN_NAME_UUID + " = ?";
        String[] whereArgs = new String[]{inputUUid};
        int updateCount = 0;
        if (cv != null) {
            try {
                updateCount = mContext.getContentResolver().update(RECORD_CONTENT_URI, cv, where, whereArgs);
                DebugUtil.i(TAG, "updateRecordByUUid: inputUUid: " + inputUUid + ", cv: " + cv + ", update count : " + updateCount, true);
            } catch (Exception e) {
                DebugUtil.e(TAG, "qureyRecordByPath : " + e.getMessage(), e);
            }
        }
        return updateCount > 0;
    }


    public boolean updateSyncStateByUUid(String inputUUid, int inputSyncState) {
        ContentValues cv = generateContentValuesForInputSyncStatus(inputUUid, inputSyncState, true);
        boolean updateSuc = false;
        if (cv != null) {
            updateSuc = updateRecordByUUid(inputUUid, cv);
        } else {
            DebugUtil.i(TAG, "updateSyncStateByUUid no record found inputUUid: " + inputUUid);
        }
        return updateSuc;
    }


    public boolean updateDownloadStateByUUid(String inputUUid, int inputSyncState) {
        ContentValues cv = generateContentValuesForInputSyncStatus(inputUUid, inputSyncState, false);
        boolean updateSuc = false;
        if (cv != null) {
            updateSuc = updateRecordByUUid(inputUUid, cv);
        }
        return updateSuc;
    }


    private ContentValues generateContentValuesForInputSyncStatus(String inputUUid, int inputSyncState, boolean upload) {
        String where = COLUMN_NAME_UUID + " = ?";
        String[] whereArgs = new String[]{inputUUid};
        ContentValues cv = null;
        Cursor cursor = null;
        try {
            cursor = mContext.getContentResolver().query(RECORD_CONTENT_URI, RECDOORD_PROJECTION, where, whereArgs, RECORDER_QUERY_ORDER);
            if ((cursor != null) && (cursor.getCount() > 0)) {
                while (cursor.moveToNext()) {
                    Record record = new Record(cursor, Record.TYPE_FROM_RECORD);
                    if (upload) {
                        cv = generateContentValuesForInputSyncStatus(record, inputSyncState);
                    } else {
                        cv = generateContentValuesForDownloadStatus(record, inputSyncState);
                    }
                }
            }
        } catch (Exception e) {
            DebugUtil.e(TAG, "qureyRecordByPath : " + e.getMessage(), e);
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return cv;
    }


    private ContentValues generateContentValuesForInputSyncStatus(Record originalRecord, int inputSyncState) {
        ContentValues cv = new ContentValues();
        cv.put(COLUMN_NAME_SYNC_UPLOAD_STATUS, inputSyncState);
        long syncDate = System.currentTimeMillis();
        long originalRetryCount = originalRecord.getFailedCount();
        if (inputSyncState == SYNC_STATUS_BACKUP_START) {

        } else if (inputSyncState == SYNC_STATUS_BACKUPING_FILE) {
            cv.put(COLUMN_NAME_SYNC_DATE, syncDate);
        } else if (inputSyncState == SYNC_STATUS_BACKUP_FILE_FAILED) {
            long retryCount = originalRetryCount + 1;
            if (retryCount > MAX_COUNT) {
                retryCount = MAX_COUNT;
            }
            cv.put(COLUMN_NAME_FAIL_COUNT, retryCount);
            cv.put(COLUMN_NAME_LAST_FAIL_TIME, syncDate);
        } else if (inputSyncState == SYNC_STATUS_BACKUP_FILE_SUC) {
            cv.put(COLUMN_NAME_LAST_FAIL_TIME, 0);
            cv.put(COLUMN_NAME_FAIL_COUNT, 0);
        }
        return cv;
    }


    private ContentValues generateContentValuesForDownloadStatus(Record originalRecord, int inputSyncState) {
        ContentValues cv = new ContentValues();
        cv.put(COLOUM_NAME_SYNC_DOWNLOAD_STATUS, inputSyncState);
        long syncDate = System.currentTimeMillis();
        long originalRetryCount = originalRecord.getFailedCount();
        if (inputSyncState == SYNC_STATUS_RECOVERY_MEGADATA_SUC) {

        } else if (inputSyncState == SYNC_STATUS_RECOVERYING_FILE) {
            cv.put(COLUMN_NAME_SYNC_DATE, syncDate);
        } else if (inputSyncState == SYNC_STATUS_RECOVERY_FILE_FAILED) {
            long retryCount = originalRetryCount + 1;
            if (retryCount > MAX_COUNT) {
                retryCount = MAX_COUNT;
            }
            cv.put(COLUMN_NAME_FAIL_COUNT, retryCount);
            cv.put(COLUMN_NAME_LAST_FAIL_TIME, syncDate);
        } else if (inputSyncState == SYNC_STATUS_RECOVERY_FILE_SUC) {
            cv.put(COLUMN_NAME_LAST_FAIL_TIME, 0);
            cv.put(COLUMN_NAME_FAIL_COUNT, 0);
        }
        return cv;
    }

    public boolean updateFileIdByUUid(String inputUUid, String inputFileId, String checkPayload) {
        ContentValues cv = generateContentValuesForInputSyncStatus(inputUUid, SYNC_STATUS_BACKUP_FILE_SUC, true);
        if (cv != null) {
            cv.put(COLUMN_NAME_FILE_ID, inputFileId);
            cv.put(COLUMN_NAME_CLOUD_CHECK_PAYLOAD, checkPayload);
            return updateRecordByUUid(inputUUid, cv);
        }
        return false;
    }

    public boolean resetUuidByUUid(String inputUUid) {
        ContentValues cv = new ContentValues();
        cv.put(COLUMN_NAME_UUID, UUID.randomUUID().toString());
        return updateRecordByUUid(inputUUid, cv);
    }

    public boolean resetUuidByUUid(String inputUUid, String newUuid) {
        ContentValues cv = new ContentValues();
        cv.put(COLUMN_NAME_UUID, TextUtils.isEmpty(newUuid) ? UUID.randomUUID().toString() : newUuid);
        return updateRecordByUUid(inputUUid, cv);
    }

    public boolean clearExpieredFileIdByUUid(String inputUUid) {
        ContentValues cv = generateContentValuesForInputSyncStatus(inputUUid, SYNC_STATUS_BACKUP_START, true);
        if (cv != null) {
            cv.put(COLUMN_NAME_FILE_ID, (String) null);
            cv.put(COLUMN_NAME_CLOUD_CHECK_PAYLOAD, (String) null);
            return updateRecordByUUid(inputUUid, cv);
        }
        return false;
    }

    /**
     * 清除云端字段信息如globalid、fileid、sysversion
     * 将本地该数据置为待上传数据
     * @param resetUUid
     * @param inputUUid
     * @return
     */
    public boolean clearCloudColumnByUUid(boolean resetUUid, String inputUUid) {
        ContentValues cv = new ContentValues();
        if (resetUUid) {
            cv.put(COLUMN_NAME_UUID, UUID.randomUUID().toString());
        }
        cv.put(COLUMN_NAME_FILE_ID, (String) null);
        cv.put(COLUMN_NAME_GLOBAL_ID, (String) null);
        cv.put(COLUMN_NAME_CLOUD_CHECK_PAYLOAD, (String) null);
        cv.put(COLUMN_NAME_CLOUD_SYS_VERSION, 0);
        cv.put(COLUMN_NAME_DIRTY, RECORD_DIRTY_MEGA_ONLY);
        cv.put(COLUMN_NAME_SYNC_UPLOAD_STATUS, SYNC_STATUS_BACKUP_START);
        cv.put(COLUMN_NAME_LOCAL_EDIT_STATUS, LOCAL_EDIT_COMPLET);
        cv.put(COLUMN_NAME_SYNC_DATE, 0);
        cv.put(COLUMN_NAME_FAIL_COUNT, 0);
        cv.put(COLUMN_NAME_LAST_FAIL_TIME, 0);

        return updateRecordByUUid(inputUUid, cv);
    }

    public List<Record> getAllData() {
        String where = COLUMN_NAME_DELETE + " != " + RECORD_DELETED;
        Cursor cursor = null;
        List<Record> allData = new ArrayList<>();
        try {
            cursor = mContext.getContentResolver().query(RECORD_CONTENT_URI, RECDOORD_PROJECTION, where, null, RECORDER_QUERY_ORDER);
            if ((cursor != null) && (cursor.getCount() > 0)) {
                while (cursor.moveToNext()) {
                    Record record = new Record(cursor, Record.TYPE_FROM_RECORD);
                    allData.add(record);
                }
            }
        } catch (Exception e) {
            DebugUtil.e(TAG, "qureyRecordByPath : " + e.getMessage(), e);
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return allData;
    }

    public List<Record> getCallerAndColorRecordList() {
        //String where = COLUMN_NAME_DISPLAY_NAME + " =? AND " + COLUMN_NAME_RELATIVE_PATH + " COLLATE NOCASE =? AND " + COLUMN_NAME_MD5 + " = ?";
        String where = COLUMN_NAME_DELETE + " != " + RECORD_DELETED + " AND "
                + COLUMN_NAME_CALL_AVATAR_COLOR + " not null OR "
                + COLUMN_NAME_CALLER_NAME + " not null";
        Cursor cursor = null;
        List<Record> allData = new ArrayList<>();
        try {
            cursor = mContext.getContentResolver().query(RECORD_CONTENT_URI, RECDOORD_PROJECTION, where, null, RECORDER_QUERY_ORDER);
            if ((cursor != null) && (cursor.getCount() > 0)) {
                while (cursor.moveToNext()) {
                    Record record = new Record(cursor, Record.TYPE_FROM_RECORD);
                    allData.add(record);
                }
            }
        } catch (Exception e) {
            DebugUtil.e(TAG, "qureyRecordByPath : " + e.getMessage(), e);
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return allData;
    }

    public boolean updateMD5AndSizeForFile(File file) {
        if (file == null) {
            DebugUtil.e(TAG, "input file is null");
            return false;
        }
        String md5 = MD5Utils.getMD5(file);
        long size = file.length();
        ContentValues cv = new ContentValues();
        String path = file.getPath();
        cv.put(DatabaseConstant.RecorderColumn.COLUMN_NAME_MD5, md5);
        cv.put(DatabaseConstant.RecorderColumn.COLUMN_NAME_SIZE, size);
        String where = DatabaseConstant.RecorderColumn.COLUMN_NAME_DATA + " COLLATE NOCASE =?";
        try {
            int updateCount = mContext.getContentResolver().update(RECORD_CONTENT_URI, cv, where, new String[]{path});
            return updateCount > 0;
        } catch (Exception e) {
            DebugUtil.e(TAG, "updateMD5AndSizeForFile error" + e);
        }
        return false;
    }

    /*
     * Do NOT use this when the operations list contains one with 'withValueBackReference' data.
     * */
    public static ArrayList<ArrayList<ContentProviderOperation>> splitForBatch(ArrayList<ContentProviderOperation> operations) {
        return splitForBatch(operations, MAX_OPERATIONS);
    }

    private static ArrayList<ArrayList<ContentProviderOperation>> splitForBatch(ArrayList<ContentProviderOperation> operations, int
            batchCount) {
        if ((operations == null) || (operations.isEmpty())) {
            return null;
        }
        final boolean notExceedMax = operations.size() < batchCount;
        final ArrayList<ArrayList<ContentProviderOperation>> operationsList = new ArrayList<ArrayList<ContentProviderOperation>>();
        if (notExceedMax) {
            operationsList.add(operations);
        } else {
            final int total = operations.size();
            ArrayList<ContentProviderOperation> ops = new ArrayList<ContentProviderOperation>();
            operationsList.add(ops);
            for (int i = 0; i < total; i++) {
                ContentProviderOperation op = operations.get(i);
                int size = ops.size();
                if (size < batchCount) {
                    ops.add(op);
                } else {
                    ops = new ArrayList<>();
                    operationsList.add(ops);
                    ops.add(op);
                }
            }
        }
        return operationsList;
    }

    public static ContentProviderResult[] applyContentProviderOperation(Context context, String authority, ArrayList<ContentProviderOperation> operations) {
        ContentProviderResult[] results = null;
        if (!TextUtils.isEmpty(authority) && (operations != null) && !operations.isEmpty()) {
            try {
                long start = System.currentTimeMillis();
                results = context.getContentResolver().applyBatch(authority, operations);
                long end = System.currentTimeMillis();
                DebugUtil.i(TAG, "applyBatch operations size :" + operations.size() + ", cost: [" + (end - start) + "] ms.");
                Thread.sleep(OPERATIONS_SLEEPTIME);
            } catch (RemoteException e) {
                DebugUtil.e(TAG, "applyContentProviderOperation RemoteException : ", e);
            } catch (OperationApplicationException e) {
                DebugUtil.e(TAG, "applyContentProviderOperation OperationApplicationException : ", e);
            } catch (InterruptedException e) {
                DebugUtil.e(TAG, "applyContentProviderOperation InterruptedException : ", e);
            } catch (Exception e) {
                DebugUtil.e(TAG, "applyContentProviderOperation Exception : ", e);
            }
        }
        return results;
    }

    public List<Record> getRecordByFileIdForDownlaodResult(String fileId) {
        Record record = null;
        if (TextUtils.isEmpty(fileId)) {
            DebugUtil.e(TAG, "input fileId is null");
            return null;
        }
        String selection = DatabaseConstant.RecorderColumn.COLUMN_NAME_FILE_ID + "='" + fileId + "'";
        List<Record> result = getRecordData(mContext, null, selection, null, null);
        return result;
    }


    public Record getRecordByGlobalId(String globalId) {
        Record record = null;
        if (TextUtils.isEmpty(globalId)) {
            DebugUtil.e(TAG, "input fileId is null");
            return null;
        }
        String selection = DatabaseConstant.RecorderColumn.COLUMN_NAME_GLOBAL_ID + "= ?";
        String[] selectionArgs = new String[]{globalId};
        List<Record> result = getRecordData(mContext, null, selection, selectionArgs, null);
        if ((result != null) && !result.isEmpty()) {
            record = result.get(0);
        }
        return record;
    }

    public Record getRecordById(String id) {
        Record record = null;
        if (TextUtils.isEmpty(id)) {
            DebugUtil.e(TAG, "input fileId is null");
            return null;
        }
        String selection = DatabaseConstant.RecorderColumn.COLUMN_NAME_ID + "= ?";
        String[] selectionArgs = new String[]{id};
        List<Record> result = getRecordData(mContext, null, selection, selectionArgs, null);
        if ((result != null) && !result.isEmpty()) {
            record = result.get(0);
        }
        return record;
    }

    public static Record getRecordByData(String data) {
        Record record = null;
        if (TextUtils.isEmpty(data)) {
            DebugUtil.e(TAG, "input data is null");
            return null;
        }
        String selection = DatabaseConstant.RecorderColumn.COLUMN_NAME_DATA + "= ?";
        String[] selectionArgs = new String[]{data};
        List<Record> result = getRecordData(BaseApplication.getAppContext(), null, selection, selectionArgs, null);
        if ((result != null) && !result.isEmpty()) {
            record = result.get(0);
        }
        return record;
    }

    public List<String> getRecordPathByIds(String[] idList) {
        List<String> realFilePaths = new ArrayList<>();
        StringBuilder selection = new StringBuilder(COLUMN_NAME_ID + " in (");
        for (int i = 0; i < idList.length; i++) {
            if (i == idList.length - 1) {
                selection.append("?)");
            } else {
                selection.append("?, ");
            }
        }
        List<Record> records = getRecordData(mContext, null, selection.toString(), idList, null);
        if (records != null && !records.isEmpty()) {
            for (Record record:records) {
                if (record != null && !TextUtils.isEmpty(record.getData())) {
                    realFilePaths.add(record.getData());
                }
            }
        }
        return realFilePaths;
    }

    public boolean updateRealFileSizeOrMd5(Record originalRecord, File inputFile, boolean changeMD5) {
        if (originalRecord == null) {
            DebugUtil.e(TAG, "input originalRecord is null");
            return false;
        }
        if ((inputFile == null) || !inputFile.exists()) {
            DebugUtil.e(TAG, "input file is null or is not exist");
            return false;
        }
        long id = originalRecord.getId();
        long realFileSize = inputFile.length();
        String selection = DatabaseConstant.RecorderColumn.COLUMN_NAME_ID + "=" + id;
        ContentValues cv = new ContentValues();
        cv.put(COLUMN_NAME_SIZE, realFileSize);
        if (changeMD5) {
            String md5 = MD5Utils.getMD5(inputFile);
            cv.put(COLUMN_NAME_MD5, md5);
        }
        int updateCount = updateRecordData(mContext, cv, selection, null);
        return updateCount > 0;
    }


    public boolean updateRealFileSizeOrMd5(Record originalRecord, long fileSize, String md5) {
        if (originalRecord == null) {
            DebugUtil.e(TAG, "input originalRecord is null");
            return false;
        }
        long id = originalRecord.getId();
        String selection = DatabaseConstant.RecorderColumn.COLUMN_NAME_ID + " = ?";
        ContentValues cv = new ContentValues();
        cv.put(COLUMN_NAME_SIZE, fileSize);
        cv.put(COLUMN_NAME_MD5, md5);
        int updateCount = updateRecordData(mContext, cv, selection, new String[]{String.valueOf(id)});
        return updateCount > 0;
    }

    public static int getRecordCount(Context context) {
        int count = 0;
        if (context == null) {
            DebugUtil.i(TAG, "context is empty, can not find the record");
            return count;
        }
        Cursor cursor = null;
        try {
            String selection = COLUMN_NAME_SIZE + " > 0";
            Bundle queryBundle = createSqlQueryBundle(selection, null, null, null, null);
            cursor = context.getContentResolver().query(RECORD_CONTENT_URI, null, queryBundle, null);
            if ((cursor != null)) {
                count = cursor.getCount();
            }
        } catch (Exception e) {
            DebugUtil.e(TAG, "getRecordCount(): " + e.getMessage(), e);
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return count;
    }

    public static List<Record> getRecordData(Context context, String[] projection, String selection, String[] selectionArgs, String order) {
        if (context == null) {
            DebugUtil.i(TAG, "inputString is empty, can not find the record");
            return null;
        }
        Cursor cursor = null;
        List<Record> result = new ArrayList<>();
        try {
            Bundle queryBundle = createSqlQueryBundle(selection, selectionArgs, order, null, null);
            cursor = context.getContentResolver().query(RECORD_CONTENT_URI, projection, queryBundle, null);
            if ((cursor != null) && (cursor.getCount() > 0)) {
                while (cursor.moveToNext()) {
                    Record record = new Record(cursor, Record.TYPE_FROM_RECORD);
                    result.add(record);
                }
            }
        } catch (Exception e) {
            DebugUtil.e(TAG, "qureyRecordBySame : " + e.getMessage(), e);
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return result;
    }

    public static @Nullable
    Bundle createSqlQueryBundle(
            @Nullable String selection,
            @Nullable String[] selectionArgs,
            @Nullable String sortOrder,
            @Nullable Integer limit,
            @Nullable Integer offset) {

        if ((selection == null) && (selectionArgs == null) && (sortOrder == null)) {
            return null;
        }

        Bundle queryArgs = new Bundle();
        if (selection != null) {
            queryArgs.putString(QUERY_ARG_SQL_SELECTION, selection);
        }
        if (selectionArgs != null) {
            queryArgs.putStringArray(QUERY_ARG_SQL_SELECTION_ARGS, selectionArgs);
        }
        if (sortOrder != null) {
            queryArgs.putString(QUERY_ARG_SQL_SORT_ORDER, sortOrder);
        }
        if (limit != null) {
            queryArgs.putInt(QUERY_ARG_LIMIT, limit);
        }
        if (offset != null) {
            queryArgs.putInt(QUERY_ARG_OFFSET, offset);
        }
        return queryArgs;
    }

    public static int updateRecordData(Context context, ContentValues cv, String selection, String[] selectionArgs) {
        if (context == null) {
            DebugUtil.i(TAG, "inputString is empty, can not find the record");
            return -1;
        }
        try {
            return context.getContentResolver().update(RECORD_CONTENT_URI, cv, selection, selectionArgs);
        } catch (Exception e) {
            DebugUtil.e(TAG, "qureyRecordBySame : " + e.getMessage(), e);
        }
        return -1;
    }


    /**
     * @param context
     * @param keyId
     * @param cv
     * @param selection
     * @param selectionArgs
     * @return
     */
    public static int updateRecordDataWithCloudMark(Context context, String keyId, ContentValues cv, String selection, String[] selectionArgs) {
        int result = updateRecordData(context, cv, selection, selectionArgs);
        byte[] markData = cv.getAsByteArray(COLUMN_NAME_MARK_DATA);
        DebugUtil.i(TAG, "updateRecordDataWithCloudMark markData " + Arrays.toString(markData));
        if (markData != null) {
            //云端的markData存在，有标记数据，需要将云端的数据更新到本地
            updateOrInsertMarkInNewTable(keyId, false, markData, true);
        } else {
            //云端的markData不存在，需要删除本地的mark表中的text类型的数据，保留本地的图片标记
            PictureMarkDbUtils.INSTANCE.deleteOnlyTextMarks(keyId);
        }
        return result;
    }

    public static Uri insertRecordData(Context context, ContentValues cv) {
        if (context == null) {
            DebugUtil.i(TAG, "inputString is empty, can not find the record");
            return null;
        }
        Uri insertUri = null;
        try {
            insertUri = context.getContentResolver().insert(RECORD_CONTENT_URI, cv);
        } catch (Exception e) {
            DebugUtil.e(TAG, "qureyRecordBySame : " + e.getMessage(), e);
        }
        if (insertUri != null) {
            long recordId = ContentUris.parseId(insertUri);
            String keyId = recordId + "";
            byte[] markData = cv.getAsByteArray(COLUMN_NAME_MARK_DATA);
            if (markData != null) {
                //跟新mark的数据库表
                updateOrInsertMarkInNewTable(keyId, true, markData, true);
            }
        }
        return insertUri;
    }

    /**
     * 删除符合条件的record记录，以及对应波形、标记等信息。
     * 不包含转文本table及文件，转文本关联的是媒体库ID，要单独处理
     * @param context
     * @param selection
     * @param selectionArgs
     * @return
     */
    public static int deleteRecordData(Context context, String selection, String[] selectionArgs) {
        if (context == null) {
            DebugUtil.i(TAG, "inputString is empty, can not deleteRecordData the record");
            return -1;
        }
        int deleteCount = 0;
        try {
            String whereId = COLUMN_NAME_ID + "=?";
            List<Record> toDeleteRecords = getRecordData(context, null, selection, selectionArgs, null);
            if (!toDeleteRecords.isEmpty()) {
                for (Record record : toDeleteRecords) {
                    long id = record.getId();
                    String[] whereIdAgrs = new String[]{String.valueOf(id)};
                    int dbDeleteCount = context.getContentResolver().delete(RECORD_CONTENT_URI, whereId, whereIdAgrs);
                    if (dbDeleteCount > 0) {
                        // 删除波形文件
                        boolean ampfileDeleted = record.deleteAmpFile();
                        if (ampfileDeleted) {
                            deleteCount++;
                        }
                        // 删除图片标记记录、文件
                        deletePictureDbAndFilesByData(record.getId() + "");
                    }
                    DebugUtil.i(TAG, "deleteRecordData id " + id + ", dbDeleteCount : " + deleteCount + ", ampfileDeleted: " + dbDeleteCount, true);
                }
            }
            return deleteCount;
        } catch (Exception e) {
            DebugUtil.e(TAG, "qureyRecordBySame : " + e.getMessage(), e);
        }
        return -1;
    }

    public List<String> deleteFileList(Context context, String selection, String[] selectionArgs) {
        List<String> ampFilePaths = getAmpFilePathList(context, selection, selectionArgs);
        List<String> deleteSucPaths = deleteAmpFiles(ampFilePaths);
        List<String> audioFilePaths = getAudioFilePathList(context, selection, selectionArgs);
        deletePictureDbAndFiles(audioFilePaths);
        return deleteSucPaths;
    }

    public long deleteRecordDataByPathPrefix(String pathPrefix) {
        if (TextUtils.isEmpty(pathPrefix)) {
            DebugUtil.w(TAG, "deleteRecordDataByPathPrefix pathPrefix is empty");
            return 0;
        }
        String selection = DatabaseConstant.RecorderColumn.COLUMN_NAME_DATA + " LIKE ?";
        String[] selectionArgs = new String[]{pathPrefix + "%"};
        int deleteRowCount = mContext.getContentResolver().delete(RECORD_CONTENT_URI, selection, selectionArgs);
        DebugUtil.d(TAG, "deleteRecordDataByPathPrefix deleteRowCount=" + deleteRowCount);
        return deleteRowCount;
    }

    private static List<String> getAudioFilePathList(Context context, String selection, String[] selectionArgs) {
        if (context == null) {
            DebugUtil.i(TAG, "inputString is empty, can not deleteRecordData the record");
            return null;
        }
        List<String> result = new ArrayList<>();
        try {
            List<Record> toDeleteRecords = getRecordData(context, null, selection, selectionArgs, null);
            if (!toDeleteRecords.isEmpty()) {
                for (Record record : toDeleteRecords) {
                    String keyId = record.getId() + "";
                    if (!TextUtils.isEmpty(keyId)) {
                        result.add(keyId);
                    }
                }
            }
        } catch (Exception e) {
            DebugUtil.e(TAG, "qureyRecordBySame : " + e.getMessage(), e);
        }
        return result;
    }


    private static List<String> getAmpFilePathList(Context context, String selection, String[] selectionArgs) {
        if (context == null) {
            DebugUtil.i(TAG, "inputString is empty, can not deleteRecordData the record");
            return null;
        }
        List<String> result = new ArrayList<>();
        try {
            List<Record> toDeleteRecords = getRecordData(context, null, selection, selectionArgs, null);
            if (!toDeleteRecords.isEmpty()) {
                for (Record record : toDeleteRecords) {
                    String ampFilePath = record.getAmpFilePath();
                    if (!TextUtils.isEmpty(ampFilePath)) {
                        result.add(ampFilePath);
                    }
                }
            }

        } catch (Exception e) {
            DebugUtil.e(TAG, "qureyRecordBySame : " + e.getMessage(), e);
        }
        return result;
    }

    private static List<String> deleteAmpFiles(List<String> paths) {
        List<String> deleteSucFiles = new ArrayList<>();
        if ((paths != null) && !paths.isEmpty()) {
            for (String path : paths) {
                if (!TextUtils.isEmpty(path)) {
                    File ampFile = new File(path);
                    if (ampFile.exists()) {
                        boolean delsuc = ampFile.delete();
                        if (delsuc) {
                            deleteSucFiles.add(path);
                        }
                    }
                }
            }
        }
        return deleteSucFiles;
    }

    private static void deletePictureDbAndFiles(List<String> paths) {
        if ((paths != null) && !paths.isEmpty()) {
            for (String path : paths) {
                deletePictureDbAndFilesByData(path);
            }
        }
    }


    public static long deletePictureDbAndFilesByData(String keyId) {
        long result = -1;
        if (!TextUtils.isEmpty(keyId)) {
            result = PictureMarkDbUtils.deletePictureMarksWithMarkPictureFiles(keyId);
        }
        DebugUtil.i(TAG, "deletePictureDbAndFilesByData keyId: " + keyId + ", result: " + result);
        return result;
    }

    public int getNeedSyncCount() {
        String[] projection = new String[]{DatabaseConstant.RecorderColumn.COLUMN_NAME_ID};
        String se1 = "(((%s != ? or %s = ?) and %s != ?) or (%s = ? and %s != ? and %s not null and %s not null and %s in (? , ? , ?, ? ))) ";
        String se2 = " and (%s > 0) and (is_recycle = 0)";
        String selection = se1 + se2;
        selection = String.format(selection,
                COLUMN_NAME_DIRTY,
                COLUMN_NAME_DELETE,
                COLUMN_NAME_LOCAL_EDIT_STATUS,
                COLUMN_NAME_SYNC_TYPE,
                COLUMN_NAME_LOCAL_EDIT_STATUS,
                COLUMN_NAME_GLOBAL_ID,
                COLUMN_NAME_FILE_ID,
                COLOUM_NAME_SYNC_DOWNLOAD_STATUS,
                COLUMN_NAME_SIZE);
        String[] selectionArgs = new String[]{
                String.valueOf(RECORD_NOT_DIRTY),
                String.valueOf(RECORD_DELETED),
                String.valueOf(LOCAL_EDITING),
                String.valueOf(SYNC_TYPE_RECOVERY),
                String.valueOf(LOCAL_EDITING),
                String.valueOf(SYNC_STATUS_RECOVERY_MEGADATA_SUC),
                String.valueOf(SYNC_STATUS_RECOVERYING_FILE),
                String.valueOf(SYNC_STATUS_RECOVERY_FILE_FAILED),
                String.valueOf(SYNC_STATUS_RECOVERY_FILE_JUMP)};
        Bundle queryBundle = createSqlQueryBundle(selection, selectionArgs, null, null, null);
        Cursor cursor = null;
        int count = 0;
        try {
            cursor = mContext.getContentResolver().query(RECORD_CONTENT_URI, projection, queryBundle, null);
            if (cursor != null) {
                count = cursor.getCount();
            }
        } catch (Throwable e) {
            DebugUtil.e(TAG, "query need sync count error", e);
        } finally {
            if (cursor != null) {
                cursor.close();
            }
            DebugUtil.d(TAG, "query need sync count:" + count);
        }
        return count;
    }

    /**
     * 本地需要备份到云端的count
     *
     * @return
     */
    public int getNeedBackUpCount() {
        String[] projection = new String[]{DatabaseConstant.RecorderColumn.COLUMN_NAME_ID};
        String selection = "(((%s != ? or %s = ?) and %s != ?) and (%s > 0) and (is_recycle = 0))";
        selection = String.format(selection,
                COLUMN_NAME_DIRTY,
                COLUMN_NAME_DELETE,
                COLUMN_NAME_LOCAL_EDIT_STATUS,
                COLUMN_NAME_SIZE);
        String[] selectionArgs = new String[]{
                String.valueOf(RECORD_NOT_DIRTY),
                String.valueOf(RECORD_DELETED),
                String.valueOf(LOCAL_EDITING)};
        Bundle queryBundle = createSqlQueryBundle(selection, selectionArgs, null, null, null);
        Cursor cursor = null;
        int count = 0;
        try {
            cursor = mContext.getContentResolver().query(RECORD_CONTENT_URI, projection, queryBundle, null);
            if (cursor != null) {
                count = cursor.getCount();
            }
        } catch (Throwable e) {
            DebugUtil.e(TAG, "query need sync back up count error", e);
        } finally {
            if (cursor != null) {
                cursor.close();
            }
            DebugUtil.d(TAG, "query need sync back up count:" + count);
        }
        return count;
    }

    /**
     * 本地db已同步到云端的count
     *
     * @return
     */
    public int getLocalCloudDataCount() {
        String[] projection = new String[]{DatabaseConstant.RecorderColumn.COLUMN_NAME_ID};
        String selection = "(%s not null and %s not null)";
        selection = String.format(selection, COLUMN_NAME_GLOBAL_ID, COLUMN_NAME_FILE_ID);
        String[] selectionArgs = new String[]{};
        Bundle queryBundle = createSqlQueryBundle(selection, selectionArgs, null, null, null);
        Cursor cursor = null;
        int count = 0;
        try {
            cursor = mContext.getContentResolver().query(RECORD_CONTENT_URI, projection, queryBundle, null);
            if (cursor != null) {
                count = cursor.getCount();
            }
        } catch (Throwable e) {
            DebugUtil.e(TAG, "query local synced count error", e);
        } finally {
            if (cursor != null) {
                cursor.close();
            }
            DebugUtil.i(TAG, "query local synced count:" + count);
        }
        return count;
    }

    public List<Record> queryMaybeRenameRecordFiles(Record mediaFile) {
        String selection = "%s = ? and %s = ? and %s = ? and %s != ?";
        selection = String.format(selection,
                DatabaseConstant.RecorderColumn.COLUMN_NAME_DATE_CREATED,
                DatabaseConstant.RecorderColumn.COLUMN_NAME_SIZE,
                DatabaseConstant.RecorderColumn.COLUMN_NAME_MD5,
                DatabaseConstant.RecorderColumn.COLUMN_NAME_DISPLAY_NAME);
        String md5 = mediaFile.getMD5();
        if (TextUtils.isEmpty(md5)) {
            if (mediaFile.checkMd5()) {
                md5 = mediaFile.getMD5();
            } else {
                md5 = "";
            }
        }
        String[] selectionArgs = new String[]{
                String.valueOf(mediaFile.getDateCreated()),
                String.valueOf(mediaFile.getFileSize()),
                md5,
                mediaFile.getDisplayName()};
        DebugUtil.d(TAG, "queryMaybeRenameRecordFiles:" + String.join(",", selectionArgs));
        return getRecordData(mContext, null, selection, selectionArgs, null);
    }

    public int updateRenameRecordDB(Record recordFile) {
        String selection = DatabaseConstant.RecorderColumn.COLUMN_NAME_ID + " = ?";
        String[] selectionArgs = new String[]{String.valueOf(recordFile.getId())};
        try {
            return mContext.getContentResolver().update(RECORD_CONTENT_URI,
                    recordFile.convertToContentValues(), selection, selectionArgs);
        } catch (Exception e) {
            DebugUtil.e(TAG, "updateRenameRecordDB error" + e);
        }
        return 0;
    }

    /**
     * 更新数据库，因targetRecord的id可能是媒体库的id，导致无法在records表查到，则用path兜底
     * @param targetRecord
     * @return
     */
    public int updateRecordDbByPathOrId(Record targetRecord) {
        String selection = DatabaseConstant.RecorderColumn.COLUMN_NAME_ID + " = ? or "
                + COLUMN_NAME_DATA + " = ?";
        String[] selectionArgs = new String[]{String.valueOf(targetRecord.getId()), targetRecord.getData()};
        try {
            return mContext.getContentResolver().update(RECORD_CONTENT_URI,
                    targetRecord.convertToContentValues(), selection, selectionArgs);
        } catch (Exception e) {
            DebugUtil.e(TAG, "updateRecordDbByPathOrId error" + e);
        }
        return 0;
    }

    /**
     * 更新数据库，因targetRecord的id可能是媒体库的id，导致无法在records表查到，则用path兜底
     * @param targetRecord
     * @return
     */
    public int updateRecordDbAvatarAndColor(Record targetRecord) {
        String selection = DatabaseConstant.RecorderColumn.COLUMN_NAME_ID + " = ? or "
                + COLUMN_NAME_DATA + " = ?";
        String[] selectionArgs = new String[]{String.valueOf(targetRecord.getId()), targetRecord.getData()};
        try {
            ContentValues values = new ContentValues();
            values.put(COLUMN_NAME_CALL_AVATAR_COLOR, targetRecord.getCallAvatarColor());
            values.put(COLUMN_NAME_CALLER_NAME, targetRecord.getCallerName());
            return mContext.getContentResolver().update(RECORD_CONTENT_URI,
                    values, selection, selectionArgs);
        } catch (Exception e) {
            DebugUtil.e(TAG, "updateRecordDbAvatarAndColor error" + e);
        }
        return -1;
    }

    public List<Record> getRecordsByGroupInfo(GroupInfo groupInfo) {
        if (groupInfo == null) {
            String selection = COLUMN_NAME_SIZE + " > 0";
            return getRecordData(mContext, null, selection, null, null);
        } else {
            return getRecordsByGroupUUId(groupInfo.getMUuId());
        }
    }
    public List<Record> getRecordsByGroupUUId(String id) {
        String selection = COLUMN_NAME_GROUP_UUID + "= ?";
        String[] selectionArgs = new String[]{String.valueOf(id)};
        List<Record> result = getRecordData(mContext, null, selection, selectionArgs, null);
        return result;
    }
    public List<Record> getRecordsByGroupId(int id) {
        String selection = COLUMN_NAME_GROUP_ID + "= ?";
        String[] selectionArgs = new String[]{String.valueOf(id)};
        List<Record> result = getRecordData(mContext, null, selection, selectionArgs, null);
        return result;
    }

    public List<Record> getRecordsByPathList(String[] pathList) {
        StringBuilder selection = new StringBuilder(COLUMN_NAME_DATA + " in (");
        for (int i = 0; i < pathList.length; i++) {
            if (i == pathList.length - 1) {
                selection.append("?)");
            } else {
                selection.append("?, ");
            }
        }
        selection.append(" and ").append(COLUMN_NAME_SIZE).append(" > 0");
        List<Record> records = getRecordData(mContext, null, selection.toString(), pathList, null);
        return records;
    }

    public List<Record> getRecordsByGroupIds(String[] idList) {
        StringBuilder selection = new StringBuilder(COLUMN_NAME_GROUP_ID + " in (");
        for (int i = 0; i < idList.length; i++) {
            if (i == idList.length - 1) {
                selection.append("?)");
            } else {
                selection.append("?, ");
            }
        }
        List<Record> records = getRecordData(mContext, null, selection.toString(), idList, null);
        return records;
    }

    public List<Record> getRecordFilePathsByPathPrefix(String recordPathPrefix) {
        if (TextUtils.isEmpty(recordPathPrefix)) {
            DebugUtil.w(TAG, "getRecordFilePathsByPathPrefix pathPrefix is empty");
            return Collections.emptyList();
        }
        String selection = DatabaseConstant.RecorderColumn.COLUMN_NAME_DATA + " LIKE ?";
        String[] selectionArgs = new String[]{recordPathPrefix + "%"};
        Record record = null;
        List<Record> list = new ArrayList<>();
        Cursor cursor = null;
        try {
            cursor = mContext.getContentResolver().query(RECORD_CONTENT_URI, RECDOORD_PROJECTION, selection, selectionArgs, null);
            if ((cursor != null) && (cursor.getCount() > 0)) {
                while (cursor.moveToNext()) {
                    record = new Record(cursor, Record.TYPE_FROM_RECORD);
                    list.add(record);
                }
            }
        } catch (Exception e) {
            DebugUtil.e(TAG, "getRecordFilePathsByPathPrefix Exception " + e.getMessage());
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return list;
    }

    private int calculateGroupCount(GroupInfo groupInfo, List<Record> list) {
        int groupCount = 0;
        if (groupInfo.isAllGroup()) {
            //如果是全部录音，则剔除已入回收站的数据
            for (int j = 0; j < list.size(); j++) {
                Record record = list.get(j);
                if (!record.isRecycle()) {
                    groupCount++;
                }
            }
        } else if (groupInfo.isRecentlyDeleteGroup()) {
            //如果是最近删除分组，则只计入回收站的数据
            for (int j = 0; j < list.size(); j++) {
                Record record = list.get(j);
                if (record.isRecycle()) {
                    groupCount++;
                }
            }
        } else {
            //其他分组， 则按组各自计算非回收站的数据
            for (int j = 0; j < list.size(); j++) {
                Record record = list.get(j);
                if (!record.isRecycle() && groupInfo.getMUuId().equals(record.getGroupUuid())) {
                    groupCount++;
                }
            }
            DebugUtil.d(TAG, "calculateGroupCount, groupInfo = " + groupInfo.getMUuId() + ", groupCount = " + groupCount);
        }
        return groupCount;
    }
    private List<GroupInfo> processDefaultGroupCountFromMediaData(List<GroupInfo> groupInfoList) {
        //从媒体库获取准确的数据
        var startTime = System.currentTimeMillis();
        MediaCounter mediaCounter = GroupInfoDbUtil.queryAndCalculateAllMediaDataCount();
        DebugUtil.d(TAG, "isInsertingMediaData, queryAndCalculateAllMediaDataCount cost time = "
                + (System.currentTimeMillis() - startTime) + " ms");
        if (mediaCounter != null) {
            GroupInfoManager.getInstance(BaseApplication.getAppContext()).setTotalMediaCount(mediaCounter.getAllCount());
            DebugUtil.d(TAG, "isInsertingMediaData, allCount = " + mediaCounter.getAllCount()
                    + ", callCount = " + mediaCounter.getCallCount() + ", commonCount = " + mediaCounter.getCommonCount());
            for (int i = 0; i < groupInfoList.size(); i++) {
                GroupInfo groupInfo = groupInfoList.get(i);
                //自定义组及回收站组直接跳过不处理
                if (groupInfo.isCustomGroup() || groupInfo.isRecentlyDeleteGroup()) {
                    continue;
                }
                if (groupInfo.isCommonGroup()) {
                    groupInfo.setMGroupCount(mediaCounter.getCommonCount());
                } else if (groupInfo.isCallingGroup()) {
                    groupInfo.setMGroupCount(mediaCounter.getCallCount());
                } else if (groupInfo.isAllGroup()) {
                    groupInfo.setMGroupCount(mediaCounter.getAllCount());
                }
                GroupInfoDbUtil.updateGroupInfo(mContext, groupInfo);
            }
        }
        return groupInfoList;
    }

    private static String[] getCustomGroupIds(List<GroupInfo> groupInfoList) {
        List<String> tmpList = new ArrayList<>();
        for (int i = 0; i < groupInfoList.size(); i++) {
            GroupInfo groupInfo = groupInfoList.get(i);
            if (groupInfo.isCustomGroup()) {
                tmpList.add(groupInfo.getMUuId());
            }
        }
        if (!tmpList.isEmpty()) {
            return tmpList.toArray(new String[tmpList.size()]);
        }
        return null;
    }

    private static String createRecycleAndCustomGroupCountSQL(List<GroupInfo> groupInfoList) {
        String[] idList = getCustomGroupIds(groupInfoList);
        if (idList != null && idList.length > 0) {
            StringBuilder stringBuilder = new StringBuilder(COLUMN_NAME_GROUP_UUID + " in (");
            for (int i = 0; i < idList.length; i++) {
                if (i == idList.length - 1) {
                    stringBuilder.append("?)");
                } else {
                    stringBuilder.append("?, ");
                }
            }
            return COLUMN_NAME_SIZE + " > 0 " + " and (" + COLUMN_NAME_IS_RECYCLE + " = 1 or " + stringBuilder + ") ";
        }
        return COLUMN_NAME_SIZE + " > 0 " + " and " + COLUMN_NAME_IS_RECYCLE + " = 1";
    }
    /**
     * 获取所有有效的录音记录 （size > 0 且 文件存在）
     * @param context
     * @return
     */
    public static void calculateRecycleAndCustomGroupCount(Context context, List<GroupInfo> groupInfoList) {
        if (context == null) {
            DebugUtil.i(TAG, "calculateRecycleAndCustomGroupCount is empty, can not find the record");
            return;
        }
        Cursor cursor = null;
        List<Record> result = new ArrayList<>();
        try {
            String selection = createRecycleAndCustomGroupCountSQL(groupInfoList);
            Bundle queryBundle = createSqlQueryBundle(selection, getCustomGroupIds(groupInfoList), null, null, null);
            cursor = context.getContentResolver().query(RECORD_CONTENT_URI, null, queryBundle, null);
            if ((cursor != null) && (cursor.getCount() > 0)) {
                DebugUtil.d(TAG, "calculateRecycleAndCustomGroupCount, cursor.getCount() = " + cursor.getCount());
                HashMap<String, Integer> groupInfoHashMap = new HashMap<>();
                //重置自定义分组和最近删除分组的个数信息
                for (int i = 0; i < groupInfoList.size(); i++) {
                    GroupInfo groupInfo = groupInfoList.get(i);
                    if (groupInfo.isRecentlyDeleteGroup()) {
                        groupInfoHashMap.put(GroupInfoDbUtil.DEFAULT_RECENTLY_DELETE_UUID, 0);
                    } else if (groupInfo.isCustomGroup()) {
                        groupInfoHashMap.put(groupInfo.getMUuId(), 0);
                    }
                }
                Set<String> pathSet = new HashSet<>();
                while (cursor.moveToNext()) {
                    Record record = new Record(cursor, Record.TYPE_FROM_RECORD);
                    String filePath = record.isRecycle() ? record.getRecycleFilePath() : record.getData();
                    if (!TextUtils.isEmpty(filePath) && new File(filePath).exists()) {
                        //去重
                        if (pathSet.contains(filePath)) {
                            DebugUtil.d(TAG, "calculateRecycleAndCustomGroupCount, filePath = " + filePath + " is duplicate");
                            continue;
                        }
                        pathSet.add(filePath);
                        result.add(record);
                        calculateGroupCount(groupInfoHashMap, record);
                    }
                }
                // 更新自定义分组和最近删除分组的个数信息
                for (int i = 0; i < groupInfoList.size(); i++) {
                    GroupInfo groupInfo = groupInfoList.get(i);
                    if (groupInfo.isCustomGroup() || groupInfo.isRecentlyDeleteGroup()) {
                        Integer count = groupInfoHashMap.get(groupInfo.getMUuId());
                        if (count != null) {
                            groupInfo.setMGroupCount(count);
                        }
                        DebugUtil.d(TAG, "calculateRecycleAndCustomGroupCount, name = "
                                + groupInfo.getMGroupName() + ", count =" + groupInfo.getMGroupCount());
                        GroupInfoDbUtil.updateGroupInfo(context, groupInfo);
                    }
                }
            } else {
                for (int i = 0; i < groupInfoList.size(); i++) {
                    GroupInfo groupInfo = groupInfoList.get(i);
                    if (groupInfo.isCustomGroup() || groupInfo.isRecentlyDeleteGroup()) {
                        groupInfo.setMGroupCount(0);
                        GroupInfoDbUtil.updateGroupInfo(context, groupInfo);
                    }
                }
            }
        } catch (Exception e) {
            DebugUtil.e(TAG, "getValidRecordData : " + e.getMessage(), e);
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
    }

    private static void calculateGroupCount(HashMap<String, Integer> groupInfoHashMap, Record record) {
        //各组计数自增
        String groupUuid = record.getGroupUuid();
        String realGroupUUuid = record.isRecycle() ? GroupInfoDbUtil.DEFAULT_RECENTLY_DELETE_UUID : groupUuid;
        if (realGroupUUuid != null) {
            Integer count = groupInfoHashMap.get(realGroupUUuid);
            if (count != null) {
                groupInfoHashMap.put(realGroupUUuid, count + 1);
            }
        }
    }

    public List<GroupInfo> processGroupCount(List<GroupInfo> groupInfoList) {
        int mediaDbCount = GroupInfoDbUtil.getMediaCount();
        calculateRecycleAndCustomGroupCount(mContext, groupInfoList);

        if (mediaDbCount == 0) {
            for (int i = 0; i < groupInfoList.size(); i++) {
                GroupInfo groupInfo = groupInfoList.get(i);
                groupInfo.setMGroupCount(0);
                GroupInfoDbUtil.updateGroupInfo(mContext, groupInfo);
            }
        } else {
            //默认分组的数据从媒体库中统计
            processDefaultGroupCountFromMediaData(groupInfoList);
        }
        return groupInfoList;
    }
    public List<Record> queryNeedDecodeAmplitudeRecordFile() {
        String selection = COLUMN_NAME_AMP_FILE_PATH + " is null and " + DatabaseConstant.RecorderColumn.COLUMN_NAME_MIMETYPE + " = ?";
        String[] selectionArgs = new String[]{"audio/mpeg"};
        return getRecordData(mContext, null, selection, selectionArgs, null);
    }

    public long clearFailedCount(int clearLeastCount) {
        DebugUtil.i(TAG, "clearFailedCount: ");
        String selection = COLUMN_NAME_FAIL_COUNT + " >= ?";
        String[] selectionArgs = new String[]{String.valueOf(clearLeastCount < 0 ? MAX_COUNT : clearLeastCount)};
        String selectionId = COLUMN_NAME_ID + " = ? ";
        ContentValues cv = new ContentValues();
        List<Record> failedRecords = getRecordData(mContext, null, selection, selectionArgs, null);
        long totalCount = 0;
        if ((failedRecords != null) && !failedRecords.isEmpty()) {
            for (Record record : failedRecords) {
                cv.clear();
                cv.put(COLUMN_NAME_SYNC_DATE, 0);
                cv.put(COLUMN_NAME_FAIL_COUNT, 0);
                cv.put(COLUMN_NAME_LAST_FAIL_TIME, 0);
                if (record.getSyncStatus() == SYNC_STATUS_BACKUP_FILE_FAILED) {
                    cv.put(COLUMN_NAME_SYNC_UPLOAD_STATUS, SYNC_STATUS_BACKUP_START);
                } else if (record.getSyncDownlodStatus() == SYNC_STATUS_RECOVERY_FILE_FAILED) {
                    cv.put(COLOUM_NAME_SYNC_DOWNLOAD_STATUS, SYNC_STATUS_RECOVERY_MEGADATA_SUC);
                }
                String[] selectionArgId = new String[]{String.valueOf(record.getId())};
                int count = updateRecordData(mContext, cv, selectionId, selectionArgId);
                if (count > 0) {
                    totalCount = totalCount + count;
                }
            }
        }
        DebugUtil.i(TAG, "clearFailedCount: clear totalcount: " + totalCount);
        return totalCount;
    }

    public long resetSyncLockedState() {
        DebugUtil.i(TAG, "resetSyncLockedState: ");
        String selection = COLUMN_NAME_SYNC_TYPE + " = ? AND " + COLOUM_NAME_SYNC_DOWNLOAD_STATUS + " = ? OR "
                + COLUMN_NAME_SYNC_TYPE + " = ? AND " + COLUMN_NAME_SYNC_UPLOAD_STATUS + " = ?";
        String[] selectionArgs = new String[]{String.valueOf(SYNC_TYPE_RECOVERY), String.valueOf(SYNC_STATUS_RECOVERYING_FILE)
                , String.valueOf(SYNC_TYPE_BACKUP), String.valueOf(SYNC_STATUS_BACKUPING_FILE)};
        String selectionId = COLUMN_NAME_ID + " = ? ";
        ContentValues cv = new ContentValues();
        List<Record> lockedRecords = getRecordData(mContext, null, selection, selectionArgs, null);
        long totalCount = 0;
        if ((lockedRecords != null) && !lockedRecords.isEmpty()) {
            for (Record record : lockedRecords) {
                cv.clear();
                cv.put(COLUMN_NAME_LAST_FAIL_TIME, 0);
                if (record.getSyncType() == SYNC_TYPE_RECOVERY) {
                    cv.put(COLOUM_NAME_SYNC_DOWNLOAD_STATUS, SYNC_STATUS_RECOVERY_FILE_FAILED);
                } else if (record.getSyncType() == SYNC_TYPE_BACKUP) {
                    cv.put(COLUMN_NAME_SYNC_UPLOAD_STATUS, SYNC_STATUS_BACKUP_START);
                }
                String[] selectionArgId = new String[]{String.valueOf(record.getId())};
                int count = updateRecordData(mContext, cv, selectionId, selectionArgId);
                if (count > 0) {
                    totalCount = totalCount + count;
                }
            }
        }
        DebugUtil.i(TAG, "resetSyncLockedState:  " + totalCount);
        return totalCount;
    }

    public static String getCallRecordWhereClauseForRecord(Context context) {
        String phoneDir = BaseUtil.getPhoneStorageDir(context);
        if (phoneDir == null) {
            phoneDir = DEFAULT_DIR;
        }
        String sdcardDir = BaseUtil.getSDCardStorageDir(context);
        String relativeDir = RecordModeUtil.getRelativePathByRecordType(RecordModeConstant.RECORD_TYPE_CALL, false);
        String dirPhone = phoneDir + File.separator + relativeDir;
        String dirSDCard = sdcardDir + File.separator + relativeDir;
        String where = null;
        if (BaseUtil.isAndroidQOrLater()) {
            where = "(" + COLUMN_NAME_MIMETYPE + " in (?, ?, ?, ?)) AND (" + COLUMN_NAME_RELATIVE_PATH + " LIKE '" + relativeDir
                    + "%') AND (" + COLUMN_NAME_SIZE + "!=" + 0 + ")";
            DebugUtil.d(TAG, "getCallRecordWhereClause where is " + where);
        } else {
            where = "(" + COLUMN_NAME_MIMETYPE + " in (?, ?, ?, ?)) AND (" + COLUMN_NAME_DATA + " LIKE '" + dirPhone
                    + "%' OR " + COLUMN_NAME_DATA + " LIKE '" + dirSDCard + "%') AND (" + COLUMN_NAME_SIZE + "!=" + 0 + ")";
            DebugUtil.d("CursorHelper", "getCallRecordWhereClause where is " + where);
        }
        return where;
    }

    public static String getWhereClauseFromRecorderType(Context context, int recordType, String[] supportMimeType) {
        String phoneDir = BaseUtil.getPhoneStorageDir(context);
        if (phoneDir == null) {
            phoneDir = DEFAULT_DIR;
        }
        String sdcardDir = BaseUtil.getSDCardStorageDir(context);
        String bucketIdDirPhone = RecordModeUtil.getBucketId(phoneDir + File.separator + Constants.RECORDINGS);
        String bucketIdDirSdcard = RecordModeUtil.getBucketId(sdcardDir + File.separator + Constants.RECORDINGS);
        String relativePath = getRelativePathFromRecorderType(recordType);
        String normalPhoneDir = phoneDir + File.separator + relativePath;
        String normalSdDir = sdcardDir + File.separator + relativePath;

        String where = "";
        if (BaseUtil.isAndroidQOrLater()) {
            where = getWhereFromRecorderTypeAboveQ(recordType, supportMimeType);
        } else {
            String mimeTypeInString = CursorHelper.getMimeTypeInString(COLUMN_NAME_MIMETYPE, supportMimeType);
            if (recordType == RecordModeConstant.RECORD_TYPE_CALL) {
                where = mimeTypeInString
                        + " AND (" + COLUMN_NAME_DATA + " LIKE '" + normalPhoneDir + "%' OR " + COLUMN_NAME_DATA + " LIKE '" + normalSdDir + "%'" + ")"
                        + " AND (" + COLUMN_NAME_SIZE + "!=" + 0 + ")";
            } else {
                where = mimeTypeInString
                        + " AND ((bucket_id in (" + bucketIdDirPhone
                        + "," + bucketIdDirSdcard + "))"
                        + " OR ( " + COLUMN_NAME_DATA + " LIKE '" + normalPhoneDir + "%' OR " + COLUMN_NAME_DATA + " LIKE '" + normalSdDir + "%'" + "))"
                        + " AND (" + COLUMN_NAME_SIZE + "!=" + 0 + ")";
            }
        }
        DebugUtil.i("CursorHelper", "getAllRecordWhereClause where is " + where);
        return where;
    }

    private static String getRelativePathFromRecorderType(int recordType) {
        String result = RecordModeUtil.getRelativePathByRecordType(RecordModeConstant.RECORD_TYPE_STANDARD, true);
        switch (recordType) {
            case RecordModeConstant.RECORD_TYPE_STANDARD:
            case RecordModeConstant.RECORD_TYPE_CONFERENCE:
            case RecordModeConstant.RECORD_TYPE_INTERVIEW:
            case RecordModeConstant.RECORD_TYPE_CALL:
            case RecordModeConstant.RECORD_TYPE_OPPO_SHARE:
                result = RecordModeUtil.getRelativePathByRecordType(recordType, true);
                break;
            case RecordModeConstant.RECORD_TYPE_OTHER:
                result = RecordModeConstant.STORAGE_RECORD_ABOVE_Q;
                break;
            default:
                break;
        }
        return result;
    }

    private static String getWhereFromRecorderTypeAboveQ(int recordType, String[] supportMimeType) {
        String relativePath = getRelativePathFromRecorderType(recordType);
        String where = "";
        String mimeTypeInString = CursorHelper.getMimeTypeInString(COLUMN_NAME_MIMETYPE, supportMimeType);
        switch (recordType) {
            case RecordModeConstant.RECORD_TYPE_STANDARD:
            case RecordModeConstant.RECORD_TYPE_CONFERENCE:
            case RecordModeConstant.RECORD_TYPE_INTERVIEW:
            case RecordModeConstant.RECORD_TYPE_CALL:
            case RecordModeConstant.RECORD_TYPE_OPPO_SHARE:
                where = mimeTypeInString
                        + " AND (" + COLUMN_NAME_RELATIVE_PATH + " LIKE '" + relativePath + "%')"
                        + " AND (" + COLUMN_NAME_SIZE + "!=" + 0 + ")";
                break;
            case RecordModeConstant.RECORD_TYPE_OTHER:
                where = mimeTypeInString
                        + " AND (" + COLUMN_NAME_RELATIVE_PATH + " LIKE '" + relativePath + "')"
                        + " AND (" + COLUMN_NAME_SIZE + "!=" + 0 + ")";
                break;
            default:
                break;
        }
        return where;
    }

    public boolean markRecordAsPrivateStatus(long id, boolean isEncrypt) {
        if (id < 0) {
            DebugUtil.i(TAG, "markRecordAsDeleted id < 0");
            return false;
        }
        try {
            ContentValues cv = new ContentValues();
            int privateState = isEncrypt ? RECORD_PRIVETE_ENCRYPT : RECORD_PRIVATE_NORMAL;
            cv.put(DatabaseConstant.RecorderColumn.COLUMN_NAME_PRIVATE_STATUS, privateState);
            String where = COLUMN_NAME_ID + " = ?";
            int updateCount = mContext.getContentResolver().update(RECORD_CONTENT_URI,
                    cv, where, new String[]{String.valueOf(id)});
            return updateCount > 0;
        } catch (Exception e) {
            DebugUtil.e(TAG, "markRecordAsPrivateStatus error " + e);
        }
        return false;
    }

    public boolean processEncrypDeleteAudioFile(String displayName, String relativePath, String md5) {
        DebugUtil.e(TAG, "processEncrypDeleteAudioFile: displayName" + displayName + ", relativePath :" + relativePath + ", md5: " + md5);
        if (TextUtils.isEmpty(displayName) || TextUtils.isEmpty(relativePath) || TextUtils.isEmpty(md5)) {
            return false;
        } else {
            String where = COLUMN_NAME_DISPLAY_NAME + " =? AND " + COLUMN_NAME_RELATIVE_PATH + " COLLATE NOCASE =? AND " + COLUMN_NAME_MD5 + " = ?";
            String[] whereArgs = new String[]{displayName, relativePath, md5};
            List<Record> records = getRecordData(mContext, null, where, whereArgs, null);
            if ((records != null) && (records.size() > 0)) {
                DebugUtil.i(TAG, "processEncrypDeleteAudioFile: find Records size " + records.size());
                boolean processSuc = false;
                for (Record record : records) {
                    boolean fileExist = record.fileExist();
                    boolean isInEncrypBox = (record.getSyncPrivateStatus() == RECORD_PRIVETE_ENCRYPT);
                    if (!fileExist && isInEncrypBox) {
                        processSuc |= deleteRecordsById(record.getId());
                    } else {
                        //do nothing, should not into this branch.
                        DebugUtil.e(TAG, "processEncrypDeleteAudioFile: fileExist: " + fileExist + ", inInEncryptBox: " + isInEncrypBox + ", return");
                        processSuc = false;
                    }
                }
                return processSuc;
            } else {
                DebugUtil.i(TAG, "no record found in local db, do nothing and return");
                return false;
            }
        }
    }

    public boolean deleteRecordsById(long id) {
        String where = COLUMN_NAME_ID + " = ? ";
        int updateCount = deleteRecordData(mContext, where, new String[]{String.valueOf(id)});
        DebugUtil.i(TAG, "deleteRecordsById: id deleteCount: " + updateCount);
        return updateCount > 0;
    }

    public boolean processDecrypAudioFile(String displayName, String relativePath, String md5) {
        Record mediaRecord = MediaDBUtils.queryRecordByRelativePathAndDisplayName(relativePath, displayName);
        boolean result = false;
        if (mediaRecord != null && Arrays.asList(CursorHelper.getsAcceptableAudioTypes()).contains(mediaRecord.getMimeType())) {
            String uuID = UUID.randomUUID().toString();
            mediaRecord.setUuid(uuID);
            mediaRecord.checkMd5();
            int recordType = RecordModeUtil.getRecordTypeForMediaRecord(mediaRecord);
            if (TextUtils.isEmpty(mediaRecord.getRelativePath())) {
                String generatedRelativePath = RecorderDBUtil.getRelativePathForData(mediaRecord.getData(), mediaRecord.getDisplayName());
                mediaRecord.setRelativePath(generatedRelativePath);
                DebugUtil.i(TAG, "processInSertData: setRelativePath: " + relativePath);
            }
            mediaRecord.setRecordType(recordType);
            mediaRecord.setSyncType(RecordConstant.SYNC_TYPE_BACKUP);
            mediaRecord.setSyncUploadStatus(RecordConstant.SYNC_STATUS_BACKUP_START);
            mediaRecord.setDirty(RecordConstant.RECORD_DIRTY_FILE_AND_MEGA);
            mediaRecord.setSyncPrivateStatus(RecordConstant.RECORD_PRIVATE_NORMAL);
            ContentValues cv = mediaRecord.convertToContentValues();
            Record dbRecord = getRecordByRelativePathAndDisplayName(relativePath, displayName);
            if (dbRecord != null) {
                boolean isdbRecordInCloud = !TextUtils.isEmpty(dbRecord.getGlobalId());
                boolean isSameFile = mediaRecord.getMD5().equalsIgnoreCase(dbRecord.getMD5());
                if (isSameFile) {
                    // same file don't erase mark data and ampda
                    cv.remove(COLUMN_NAME_MARK_DATA);
                    cv.remove(COLUMN_NAME_AMP_DATA);
                } else {
                    cv.put(COLUMN_NAME_AMP_FILE_PATH, (String) null);
                    cv.put(COLUMN_NAME_MARK_DATA, (String) null);
                    //mark表的操作，去掉之前的picture表中的脏数据
                    String keyId = dbRecord.getId() + "";
                    PictureMarkDbUtils.INSTANCE.deletePictureMarks(keyId);
                    //todo convert数据，amp文件等都需要删除
                    if (isdbRecordInCloud) {
                        // this record is inCloud and DecryptMediaRecord is the same name , but the md5 not the same, the encrypbox override this file with an same name different file;
                        cv.put(COLUMN_NAME_FILE_ID, (String) null);
                        cv.put(COLUMN_NAME_GLOBAL_ID, (String) null);
                        cv.put(COLUMN_NAME_CLOUD_CHECK_PAYLOAD, (String) null);
                        cv.put(COLUMN_NAME_CLOUD_SYS_VERSION, 0);
                        DebugUtil.i(TAG, "processDecrypAudioFile: displayName: " + displayName + ", relativePath: " + relativePath + ", same file name, md5 not equal, trig upload files ");
                    }
                }
                String where = COLUMN_NAME_ID + " = ?";
                String[] whereArg = new String[]{String.valueOf(dbRecord.getId())};
                int updateCount = updateRecordData(BaseApplication.getAppContext(), cv, where, whereArg);
                result = updateCount > 0;
                DebugUtil.i(TAG, "processDecrypAudioFile displayname: " + displayName + ", relativePath: " + relativePath + ", update result: " + updateCount);
            } else {
                Uri insertUri = insertRecordData(BaseApplication.getAppContext(), cv);
                result = (insertUri != null);
                DebugUtil.i(TAG, "processDecrypAudioFile displayname: " + displayName + ", relativePath: " + relativePath + ", insert result: " + insertUri);
            }
            return result;
        } else {
            DebugUtil.i(TAG, "processDecrypAudioFile, no media File found, so do nothing return");
            return false;
        }
    }

    public static void writeMarkData(String inputName, Uri uri, List<MarkDataBean> markList) {
        Context context = BaseApplication.getAppContext();
        if (TextUtils.isEmpty(inputName)) {
            DebugUtil.i(TAG, "input name is empty, return");
            return;
        }
        if (FileUtils.checkFileNotExistOrFileSizeZero(inputName, uri)) return;
        String markString = MarkSerializUtil.INSTANCE.convertStoredDBStringForMarkDataBeanList(markList);
        Record record = RecorderDBUtil.getInstance(context).qureyRecordByPath(inputName);
        if (record != null) {
            DebugUtil.i(TAG, "writeMarkData: id " + record.getId() + ", markString: " + markString);
            RecorderDBUtil.getInstance(context).updateRecordMarkById(record.getId(), markString);
        } else {
            DebugUtil.e(TAG, "writeMarkData no record found in recordDb, update error");
        }
    }

    /**
     * 将文本标记解析之后更新到Mark表中，更新策略是跟原有逻辑合并
     *
     * @param recordId 本地Record表中的主键id
     * @param markData 本地Record表中的文本标记
     */
    public void checkAndExactMarkDataToMarkTable(long recordId, byte[] markData) {
        if (markData != null) {
            String markString = new String(markData, Charset.forName("UTF-8"));
            List<MarkDataBean> marksFromMarkString = MarkSerializUtil.INSTANCE.parseMarkDataBeanListFromString(markString);
            String keyId = recordId + "";
            List<MarkDataBean> marksFromMarkTable = PictureMarkDbUtils.INSTANCE.queryPictureMarks(keyId);
            MarkProcessUtil.MarkDiffResult diffResult = MarkProcessUtil.INSTANCE.diffMarkList(marksFromMarkTable, marksFromMarkString);
            if (diffResult.getAddMarks().size() > 0) {
                for (MarkDataBean addMark : diffResult.getAddMarks()) {
                    PictureMarkDbUtils.addPictureMark(keyId, addMark);
                    DebugUtil.i(TAG, "checkAndExactMarkDataToMarkTable addMarks: " + addMark);
                }
            }
            if (diffResult.getUpdateMarks().size() > 0) {
                for (MarkDataBean updateMark : diffResult.getUpdateMarks()) {
                    PictureMarkDbUtils.INSTANCE.updatePictureMark(keyId, updateMark);
                    DebugUtil.i(TAG, "checkAndExactMarkDataToMarkTable updateMarks: " + updateMark);
                }
            }
        }
    }


    /**
     * 跟新或插入MARK数据库
     *
     * @param keyId          keyId， 对应Record表中的主键id，为String类型
     * @param directlyInsert 是否需要直接插入Mark表  true表示直接插入，不需要在mark表中查询更新； false表示需要在mark表中查询更新mark表中的数据
     * @param markData       需要更新的mark数据，这里的为byte数组，只包含了文字标记，不包含图片标记
     * @param forceUpdate    是否使用新的markData进行强制更新，强制更新的意思是将数据db中本来存在的数据和新的标记数据对比，数据库中存在而在更新的标记数据中没有的部分需要强制删除。
     */

    public static void updateOrInsertMarkInNewTable(String keyId, boolean directlyInsert, byte[] markData, boolean forceUpdate) {
        String textMarkString = new String(markData, Charset.forName("UTF-8"));
        DebugUtil.i(TAG, "updateOrInsertMarkInNewTable input keyId: " + keyId + ", markMarkString: " + textMarkString);
        List<MarkDataBean> newMarkBeans = MarkSerializUtil.INSTANCE.parseMarkDataBeanListFromString(textMarkString);
        if ((newMarkBeans != null) && (newMarkBeans.size() > 0)) {
            if (directlyInsert) {
                PictureMarkDbUtils.addPictureMarks(keyId, newMarkBeans);
            } else {
                queryAndUpdateMarkDb(keyId, newMarkBeans, forceUpdate, true);
            }
        }
    }

    /**
     * 跟新或插入MARK数据库
     *
     * @param keyId          keyId， 对应Record表中的主键id，为String类型
     * @param directlyInsert 是否需要直接插入Mark表  true表示直接插入，不需要在mark表中查询更新； false表示需要在mark表中查询更新mark表中的数据
     * @param newMarkBeans   需要更新的mark数据，这里的标记列表中可能包含图片标记，也可能包含文字标记
     * @param forceUpdate    是否使用新的markData进行强制更新，强制更新的意思是将数据db中本来存在的数据和新的标记数据对比，数据库中存在而在更新的标记数据中没有的部分需要强制删除。
     */
    private static void updateOrInsertMarkInNewTable(String keyId, boolean directlyInsert, List<MarkDataBean> newMarkBeans, boolean forceUpdate) {
        DebugUtil.i(TAG, "updateOrInsertMarkInNewTable input newMarkBeans: " + newMarkBeans + "");
        if ((newMarkBeans != null) && (newMarkBeans.size() > 0)) {
            if (directlyInsert) {
                PictureMarkDbUtils.addPictureMarks(keyId, newMarkBeans);
            } else {
                queryAndUpdateMarkDb(keyId, newMarkBeans, forceUpdate, false);
            }
        }
    }


    /**
     * 跟新或插入MARK数据库
     *
     * @param keyId         keyId， 对应Record表中的主键id，为String类型
     * @param newMarkBeans  需要更新的mark数据，这里的标记列表中可能包含图片标记，也可能包含文字标记
     * @param forceUpdate   是否使用新的markData完全覆盖本地数据库数据， true -> 强制覆盖(数据库中存在而在更新的标记数据中没有的部分需要在数据库中强制删除） false-> 不强制覆盖，只做add和update，不做相关的delete操作。
     * @param onlyUpateText 是否在覆盖逻辑中只删除文本标记, true -> 强制覆盖场景，只删除text标记，不删除图片标记； false->强制覆盖场景，无论图片标记还是文本标记都删除覆盖。
     *                      <p>
     *                      <p>
     *                      一般来说 onlyUpateText = true的场景是云端同步标记到本地，云端的文本标记相比本地发生了减少了若干个，同时本地存在图片标记（图片标记暂时不上云，所以图片标记始终在本地，云端不存在）的情况下，需要动态删除本地的文本标记，而不删除图片标记。
     */
    private static void queryAndUpdateMarkDb(String keyId, List<MarkDataBean> newMarkBeans, boolean forceUpdate, boolean onlyUpateText) {
        List<MarkDataBean> dbMarkBeans = PictureMarkDbUtils.INSTANCE.queryPictureMarks(keyId);
        if (dbMarkBeans != null && dbMarkBeans.size() > 0) {
            MarkProcessUtil.MarkDiffResult result = MarkProcessUtil.INSTANCE.diffMarkList(dbMarkBeans, newMarkBeans);
            if (result.getAddMarks().size() > 0) {
                for (MarkDataBean addMark : result.getAddMarks()) {
                    PictureMarkDbUtils.addPictureMark(keyId, addMark);
                    DebugUtil.i(TAG, "updateOrInsertMarkInNewTable addMarks: " + addMark);
                }
            }
            if (result.getUpdateMarks().size() > 0) {
                for (MarkDataBean updateMark : result.getUpdateMarks()) {
                    PictureMarkDbUtils.INSTANCE.updatePictureMark(keyId, updateMark);
                    DebugUtil.i(TAG, "updateOrInsertMarkInNewTable updateMarks: " + updateMark);
                }
            }
            if (forceUpdate) {
                if (result.getDeleteMarks().size() > 0) {
                    for (MarkDataBean deleteMark : result.getDeleteMarks()) {
                        boolean markIsTextType = deleteMark.isTextType();
                        if (onlyUpateText) {
                            if (markIsTextType) {
                                PictureMarkDbUtils.INSTANCE.deletePictureMark(keyId, deleteMark.getTimeInMills());
                                DebugUtil.i(TAG, "updateOrInsertMarkInNewTable deleteMarks: " + deleteMark);
                            } else {
                                //not text type, no need delete Text Mark
                                DebugUtil.i(TAG, "updateOrInsertMarkInNewTable no text type, no need delete text mark: ");
                            }
                        } else {
                            PictureMarkDbUtils.INSTANCE.deletePictureMark(keyId, deleteMark.getTimeInMills());
                            DebugUtil.i(TAG, "updateOrInsertMarkInNewTable deleteMarks: " + deleteMark);
                        }
                    }
                }
            }
        } else {
            PictureMarkDbUtils.addPictureMarks(keyId, newMarkBeans);
        }
    }


    public Record insertInitRecordIntoDbForStartRecord(String disPlayName, long currentTime, String mimeType, String mRelativePath, String allPath, int recordType) {
        DebugUtil.i(TAG, "insertInitRecordIntoDbForStartRecord disPlayName: " + disPlayName + ", currentTime: " + currentTime + ", mimeType: " + mimeType +
                ",mRelativePath: " + mRelativePath + ", ");

        ContentResolver resolver = mContext.getContentResolver();
        Uri base = RECORD_CONTENT_URI;
        ContentValues cv = getRecordDbContentValues(disPlayName, currentTime, mimeType, mRelativePath, allPath, recordType);
        Record inputRecord = new Record();
        inputRecord.setDisplayName(disPlayName);
        inputRecord.setUuid(cv.getAsString(COLUMN_NAME_UUID));
        inputRecord.setDateCreated(currentTime / TIME_ONE_SECOND);
        inputRecord.setDateModied(currentTime / TIME_ONE_SECOND);
        inputRecord.setMimeType(mimeType);
        inputRecord.setRelativePath(mRelativePath);
        inputRecord.setData(allPath);
        inputRecord.setRecordType(recordType);
        Uri outPutUri = null;
        long resultId = -1;
        try {
            outPutUri = resolver.insert(base, cv);
        } catch (Exception e) {
            DebugUtil.e(TAG, "addToMediaDB query get Exception ", e);
        } finally {
            if (outPutUri != null) {
                resultId = ContentUris.parseId(outPutUri);
            }
        }
        if (outPutUri != null) {
            inputRecord.setId(ContentUris.parseId(outPutUri));
            return inputRecord;
        }
        return null;
    }


    private ContentValues getRecordDbContentValues(String disPlayName, long currentTime, String mimeType, String mRelativePath, String allPath, int recordType) {
        ContentValues values = new ContentValues();
        //no size, duration, ampdata, markdata here
        values.put(COLUMN_NAME_DISPLAY_NAME, disPlayName);
        values.put(COLUMN_NAME_UUID, UUID.randomUUID().toString());
        values.put(COLUMN_NAME_DATE_CREATED, currentTime / TIME_ONE_SECOND);
        values.put(COLUMN_NAME_DATE_MODIFIED, currentTime / TIME_ONE_SECOND);
        values.put(COLUMN_NAME_MIMETYPE, mimeType);
        values.put(COLUMN_NAME_RELATIVE_PATH, mRelativePath);
        values.put(COLUMN_NAME_DATA, allPath);
        values.put(COLUMN_NAME_RECORD_TYPE, recordType);
        return values;
    }

    public static String getRelativePathForData(String path, String displayName) {
        String fileName = "";
        if (TextUtils.isEmpty(path) || TextUtils.isEmpty(displayName)) {
            return fileName;
        }
        String parent = BaseUtil.getPhoneStorageDir(BaseApplication.getAppContext()) + File.separator;
        String relativePath = path.replace(parent, "").replace(displayName, "");
        return relativePath;
    }

    public boolean updateCallNameByRecordId(String rowId, String callName, boolean setNeedUpload) {
        Record record = getRecordById(rowId);
        DebugUtil.i(TAG, "updateCallNameByRecordId, newName: " + callName + ", Record: " + record);
        if (record != null) {
            String where = COLUMN_NAME_ID + " = ?";
            String[] whereArgs = new String[]{String.valueOf(record.getId())};
            ContentValues contentValues = new ContentValues();

            contentValues.put(COLUMN_NAME_CALL_NAME, callName);
            contentValues.put(COLUMN_NAME_PARSE_CALL, 1);
            if (setNeedUpload) {
                contentValues.put(DatabaseConstant.RecorderColumn.COLUMN_NAME_DIRTY, RECORD_DIRTY_MEGA_ONLY);
                contentValues.put(DatabaseConstant.RecorderColumn.COLUMN_NAME_SYNC_TYPE, SYNC_TYPE_BACKUP);
                contentValues.put(COLUMN_NAME_SYNC_UPLOAD_STATUS, SYNC_STATUS_BACKUP_START);
            }
            int updateResult = updateRecordData(mContext, contentValues, where, whereArgs);
            DebugUtil.d(TAG, "updateCallNameByRecordId, updateResult:" + updateResult);
            return updateResult > 0;
        }
        return false;
    }

    public static void insertCallRecordingsIfNeed(String originalData, Record mediaRecordReal, String oldName, String newName, String newPath) {
        if (mediaRecordReal != null && RecorderDBUtil.getRecordByData(originalData) == null) {
            // 初次插入需要用修改后的display name和path。
            mediaRecordReal.setDisplayName(newName);
            mediaRecordReal.setData(newPath);
            if (mediaRecordReal.getOriginalName() == null) {
                mediaRecordReal.setOriginalName(oldName);
            }
            CloudKitInterface cloudkit = KoinInterfaceHelper.INSTANCE.getCloudKitApi();
            mediaRecordReal.setCallAvatarColor(CircleTextImageUtil.INSTANCE.getRandomColor());
            if (cloudkit != null && cloudkit.isMediaComparing()) {
                // 存在媒体库同步到record表任务，则需要先停掉，等插入任务完成后再继续。
                cloudkit.doStopMediaCompare(true);
                RecordDataSync.getInstance().processInsertData(BaseApplication.getAppContext(), List.of(mediaRecordReal));
                cloudkit.doMediaCompare(false, -1);
            } else {
                RecordDataSync.getInstance().processInsertData(BaseApplication.getAppContext(), List.of(mediaRecordReal));
            }
        }
    }
}
