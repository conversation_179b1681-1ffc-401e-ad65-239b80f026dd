package com.soundrecorder.common.buryingpoint;

import com.soundrecorder.base.BaseApplication;
import com.soundrecorder.base.utils.DebugUtil;

import java.util.HashMap;

import kotlin.jvm.JvmStatic;

public class HoverStaticsUtil {

    public static final String TAG = "HoverStaticsUtil";
    public static final String USER_ACTION_HOVER = "hover_event";
    public static final String EVENT_USE_HOVER_OF_PAGE = "use_hover_of_page";
    public static final String KEY_HOVER_NUMBER = "open_hover_num";

    /**
     * 统计用户进入悬停模式次数
     */
    @JvmStatic
    public static void addHoverNumEvent(String hoverFrom) {
        HashMap<String, String> eventInfo = new HashMap<>();
        eventInfo.put(KEY_HOVER_NUMBER, hoverFrom);
        RecorderUserAction.addNewCommonUserAction(BaseApplication.getAppContext(),
                USER_ACTION_HOVER,
                EVENT_USE_HOVER_OF_PAGE,
                eventInfo, false);
        DebugUtil.i(TAG, "addHoverNumEvent");
    }
}