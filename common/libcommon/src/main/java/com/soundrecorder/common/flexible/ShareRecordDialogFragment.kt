/*********************************************************************
 * * Copyright (C), 2023, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  ConvertTaskAction.kt
 * * Description :
 * * Version     : 1.0
 * * Date        : 2025/5/29
 * * Author      : ********
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.common.flexible

import android.annotation.SuppressLint
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.preference.Preference
import com.coui.appcompat.contextutil.COUIContextUtil
import com.coui.appcompat.panel.COUIBottomSheetDialog
import com.coui.appcompat.panel.COUIBottomSheetDialogFragment
import com.coui.appcompat.panel.COUIPanelFragment
import com.coui.appcompat.preference.COUIJumpPreference
import com.coui.appcompat.preference.COUIPreferenceFragment
import com.soundrecorder.common.R
import com.soundrecorder.common.share.ShareSupportHelper

class ShareRecordDialogFragment : COUIPanelFragment() {
    companion object {
        const val TAG = "ShareRecordDialogFragment"
        const val SHARE_TAG = "ShareRecordDialogFragment"
    }

    private var mPreferenceDialog = ShareRecordDialogPreferenceFragment()
    private var clickCallback: ((Int) -> Unit)? = null
    private var mHasSummary = true
    override fun initView(panelView: View?) {
        super.initView(panelView)
        initObserves()
        initToolbar()
        initPreference()
        initDialogContent(panelView)
    }

    private fun initObserves() {
        mPreferenceDialog.setOnShareRecordDialogClickEventCallBack(mHasSummary) { type ->
            clickCallback?.invoke(type)
        }
    }

    @SuppressLint("CommitTransaction")
    private fun initPreference() {
        childFragmentManager.beginTransaction().replace(contentResId, mPreferenceDialog).commit()
    }

    private fun initDialogContent(panelView: View?) {
        hideDragView()
    }

    private fun initToolbar() {
        toolbar = toolbar?.apply {
            title = resources.getString(R.string.share_record)
            isTitleCenterStyle = true
            inflateMenu(R.menu.menu_share_cancel)
            menu.findItem(R.id.item_cancel_share_dialog).apply {
                setOnMenuItemClickListener {
                    dismissDialog()
                    true
                }
            }
        }
    }

    override fun onShow(isShowOnFirstPanel: Boolean?) {
        super.onShow(isShowOnFirstPanel)
        //设置dialogFragment的背景
        ((parentFragment as? COUIBottomSheetDialogFragment)?.dialog as? COUIBottomSheetDialog)?.setPanelBackgroundTintColor(
            COUIContextUtil.getAttrColor(
                context,
                com.support.appcompat.R.attr.couiColorSurfaceWithCard
            )
        )
    }

    private fun dismissDialog() {
        (parentFragment as? COUIBottomSheetDialogFragment)?.dismiss()
    }

    override fun onDestroy() {
        super.onDestroy()
        if (clickCallback != null) {
            clickCallback = null
        }
    }

    fun setOnShareRecordDialogClickEvent(hasSummary: Boolean, callback: (Int) -> Unit) {
        mHasSummary = hasSummary
        clickCallback = callback
    }
}

class ShareRecordDialogPreferenceFragment : COUIPreferenceFragment() {
    companion object {
        private const val SHARE_RECORD_SHARE_LINK = "share_record_share_link"
        private const val SHARE_RECORD_SHARE_AUDIO = "share_record_share_audio"
        private const val SHARE_RECORD_SHARE_TEXT = "share_record_share_text"
        private const val SHARE_RECORD_SHARE_SUMMARY = "share_record_share_summary"
    }

    private var mShareRecordShareLink: COUIJumpPreference? = null
    private var mShareRecordShareAudio: COUIJumpPreference? = null
    private var mShareRecordShareText: COUIJumpPreference? = null
    private var mShareRecordShareSummary: COUIJumpPreference? = null
    private var mCallBack: ((Int) -> Unit)? = null
    private var mHasSummary = true
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        val onCreateView = super.onCreateView(inflater, container, savedInstanceState)
        //去掉原本的toolbar
        onCreateView?.let {
            (it as? ViewGroup)?.apply {
                removeView(it.findViewById(com.support.preference.R.id.appbar_layout))
            }
        }
        return onCreateView
    }

    override fun onCreatePreferences(savedInstanceState: Bundle?, rootKey: String?) {
        addPreferencesFromResource(R.xml.share_record_dialog_preference)
        mShareRecordShareLink = findPreference(SHARE_RECORD_SHARE_LINK)
        mShareRecordShareAudio = findPreference(SHARE_RECORD_SHARE_AUDIO)
        mShareRecordShareText = findPreference(SHARE_RECORD_SHARE_TEXT)
        mShareRecordShareSummary = findPreference(SHARE_RECORD_SHARE_SUMMARY)
        mShareRecordShareSummary?.isVisible = mHasSummary
    }

    override fun onPreferenceTreeClick(preference: Preference?): Boolean {
        if (preference == null) {
            return super.onPreferenceTreeClick(preference)
        }
        when (preference.key) {
            SHARE_RECORD_SHARE_LINK -> mCallBack?.invoke(ShareSupportHelper.SHARE_TYPE_LINK)
            SHARE_RECORD_SHARE_AUDIO -> mCallBack?.invoke(ShareSupportHelper.SHARE_TYPE_AUDIO)
            SHARE_RECORD_SHARE_TEXT -> mCallBack?.invoke(ShareSupportHelper.SHARE_TYPE_TEXT)
            SHARE_RECORD_SHARE_SUMMARY -> mCallBack?.invoke(ShareSupportHelper.SHARE_TYPE_SUMMARY)
        }
        return super.onPreferenceTreeClick(preference)
    }

    fun setOnShareRecordDialogClickEventCallBack(hasSummary: Boolean, callback: (Int) -> Unit) {
        mHasSummary = hasSummary
        mCallBack = callback
    }

    override fun onDestroy() {
        super.onDestroy()
        if (mCallBack != null) {
            mCallBack = null
        }
    }
}