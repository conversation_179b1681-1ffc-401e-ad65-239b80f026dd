/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: TaskBarUtil
 * Description:
 * Version: 1.0
 * Date: 2023/6/7
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2023/6/7 1.0 create
 */

package com.soundrecorder.common.utils.taskbar

import android.app.Activity
import androidx.annotation.ColorRes
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.BaseUtil
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.FeatureOption
import com.soundrecorder.base.utils.ScreenUtil
import com.soundrecorder.base.utils.StatusBarUtil
import com.soundrecorder.common.R

object TaskBarUtil {

    /**
     * 适配支持taskBar的设备导航栏栏颜色
     */
    @JvmStatic
    fun setNavigationColorOnSupportTaskBar(
        navigationHeight: Int,
        activity: Activity?,
        @ColorRes defaultNoTaskBarColor: Int? = null
    ) {
        setNavigationColorOnSupportTaskBar(navigationHeight, activity, defaultNoTaskBarColor, null)
    }

    @JvmStatic
    fun setNavigationColorOnSupportTaskBar(
        navigationHeight: Int,
        activity: Activity?,
        @ColorRes defaultNoTaskBarColor: Int? = null,
        @ColorRes taskBarColor: Int? = null
    ) {
        if ((activity == null) || !isSupportTaskBar()) {
            // 不支持taskbar的设备上不重复设置
            return
        }
        if (isTaskBarShowing(navigationHeight, activity)) {
            StatusBarUtil.setNavigationBarColor(activity, taskBarColor ?: R.color.background_taskbar_navigation)
        } else {
            StatusBarUtil.setNavigationBarColor(activity, defaultNoTaskBarColor ?: com.soundrecorder.base.R.color.common_background_color)
        }
    }

    @JvmStatic
    fun isTaskBarShowing(navigationHeight: Int, activity: Activity): Boolean { // 1.判断是否支持taskBar
        if (!isSupportTaskBar()) {
            DebugUtil.d("TaskBarUtil", "not support task bar")
            return false
        }
        if (ScreenUtil.isSmallScreen(activity)) {
            return false
        }
        if (navigationHeight > activity.resources.getDimensionPixelSize(R.dimen.navigation_gesture_taskbar_limit)) {
            DebugUtil.d("TaskBarUtil", "taskbar showing navigationHeight=$navigationHeight")
            return true
        }
        return false
    }

    /**
     * Android 版本号判断，Android T 及以上才有的特性
     * ColorOS 版本判断，ColorOS13.2 及以上（考虑回落建议隔离到 ColorOS13）
     * 大屏设备：平板、折叠大屏
     * 通过项目 AppFeature 判断： "com.android.launcher.TASKBAR_ENABLE"
     * 通过 Taskbar 动态开关 Settings 判断（不推荐，搬家、升级场景存在默认值等问题）-"enable_launcher_taskbar"
     */
    @JvmStatic
    fun isSupportTaskBar(): Boolean {
        return BaseUtil.isAndroidTOrLater && FeatureOption.isTaskBarEnable(BaseApplication.getAppContext())
    }
}