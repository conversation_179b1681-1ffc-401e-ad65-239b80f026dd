/*
 * Copyright (C), 2008-2024 OPLUS Mobile Comm Corp., Ltd.
 * File: PermissionStateManager
 * Description: 权限状态管理器，统一管理隐私弹框和权限弹框的状态
 * Version: 1.0
 * Date: 2024/12/19
 * Author: AI Assistant
 */

package com.soundrecorder.common.permission

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.permission.PermissionUtils.SHOULD_REQUEST_PERMISSIONS
import com.soundrecorder.common.permission.PermissionUtils.SHOULD_SHOW_ALL_FILE_PERMISSION
import com.soundrecorder.common.permission.PermissionUtils.SHOULD_SHOW_USER_NOTICE

/**
 * 权限状态管理器
 * 统一管理隐私弹框和权限弹框的状态，确保数据加载在权限完全授予后进行
 */
object PermissionStateManager {
    
    private const val TAG = "PermissionStateManager"
    
    /**
     * 权限状态枚举
     */
    enum class PermissionState {
        /** 需要显示用户须知弹框 */
        NEED_SHOW_USER_NOTICE,
        /** 需要显示文件访问权限弹框 */
        NEED_SHOW_FILE_PERMISSION,
        /** 需要请求运行时权限 */
        NEED_REQUEST_RUNTIME_PERMISSIONS,
        /** 所有权限已授予，可以加载数据 */
        ALL_PERMISSIONS_GRANTED,
        /** 权限被拒绝 */
        PERMISSIONS_DENIED
    }
    
    private val _permissionState = MutableLiveData<PermissionState>()
    val permissionState: LiveData<PermissionState> = _permissionState
    
    private val _canLoadData = MutableLiveData<Boolean>()
    val canLoadData: LiveData<Boolean> = _canLoadData
    
    init {
        updatePermissionState()
    }
    
    /**
     * 更新权限状态
     */
    fun updatePermissionState() {
        val nextAction = PermissionUtils.getNextAction()
        val hasReadAudioPermission = PermissionUtils.hasReadAudioPermission()
        
        val newState = when {
            nextAction == SHOULD_SHOW_USER_NOTICE -> {
                PermissionState.NEED_SHOW_USER_NOTICE
            }
            nextAction == SHOULD_SHOW_ALL_FILE_PERMISSION -> {
                PermissionState.NEED_SHOW_FILE_PERMISSION
            }
            nextAction == SHOULD_REQUEST_PERMISSIONS && !hasReadAudioPermission -> {
                PermissionState.NEED_REQUEST_RUNTIME_PERMISSIONS
            }
            nextAction == SHOULD_REQUEST_PERMISSIONS && hasReadAudioPermission -> {
                PermissionState.ALL_PERMISSIONS_GRANTED
            }
            else -> {
                PermissionState.PERMISSIONS_DENIED
            }
        }
        
        DebugUtil.d(TAG, "updatePermissionState: $newState, nextAction: $nextAction, hasReadAudioPermission: $hasReadAudioPermission")
        
        _permissionState.value = newState
        _canLoadData.value = newState == PermissionState.ALL_PERMISSIONS_GRANTED
    }
    
    /**
     * 用户须知弹框同意后调用
     */
    fun onUserNoticeAgreed() {
        DebugUtil.d(TAG, "onUserNoticeAgreed")
        updatePermissionState()
    }
    
    /**
     * 文件访问权限授予后调用
     */
    fun onFilePermissionGranted() {
        DebugUtil.d(TAG, "onFilePermissionGranted")
        updatePermissionState()
    }
    
    /**
     * 运行时权限授予后调用
     */
    fun onRuntimePermissionGranted() {
        DebugUtil.d(TAG, "onRuntimePermissionGranted")
        updatePermissionState()
    }
    
    /**
     * 权限被拒绝后调用
     */
    fun onPermissionDenied() {
        DebugUtil.d(TAG, "onPermissionDenied")
        _permissionState.value = PermissionState.PERMISSIONS_DENIED
        _canLoadData.value = false
    }
    
    /**
     * 检查是否可以加载数据
     */
    fun isCanLoadData(): Boolean {
        return _canLoadData.value == true
    }
    
    /**
     * 检查当前权限状态
     */
    fun getCurrentState(): PermissionState {
        return _permissionState.value ?: PermissionState.NEED_SHOW_USER_NOTICE
    }
    
    /**
     * 重置状态（用于测试或特殊场景）
     */
    fun reset() {
        DebugUtil.d(TAG, "reset")
        updatePermissionState()
    }
}
