package com.soundrecorder.common.widget

import android.animation.ValueAnimator
import android.content.Context
import android.util.AttributeSet
import androidx.appcompat.widget.AppCompatTextView
import androidx.core.content.ContextCompat
import com.coui.appcompat.contextutil.COUIContextUtil
import com.coui.appcompat.tintimageview.COUITintUtil
import com.soundrecorder.common.R
import com.soundrecorder.common.utils.PathInterpolatorHelper

class AnimateColorTextView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : AppCompatTextView(context, attrs, defStyleAttr) {
    private val selectedImageColor: Int
    private val unSelectedImageColor: Int
    private val selectedTextColor: Int
    private val unSelectedTextColor: Int
    private val imageColorAnimator: ValueAnimator
    private val textColorAnimator: ValueAnimator
    private var animateSelected: Boolean = false

    init {
        val ta = context.obtainStyledAttributes(attrs, R.styleable.AnimateColorTextView)
        val defaultSelectedImageColor = COUIContextUtil.getAttrColor(context, com.support.appcompat.R.attr.couiColorPrimary, 0)
        selectedImageColor = ta.getColor(R.styleable.AnimateColorTextView_selected_image_color, defaultSelectedImageColor)
        val defaultUnSelectedImageColor = ContextCompat.getColor(context, android.R.color.black)
        unSelectedImageColor = ta.getColor(R.styleable.AnimateColorTextView_unselected_image_color, defaultUnSelectedImageColor)

        selectedTextColor = ta.getColor(R.styleable.AnimateColorTextView_selected_image_color, defaultSelectedImageColor)
        val defaultUnSelectedTextColor = ContextCompat.getColor(context, android.R.color.darker_gray)
        unSelectedTextColor = ta.getColor(R.styleable.AnimateColorTextView_unselected_text_color, defaultUnSelectedTextColor)
        animateSelected = ta.getBoolean(R.styleable.AnimateColorTextView_animate_selected, false)
        ta.recycle()
        val duration = 200L
        imageColorAnimator = ValueAnimator.ofArgb(selectedImageColor, unSelectedImageColor).apply {
            this.duration = duration
            this.interpolator = PathInterpolatorHelper.couiEaseInterpolator
            this.addUpdateListener {
                val color = it.animatedValue as Int
                setImageColor(color)
            }
        }
        textColorAnimator = ValueAnimator.ofArgb(selectedTextColor, unSelectedTextColor).apply {
            this.duration = duration
            this.interpolator = PathInterpolatorHelper.couiMoveEaseInterpolator
            this.addUpdateListener {
                val color = it.animatedValue as Int
                setTextColor(color)
            }
        }
        setImageColor(if (animateSelected) selectedImageColor else unSelectedImageColor)
        setTextColor(if (animateSelected) selectedTextColor else unSelectedTextColor)
    }

    private fun setImageColor(color: Int) {
        compoundDrawables.filterNotNull().forEach { d ->
            COUITintUtil.tintDrawable(d, color)
        }
    }

    fun getAnimateSelected(): Boolean {
        return animateSelected
    }

    fun setAnimateSelected(selected: Boolean) {
        if (animateSelected == selected) return
        animateSelected = selected
        if (animateSelected) {
            imageColorAnimator.reverse()
            textColorAnimator.reverse()
        } else {
            imageColorAnimator.start()
            textColorAnimator.start()
        }
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        imageColorAnimator.cancel()
        textColorAnimator.cancel()
    }
}