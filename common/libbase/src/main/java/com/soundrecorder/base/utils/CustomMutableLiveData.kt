/*********************************************************************
 * * Copyright (C), 2022, OPlus. All rights reserved.
 * * VENDOR_EDIT
 * * File        :  -
 * * Version     : 1.0
 * * Date        : 2022/2/16
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.base.utils

import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.Observer
import java.util.*
import java.util.concurrent.atomic.AtomicInteger

open class CustomMutableLiveData<T> : MutableLiveData<T> {

    companion object {
        const val START_VERSION = -1
    }

    private val mCurrentVersion = AtomicInteger(START_VERSION + 1)

    constructor() : super()

    constructor(value: T) : super(value)

    override fun observe(owner: LifecycleOwner, observer: Observer<in T>) {
        super.observe(owner, createObserverWrapper(observer, mCurrentVersion.get()))
    }

    override fun observeForever(observer: Observer<in T>) {
        super.observeForever(createObserverWrapper(observer, mCurrentVersion.get()))
    }

    fun observeSticky(owner: LifecycleOwner, observer: Observer<in T>) {
        super.observe(owner, createObserverWrapper(observer, START_VERSION))
    }

    fun observeStickyForever(observer: Observer<in T>) {
        super.observeForever(createObserverWrapper(observer, START_VERSION))
    }

    override fun setValue(value: T) {
        mCurrentVersion.getAndIncrement()
        super.setValue(value)
    }

    fun clear() {
        super.setValue(null)
    }

    override fun removeObserver(observer: Observer<in T>) {
        if (observer.javaClass.isAssignableFrom(ObserverWrapper::class.java)) {
            super.removeObserver(observer)
        } else {
            super.removeObserver(createObserverWrapper(observer, mCurrentVersion.get()))
        }
    }

    private fun createObserverWrapper(observer: Observer<in T>, version: Int): Observer<in T> {
        DebugUtil.i("CustomMutableLiveData", "createObserverWrapper: observer is $observer, version is $version")
        return ObserverWrapper(observer, version)
    }

    inner class ObserverWrapper(val observer: Observer<in T>, val version: Int) : Observer<T> {
        override fun onChanged(t: T) {
            DebugUtil.i("CustomMutableLiveData", "onChanged: observer is $observer, version is $version, curVersion is ${mCurrentVersion.get()}")
            if (mCurrentVersion.get() > version) {
                observer.onChanged(t)
            }
        }

        override fun equals(other: Any?): Boolean {
            if (this === other) {
                return true
            }
            if (other == null || javaClass != other.javaClass) {
                return false
            }
            return (other as? CustomMutableLiveData<T>.ObserverWrapper)?.observer == observer
        }

        override fun hashCode(): Int {
            return Objects.hash(observer)
        }
    }
}