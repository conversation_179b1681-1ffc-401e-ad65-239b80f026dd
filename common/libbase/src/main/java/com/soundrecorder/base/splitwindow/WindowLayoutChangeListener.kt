package com.soundrecorder.base.splitwindow

import android.graphics.Rect
import android.view.View

abstract class WindowLayoutChangeListener : View.OnLayoutChangeListener {

    private var current: Rect = Rect()
    private var old: Rect = Rect()

    override fun onLayoutChange(
        v: View?,
        left: Int,
        top: Int,
        right: Int,
        bottom: Int,
        oldLeft: Int,
        oldTop: Int,
        oldRight: Int,
        oldBottom: Int
    ) {
        current.set(left, top, right, bottom)
        old.set(oldLeft, oldTop, oldRight, oldBottom)
        if (current == old) {
            return
        }
        onLayoutChange(v, current, old)
    }

    abstract fun onLayoutChange(v: View?, rect: Rect, oldRect: Rect)
}