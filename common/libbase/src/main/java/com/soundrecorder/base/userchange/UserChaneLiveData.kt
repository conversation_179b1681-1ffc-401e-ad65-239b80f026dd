/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: UserChaneLiveData
 Description:
 Version: 1.0
 Date: 2022/8/29
 Author: W9010241
 -----------Revision History-----------
 <author> <date> <version> <desc>
 W9010241 2022/8/29 1.0 create
 */

package com.soundrecorder.base.userchange

import android.content.Context.RECEIVER_NOT_EXPORTED
import android.content.Intent
import android.content.IntentFilter
import com.soundrecorder.base.BaseApplication.getApplication
import com.soundrecorder.base.utils.CustomMutableLiveData
import com.soundrecorder.base.utils.DebugUtil

class UserChaneLiveData : CustomMutableLiveData<Boolean>(), UserChangeReceiver.UserChangeListener {

    companion object {
        private const val TAG = "UserChaneLiveData"
    }

    private var mUserChangeReceiver: UserChangeReceiver? = null

    @Suppress("TooGenericExceptionCaught")
    private fun registerUserChangerReceiver() {
        if (mUserChangeReceiver == null) {
            mUserChangeReceiver = UserChangeReceiver()
        }
        mUserChangeReceiver?.setUserChangeListener(this)
        runCatching {
            getApplication().registerReceiver(mUserChangeReceiver, IntentFilter(Intent.ACTION_USER_BACKGROUND), RECEIVER_NOT_EXPORTED)
        }.onFailure {
            DebugUtil.d(TAG, "registerUserChangerReceiver failed: ${it.message}")
        }
    }

    @Suppress("TooGenericExceptionCaught")
    private fun unregisterUserChangerReceiver() {
        if (mUserChangeReceiver != null) {
            runCatching {
                getApplication().unregisterReceiver(mUserChangeReceiver)
            }.onFailure {
                DebugUtil.d(TAG, "unregisterUserChangerReceiver failed: ${it.message}")
            }
            mUserChangeReceiver?.setUserChangeListener(null)
            mUserChangeReceiver = null
        }
    }

    override fun doUserChange() {
        DebugUtil.d(TAG, "doUserChange")
        setValue(true)
    }

    override fun onActive() {
        unregisterUserChangerReceiver()
        registerUserChangerReceiver()
    }

    override fun onInactive() {
        unregisterUserChangerReceiver()
    }
}