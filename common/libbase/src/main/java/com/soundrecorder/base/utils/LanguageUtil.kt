/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  LanguageUtil
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/8/23
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.base.utils

import android.content.Context
import android.os.LocaleList
import com.google.gson.Gson
import com.google.gson.JsonObject
import com.google.gson.reflect.TypeToken
import com.soundrecorder.base.BaseApplication
import java.util.Locale

object LanguageUtil {

    private const val TAG = "LanguageUtil"

    private const val LANG_ZH = "zh"
    private const val LANG_ZH_TW = "zh-TW"
    private const val LANG_ZH_HK = "zh-HK"
    private const val LANG_EN = "en"
    private const val LANG_HI = "hi"
    private const val LANG_ES = "es"
    private const val LANG_ES_MX = "es-MX"
    private const val LANG_IT = "it"
    private const val LANG_ID = "id"
    private const val LANG_TH = "th"
    private const val LANG_AR = "ar"
    private const val LANG_VI = "vi"
    private const val LANG_MS = "ms"
    private const val LANG_PT_BR = "pt-BR"
    private const val LANG_RU = "ru"
    private const val LANG_FIL = "fil"
    private const val LANG_JA = "ja"
    private const val LANG_FR = "fr"
    private const val LANG_TR = "tr"
    private const val LANG_PL = "pl"

    // 配置文件路径
    private const val LANGUAGE_CONFIG_FILE = "realtime_asr_language_config.json"

    // JSON字段名
    private const val FIELD_DOMESTIC = "domestic"
    private const val FIELD_EXPORT = "export"

    @JvmStatic
    fun getCurrentLanguageFromSystem(): String {
        var language = ""
        val localeList = LocaleList.getDefault()
        if (localeList.size() > 0) {
            val locale = localeList[0]
            if (locale != null) {
                language = locale.language
            }
        }
        DebugUtil.i(TAG, "CurrentLanguage: $language")
        return language
    }

    /**
     * 简体中文
     */
    @JvmStatic
    fun isZHCN(): Boolean {
        val locale = Locale.getDefault()
        val isZH = locale.language.equals("zh", true)
        val isCN = locale.country.equals("CN", true)
        return isZH && isCN
    }

    /**
     * 繁体中文，中国台湾地区使用
     */
    @JvmStatic
    fun isZHTW(): Boolean {
        val locale = Locale.getDefault()
        val isZH = locale.language.equals("zh", true)
        val isTW = locale.country.equals("TW", true)
        return isZH && isTW
    }

    /**
     * 繁体中文，中国香港以及其它中文地区
     */
    @JvmStatic
    fun isZHHK(): Boolean {
        val locale = Locale.getDefault()
        val isZH = locale.language.equals("zh", true)
        val isHK = locale.country.equals("HK", true)
        return isZH && isHK
    }

    /**
     * 维吾尔语
     */
    @JvmStatic
    fun isUG(): Boolean = Locale.getDefault().language.equals("ug", true)

    /**
     * 藏语
     */
    @JvmStatic
    fun isBO(): Boolean = Locale.getDefault().language.equals("bo", true)

    /**
     * 获取当前系统语言
     */
    @JvmStatic
    fun getLocalLanguage(): String = Locale.getDefault().language

    /**
     * 语种映射
     * 注意，配置本地语种列表时，一定要注意配置的语种是否有对应的映射词条，语种映射词条见方法getLanguageDisplayName
     * 本地映射文件为 realtime_asr_language_config.json
     */
    @JvmStatic
    fun getAsrLangMap(context: Context, asrLanguage: List<String>): MutableMap<String, String> {
        val resultMap = mutableMapOf<String, String>()
        asrLanguage.forEach { langCode ->
            getLanguageDisplayName(context, langCode)?.let {
                resultMap[langCode] = it
            } ?: run {
                DebugUtil.w(TAG, "getLanguageDisplayName return null, langCode=$langCode")
                resultMap[langCode] = langCode
            }
        }
        return resultMap
    }

    /**
     * 根据语言代码获取显示名称，使用when选择器方式映射
     * @param context 上下文
     * @param languageCode 语言代码
     * @return 语言显示名称，如果不支持则返回null
     */
    @JvmStatic
    fun getLanguageDisplayName(context: Context, languageCode: String): String? {
        return when (languageCode) {
            LANG_ZH -> context.getString(com.soundrecorder.base.R.string.convert_record_default_simplified_chinese)
            LANG_ZH_TW -> context.getString(com.soundrecorder.base.R.string.language_traditional_chinese)
            LANG_ZH_HK -> context.getString(com.soundrecorder.base.R.string.language_traditional_chinese)
            LANG_EN -> context.getString(com.soundrecorder.base.R.string.summary_language_en)
            LANG_HI -> context.getString(com.soundrecorder.base.R.string.summary_language_hi)
            LANG_ES -> context.getString(com.soundrecorder.base.R.string.language_spanish_spain)
            LANG_ES_MX -> context.getString(com.soundrecorder.base.R.string.language_spanish_mexico)
            LANG_IT -> context.getString(com.soundrecorder.base.R.string.language_italian)
            LANG_ID -> context.getString(com.soundrecorder.base.R.string.language_bahasa_indonesia)
            LANG_TH -> context.getString(com.soundrecorder.base.R.string.language_thai)
            LANG_AR -> context.getString(com.soundrecorder.base.R.string.language_arabic)
            LANG_VI -> context.getString(com.soundrecorder.base.R.string.language_vietnamese)
            LANG_MS -> context.getString(com.soundrecorder.base.R.string.language_malay)
            LANG_PT_BR -> context.getString(com.soundrecorder.base.R.string.language_portuguese_brazil)
            LANG_RU -> context.getString(com.soundrecorder.base.R.string.language_russia)
            LANG_FIL -> context.getString(com.soundrecorder.base.R.string.language_filipino)
            LANG_JA -> context.getString(com.soundrecorder.base.R.string.language_japanese)
            LANG_FR -> context.getString(com.soundrecorder.base.R.string.language_french)
            LANG_TR -> context.getString(com.soundrecorder.base.R.string.language_turkish)
            LANG_PL -> context.getString(com.soundrecorder.base.R.string.language_polish)
            else -> null // 不支持的语言代码返回null
        }
    }

    /**
     * 获取区域支持的语言配置
     * 根据当前是内销还是外销返回对应的语言列表
     */
    @JvmStatic
    fun getRegionSupportedLanguages(isEXP: Boolean = BaseUtil.isEXP()): Set<String> {
        return runCatching {
            val inputStream = BaseApplication.getAppContext().assets.open(LANGUAGE_CONFIG_FILE)
            val jsonString = inputStream.bufferedReader().use { it.readText() }
            val jsonObject = Gson().fromJson(jsonString, JsonObject::class.java)

            // 根据是否外销选择不同的字段
            val regionField = if (isEXP) FIELD_EXPORT else FIELD_DOMESTIC
            val languagesArray = jsonObject.getAsJsonArray(regionField)

            // 将JsonArray转换为Set<String>
            val type = object : TypeToken<Set<String>>() {}.type
            Gson().fromJson<Set<String>>(languagesArray, type) ?: emptySet()
        }.getOrElse { e ->
            DebugUtil.e(TAG, "Failed to load language config: ${e.message}")
            emptySet() // 如果配置文件有问题，返回空集合
        }
    }

    @JvmStatic
    fun getAsrDefaultLanguage(): String {
        return if (BaseUtil.isEXP()) {
            LANG_EN
        } else {
            LANG_ZH
        }
    }

    /**
     * 获取默认支持的语言 如果系统语言不支持，则返回默认语言(内销中文，外销英文)
     * @param supportLanguageList 支持的语言列表
     * @return 默认支持的语言
     */
    @JvmStatic
    fun getAsrDefaultSupportLanguageWithLocal(supportLanguageList: List<String>): String {
        val localLanguage = getLocalLanguage()
        if (supportLanguageList.contains(localLanguage)) {
            DebugUtil.d(TAG, "getAsrDefaultSupportLanguageWithLocal return localLanguage=$localLanguage")
            return localLanguage
        }
        val defaultLanguage = getAsrDefaultLanguage()
        DebugUtil.d(TAG, "getAsrDefaultSupportLanguageWithLocal return defaultLanguage=$defaultLanguage")
        return defaultLanguage
    }
}