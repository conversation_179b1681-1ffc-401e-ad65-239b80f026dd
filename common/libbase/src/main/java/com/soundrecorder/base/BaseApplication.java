package com.soundrecorder.base;

import android.app.Activity;
import android.app.Application;
import android.content.Context;
import android.content.res.Configuration;
import android.text.TextUtils;
import android.view.View;

import com.soundrecorder.base.arms.AppDelegate;
import com.soundrecorder.base.utils.FeatureOption;
import com.soundrecorder.base.utils.MultiUserUtils;

import java.util.Locale;

public class BaseApplication extends Application {

    public static boolean sIsRTLanguage = false;
    public static boolean sNeedToNormalRingMode = false;
    public static boolean sIsMainSystem = true;  //true 主系统，false 系统分身
    private static BaseApplication mInstance;
    private AppDelegate mAppDelegate;

    public static void setInstance(BaseApplication mInstance) {
        BaseApplication.mInstance = mInstance;
    }

    public static BaseApplication getApplication() {
        return mInstance;
    }

    public static Context getAppContext() {
        return mInstance.getApplicationContext();
    }

    @Override
    protected void attachBaseContext(Context base) {
        super.attachBaseContext(base);
        setInstance(this);
    }

    @Override
    public void onCreate() {
        super.onCreate();
        if (!isMainProcess()) {
            return;
        }
        onCreateInit();
    }

    protected void onCreateInit() {
        FeatureOption.loadOptions(this);
        sIsRTLanguage = TextUtils.getLayoutDirectionFromLocale(Locale.getDefault()) == View.LAYOUT_DIRECTION_RTL;
        sIsMainSystem = !MultiUserUtils.isSystemClone();
        mAppDelegate = new AppDelegate(this);
        mAppDelegate.onApplicationCreate();
    }

    @Override
    public void onConfigurationChanged(Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        sIsRTLanguage = TextUtils.getLayoutDirectionFromLocale(Locale.getDefault()) == View.LAYOUT_DIRECTION_RTL;
    }

    public void onDestroyedRelease(Activity activity) {
    }

    public Boolean isMainProcess() {
        try {
            return getPackageName().equals(getProcessName());
        } catch (Exception e) {
            return false;
        }
    }
}
