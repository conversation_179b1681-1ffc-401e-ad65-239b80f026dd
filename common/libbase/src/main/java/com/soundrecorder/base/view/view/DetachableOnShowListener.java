/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/12/5
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.base.view;

import android.app.Dialog;
import android.content.DialogInterface;
import android.view.ViewTreeObserver;

public final class DetachableOnShowListener implements DialogInterface.OnShowListener {

    private DialogInterface.OnShowListener mDelegateOrNull;

    private DetachableOnShowListener(DialogInterface.OnShowListener delegate) {
        this.mDelegateOrNull = delegate;
    }

    public static DetachableOnShowListener wrap(DialogInterface.OnShowListener delegate) {
        return new DetachableOnShowListener(delegate);
    }

    public void clearOnDetach(Dialog dialog) {
        dialog.getWindow()
                .getDecorView()
                .getViewTreeObserver()
                .addOnWindowAttachListener(new ViewTreeObserver.OnWindowAttachListener() {
                    @Override
                    public void onWindowAttached() {
                    }

                    @Override
                    public void onWindowDetached() {
                        mDelegateOrNull = null;
                    }
                });
    }

    @Override
    public void onShow(DialogInterface dialogInterface) {
        if (mDelegateOrNull != null) {
            mDelegateOrNull.onShow(dialogInterface);
        }
    }
}