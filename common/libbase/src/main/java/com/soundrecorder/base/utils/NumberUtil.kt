/******************************************************************
 * Copyright (C), 2020-2021, OPPO Mobile Comm Corp., Ltd
 * VENDOR_EDIT
 * File: - NumberUtil.Kt
 * Description:
 * Version: 1.0
 * Date :  2020/8/12
 * Author: <EMAIL>
 *
 * ---------------- Revision History: ---------------------------
 * <author>        <data>        <version>        <desc>
 * Yongjiang,Lu      2020/8/12     1.0         build this module
 */
package com.soundrecorder.base.utils

import kotlin.math.abs

object NumberUtil {

    private const val FLOAT_EQUAL = 0.0000001f

    @JvmStatic
    fun getFloatString(number: Float): String {
        return if (number.toInt().toFloat() == number) {
            number.toInt().toString()
        } else {
           number.toString()
        }
    }

    @JvmStatic
    fun parseStr2Int(str: String, defaultValue: Int = 0): Int {
        return str.toIntOrNull() ?: defaultValue
    }

    @JvmStatic
    fun floatEqual(f1: Float, f2: Float, delta: Float): Boolean {
        return abs(f1 - f2) <= delta
    }

    @JvmStatic
    fun floatEqual(f1: Float, f2: Float): Boolean {
        return floatEqual(f1, f2, FLOAT_EQUAL)
    }

    @JvmStatic
    fun parseInt(num: String?, defaultValue: Int?): Int? = num?.toIntOrNull() ?: defaultValue

    @JvmStatic
    fun parseLong(num: String?, defaultValue: Long?): Long? = num?.toLongOrNull() ?: defaultValue
}