package com.recorder.cloudkit.tipstatus.dialog;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mockStatic;

import android.content.Context;
import android.content.res.Configuration;
import android.os.BatteryManager;
import android.os.Build;
import android.os.Handler;
import android.os.Message;
import android.view.View;
import android.widget.Button;
import android.widget.TextView;

import androidx.test.core.app.ApplicationProvider;
import androidx.test.ext.junit.runners.AndroidJUnit4;

import com.recorder.cloudkit.R;
import com.recorder.cloudkit.shadows.ShadowCOUIVersionUtil;
import com.recorder.cloudkit.shadows.ShadowFeatureOption;
import com.recorder.cloudkit.shadows.ShadowOS12FeatureUtil;
import com.recorder.cloudkit.shadows.ShadowOplusUsbEnvironment;
import com.recorder.cloudkit.sync.RecordSyncChecker;
import com.recorder.cloudkit.sync.bean.CloudSyncResult;
import com.recorder.cloudkit.sync.bean.constant.SyncErrorCode;
import com.recorder.cloudkit.sync.ui.SettingRecordSyncActivity;
import com.recorder.cloudkit.tipstatus.TipStatusManager;
import com.soundrecorder.base.BaseApplication;

import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.powermock.reflect.Whitebox;
import org.robolectric.Robolectric;
import org.robolectric.annotation.Config;

@RunWith(AndroidJUnit4.class)
@Config(sdk = Build.VERSION_CODES.S, shadows = {ShadowOS12FeatureUtil.class, ShadowOplusUsbEnvironment.class,
        ShadowFeatureOption.class, ShadowCOUIVersionUtil.class})
public class CloudSyncStatusDialogTest {
    private static final int MESSAGE_REFRESH_UI = 0;

    private Context mContext;
    private SettingRecordSyncActivity mActivity;
    private MockedStatic<BaseApplication> mMockBaseApplication;

    @Before
    public void setUp() {
        mActivity = Robolectric.buildActivity(SettingRecordSyncActivity.class).get();
        mContext = ApplicationProvider.getApplicationContext();
        mMockBaseApplication = mockStatic(BaseApplication.class);
        mMockBaseApplication.when(() -> BaseApplication.getAppContext()).thenReturn(mContext);
    }

    @After
    public void tearDown() {
        mActivity = null;
        mContext = null;
        if (mMockBaseApplication != null) {
            mMockBaseApplication.close();
            mMockBaseApplication = null;
        }
    }

    @Test
    public void verify_value_when_handleMessage() {
        CloudSyncStatusDialog dialog = new CloudSyncStatusDialog(mActivity, mActivity);
        Handler handler = Whitebox.getInternalState(dialog, "mHandler");
        Message message = Message.obtain();
        message.what = MESSAGE_REFRESH_UI;
        handler.handleMessage(message);
        Button button = Whitebox.getInternalState(dialog, "mButton");
        Assert.assertTrue(button.isEnabled());
    }

    @Test
    public void verify_value_when_onResume() {
        CloudSyncStatusDialog dialog = new CloudSyncStatusDialog(mActivity, mActivity);
        dialog.onResume();

        RecordSyncChecker.BatteryInfo chargingAndNotLow = new RecordSyncChecker.BatteryInfo();
        chargingAndNotLow.setChargeStatus(BatteryManager.BATTERY_STATUS_CHARGING);
        chargingAndNotLow.setBatteryLevel(RecordSyncChecker.BATTERY_CHARGING_MIN_TEMP);

        RecordSyncChecker.BatteryInfo chargingAndLow = new RecordSyncChecker.BatteryInfo();
        chargingAndLow.setChargeStatus(BatteryManager.BATTERY_STATUS_CHARGING);
        chargingAndLow.setBatteryLevel(RecordSyncChecker.BATTERY_CHARGING_MIN_TEMP - 1);

        RecordSyncChecker.BatteryInfo unChargingAndNotLow = new RecordSyncChecker.BatteryInfo();
        unChargingAndNotLow.setChargeStatus(BatteryManager.BATTERY_STATUS_NOT_CHARGING);
        unChargingAndNotLow.setBatteryLevel(RecordSyncChecker.BATTERY_UN_CHARGING_MIN_TEMP);

        RecordSyncChecker.BatteryInfo unChargingAndLow = new RecordSyncChecker.BatteryInfo();
        unChargingAndLow.setChargeStatus(BatteryManager.BATTERY_STATUS_NOT_CHARGING);
        unChargingAndLow.setBatteryLevel(RecordSyncChecker.BATTERY_UN_CHARGING_MIN_TEMP - 1);

        MockedStatic<RecordSyncChecker> mockChecker = mockStatic(RecordSyncChecker.class);
        Mockito.when(RecordSyncChecker.getBatteryInfo(any())).thenReturn(chargingAndNotLow, chargingAndLow, unChargingAndNotLow, unChargingAndLow);
        TipStatusManager.setSyncResult(SyncErrorCode.RESULT_POWER_SAVING_MODE);
        dialog.onResume();
        Assert.assertFalse(dialog.isShow());

        TipStatusManager.setSyncResult(SyncErrorCode.RESULT_POWER_SAVING_MODE);
        dialog.onResume();
        Assert.assertEquals(SyncErrorCode.RESULT_LOW_BATTERY_CHARGING, TipStatusManager.getSyncResult());

        TipStatusManager.setSyncResult(SyncErrorCode.RESULT_POWER_SAVING_MODE);
        dialog.onResume();
        Assert.assertFalse(dialog.isShow());

        TipStatusManager.setSyncResult(SyncErrorCode.RESULT_POWER_SAVING_MODE);
        dialog.onResume();
        Assert.assertEquals(SyncErrorCode.RESULT_LOW_BATTERY, TipStatusManager.getSyncResult());
        mockChecker.close();

        TipStatusManager.setSyncResult(SyncErrorCode.RESULT_LOCAL_INSUFFICIENT_SPACE);
        dialog.onResume();
        Assert.assertFalse(dialog.isShow());
    }

    @Test
    public void should_returnTrue_when_getCloudState() throws Exception {
        CloudSyncStatusDialog dialog = new CloudSyncStatusDialog(mActivity, mActivity);
        int state = Whitebox.invokeMethod(dialog, "getCloudState");
        Assert.assertEquals(TipStatusManager.getSyncResult(), state);
    }

    @Test
    public void should_returnTrue_when_getCloudSynWay() throws Exception {
        CloudSyncStatusDialog dialog = new CloudSyncStatusDialog(mActivity, mActivity);
        TipStatusManager.setMSyncResultData(CloudSyncResult.Companion.createSuccess());
        int code = Whitebox.invokeMethod(dialog, "getCloudSynWay");
        Assert.assertEquals(CloudSyncResult.ERROR_FROM_DEFAULT_CHECKER, code);
    }

    @Test
    public void should_returnTrue_when_setTextColorAndText() throws Exception {
        CloudSyncStatusDialog dialog = new CloudSyncStatusDialog(mActivity, mActivity);
        Whitebox.invokeMethod(dialog, "setTextColorAndText",1, R.string.about_record_of_settings, null);
        TextView textView = Whitebox.getInternalState(dialog, "mTextView");
        Assert.assertEquals(mContext.getString(R.string.about_record_of_settings), textView.getText().toString());
    }

    @Test
    public void should_returnTrue_when_setButtonText() throws Exception {
        CloudSyncStatusDialog dialog = new CloudSyncStatusDialog(mActivity, mActivity);
        Whitebox.invokeMethod(dialog, "setButtonText",R.string.about_record_of_settings);
        Button button = Whitebox.getInternalState(dialog, "mButton");
        Assert.assertNotEquals("", button.getText().toString());
    }

    @Test
    public void should_returnTrue_when_resetRetry() throws Exception {
        CloudSyncStatusDialog dialog = new CloudSyncStatusDialog(mActivity, mActivity);
        Whitebox.invokeMethod(dialog, "resetRetry");
        Handler handler = Whitebox.getInternalState(dialog, "mHandler");
        Assert.assertFalse(handler.hasMessages(MESSAGE_REFRESH_UI));
    }

    @Test
    public void should_returnTrue_when_businessOfState() throws Exception {
        CloudSyncStatusDialog dialog = new CloudSyncStatusDialog(mActivity, mActivity);
        Whitebox.invokeMethod(dialog, "businessOfState");
        Assert.assertNotNull(TipStatusManager.getMNotifyDialogListener());
    }

    @Test
    public void should_returnTrue_when_onClick() {
        CloudSyncStatusDialog dialog = new CloudSyncStatusDialog(mActivity, mActivity);
        View view = Mockito.mock(View.class);

        TipStatusManager.setSyncResult(SyncErrorCode.RESULT_NETWORK_NO_CONNECT);
        dialog.onClick(view);

        TipStatusManager.setSyncResult(SyncErrorCode.RESULT_POWER_SAVING_MODE);
        dialog.onClick(view);

        TipStatusManager.setSyncResult(SyncErrorCode.RESULT_LOW_BATTERY);
        dialog.onClick(view);
        Assert.assertFalse(dialog.isShow());

        TipStatusManager.setSyncResult(SyncErrorCode.RESULT_LOCAL_INSUFFICIENT_SPACE);
        dialog.onClick(view);

        TipStatusManager.setSyncResult(SyncErrorCode.RESULT_TEMPERATURE_HIGH);
        dialog.onClick(view);
        Assert.assertFalse(dialog.isShow());

        TipStatusManager.setSyncResult(SyncErrorCode.UI_STATE_QUERYING);
        dialog.onClick(view);
        Button button = Whitebox.getInternalState(dialog, "mButton");
        Assert.assertFalse(button.isEnabled());
    }

    @Test
    public void should_returnTrue_when_trigSynNow() throws Exception {
        CloudSyncStatusDialog dialog = new CloudSyncStatusDialog(mActivity, mActivity);
        TipStatusManager.setMSyncResultData(CloudSyncResult.createResult(SyncErrorCode.RESULT_SUCCESS, CloudSyncResult.ERROR_FROM_FILE_BACKUP));
        Whitebox.invokeMethod(dialog, "trigSynNow");

        TipStatusManager.setMSyncResultData(CloudSyncResult.createResult(SyncErrorCode.RESULT_SUCCESS, CloudSyncResult.ERROR_FROM_FILE_RECOVERY));
        Whitebox.invokeMethod(dialog, "trigSynNow");
        Assert.assertFalse(dialog.isShow());
    }

    @Test
    public void should_returnTrue_when_isShow() {
        CloudSyncStatusDialog dialog = new CloudSyncStatusDialog(mActivity, mActivity);
        dialog.show();
        Assert.assertTrue(dialog.isShow());
    }

    @Test
    public void should_returnTrue_when_show() {
        CloudSyncStatusDialog dialog = new CloudSyncStatusDialog(mActivity, mActivity);
        dialog.show();
        Assert.assertTrue(dialog.isShow());
    }

    @Test
    public void should_returnFalse_when_dismiss() {
        CloudSyncStatusDialog dialog = new CloudSyncStatusDialog(mActivity, mActivity);
        dialog.dismiss(true);
        Assert.assertFalse(dialog.isShow());
    }

    @Test
    public void should_returnTrue_when_updateLayoutOnConfig() {
        CloudSyncStatusDialog dialog = new CloudSyncStatusDialog(mActivity, mActivity);
        dialog.show();
        Configuration configuration = new Configuration();
        dialog.updateLayoutOnConfig(configuration);
        Assert.assertEquals(configuration, Whitebox.getInternalState(dialog.getMDialog(), "mConfiguration"));
    }

    @Test
    public void should_returnTrue_when_release() {
        CloudSyncStatusDialog dialog = new CloudSyncStatusDialog(mActivity, mActivity);
        dialog.release();
        Assert.assertNull(Whitebox.getInternalState(dialog, "mFragmentManager"));
    }

    @Test
    public void should_returnTrue_when_doDismiss() throws Exception {
        CloudSyncStatusDialog dialog = new CloudSyncStatusDialog(mActivity, mActivity);
        dialog.doDismiss();
        Assert.assertFalse(dialog.isShow());
    }
}
