package com.recorder.cloudkit.account

import android.content.Context
import android.os.Build
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.recorder.cloudkit.shadows.ShadowFeatureOption
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowFeatureOption::class])
class AccountPrefTest {
    private val USER_ID = "*********"
    private var mContext: Context? = null

    @Before
    fun setUp() {
        mContext = ApplicationProvider.getApplicationContext()
    }

    @After
    fun release() {
        mContext = null
    }

    @Test
    fun should_getRightUId_when_setAccountUId() {
        AccountPref.setAccountUId(mContext, "")
        val oldUserId = AccountPref.getAccountUId(mContext)
        assert(oldUserId == "")

        AccountPref.setAccountUId(mContext, USER_ID)

        var newUserId = AccountPref.getAccountUId(mContext)
        assert(newUserId == USER_ID)

        AccountPref.setAccountUId(mContext, null)
        newUserId = AccountPref.getAccountUId(mContext)
        assert(newUserId == "")
    }

}