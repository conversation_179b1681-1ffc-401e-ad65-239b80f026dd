package com.recorder.cloudkit.view

import android.annotation.SuppressLint
import android.content.Context
import android.util.AttributeSet
import android.widget.TextView
import androidx.preference.PreferenceViewHolder

class CustomSwitchPreference(
    context: Context?,
    attrs: AttributeSet?
) : LoadingSwitchPreference(context, attrs) {
    @SuppressLint("ResourceType")
    override fun onBindViewHolder(holder: PreferenceViewHolder) {
        super.onBindViewHolder(holder)
        val summaryView = holder.findViewById(android.R.id.summary) as TextView
        summaryView.setOnClickListener {
            holder.itemView.callOnClick()
        }
    }
}