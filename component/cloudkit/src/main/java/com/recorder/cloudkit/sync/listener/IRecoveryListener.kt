package com.recorder.cloudkit.sync.listener

import com.heytap.cloudkit.libsync.netrequest.metadata.CloudMetaDataRecord
import com.recorder.cloudkit.sync.bean.CloudSyncResult
import com.recorder.cloudkit.sync.bean.RecordTransferFile

interface IRecoveryListener {
    /**
     * 开始下载元数据
     */
    fun onStartRecovery() {}

    /**
     * 某次下载元数据成功回调
     */
    fun onDownloadMetaDataFinish(hasMoreData: <PERSON>ole<PERSON>, data: List<CloudMetaDataRecord>?) {}

    /**
     * 开始下载某个文件
     */
    fun onStartDownloadFile() {}

    /**
     * 某批次文件下载结果回调
     */
    fun onDownloadFileFinish(successFileList: List<RecordTransferFile>?, errorFileList: List<RecordTransferFile>?) {}

    /**
     * 整个恢复逻辑完成结果（成功 or 失败 ）
     * @param cloudKitError success: 整个流程正常执行完成， error：对应isMetaData哪个环节失败结束了
     */
    fun onFinish(cloudKitError: CloudSyncResult) {}
}