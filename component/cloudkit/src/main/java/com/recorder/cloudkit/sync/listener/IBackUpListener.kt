package com.recorder.cloudkit.sync.listener

import com.heytap.cloudkit.libsync.netrequest.metadata.CloudBackupResponseError
import com.heytap.cloudkit.libsync.netrequest.metadata.CloudBackupResponseRecord
import com.recorder.cloudkit.sync.bean.CloudSyncResult
import com.recorder.cloudkit.sync.bean.RecordTransferFile

interface IBackUpListener {
    /**
     * 开始上传元数据
     */
    fun onStartUploadMetaData() {}

    /**
     * 元数据上传完成
     */
    fun onUploadMetaDataFinish(successData: List<CloudBackupResponseRecord>, errorData: List<CloudBackupResponseError>) {}

    /**
     * 开始上传文件
     */
    fun onStartUploadFile() {}

    /**
     * 文件上传完成
     */
    fun onUploadFileFinish(errorFileList: List<RecordTransferFile>?) {}

    /**
     * 备份流程结束
     */
    fun onFinish(stepValue: String, cloudKitError: CloudSyncResult)
}