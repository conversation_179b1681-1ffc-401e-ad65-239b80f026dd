package com.recorder.cloudkit.account;

import android.content.Context;

import com.soundrecorder.base.utils.PrefUtil;

/**
 * 本地保存当前登录账号 uid
 * 用于应用冷启动判断是否有切换账号
 */
public class AccountPref {
    private static final String SP_KEY_ACCOUNT_ID = "user_id";
    private static final String SP_KEY_UPGRADE_ACCOUNT_TAG = "account_tag";

    public static void setAccountUId(Context context, String userId) {
        if (userId == null) {
            userId = "";
        }
        PrefUtil.putObject(context, SP_KEY_ACCOUNT_ID, userId);
    }

    public static String getAccountUId(Context context) {
        Object id = PrefUtil.getObject(context, SP_KEY_ACCOUNT_ID, "");
        return id == null ? "" : String.valueOf(id);
    }

    /**
     * 标记录音升级为CK版本已处理账号SDK问题（若应用之前没有接账号SDK，当该应用升级到接入账号SDK的版本时，有一定概率无法获取到账号登录态（受账号当前技术设计限制））
     * @param context
     */
    public static void setUpgradeAccountTag(Context context) {
        PrefUtil.putBoolean(context, SP_KEY_UPGRADE_ACCOUNT_TAG, true);
    }

    /**
     * 是否处理为升级CK版本账号SDK问题
     * @param context
     * @return true： 已处理
     */
    public static boolean getUpgradeAccountTag(Context context) {
        return PrefUtil.getBoolean(context, SP_KEY_UPGRADE_ACCOUNT_TAG, false);
    }
}
