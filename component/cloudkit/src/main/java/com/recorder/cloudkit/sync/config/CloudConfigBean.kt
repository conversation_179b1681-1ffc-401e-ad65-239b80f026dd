package com.recorder.cloudkit.sync.config

import java.io.Serializable

/**
 * {
 *"key":"record.ocloudH5",
 *"value":"{\"linkUrl\":\"https://cloud.oneplus.in\",\"type\":\"h5\",\"order\":1}",
 *"region":["IN"],
 *"brand":["ONEPLUS"],
 *"version":"1.0",
 *"description":"查看云端数据"
}*/
data class CloudConfigBean(var key: String?) : Serializable {
    companion object {
        private const val serialVersionUID = -4751297301979075379L
    }

    var sysRecordType: String? = null
    var description: String? = null
    var region: List<String>? = null
    var brand: List<String>? = null
    var version: String? = null
    var value: String? = null

    var dataBean: DataBean? = null


    override fun toString(): String {
        return "CloudConfigBean(key=$key, sysRecordType=$sysRecordType, description=$description, " +
                "region=$region, version=$version, value=$value, brand $brand)"
    }

    data class DataBean(var linkUrl: String?, var type: String?, var order: Int) : Serializable {
        override fun toString(): String {
            return "DataBean(linkUrl=$linkUrl, type=$type, order=$order)"
        }
    }
}
