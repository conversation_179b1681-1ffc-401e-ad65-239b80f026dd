<?xml version="1.0" encoding="utf-8"?>
<manifest package="com.recorder.cloudkit"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">
    <queries>
        <!-- required to query cloud app-->
        <package android:name="com.heytap.cloud" />
    </queries>
    <!--老版本SDK所需权限-->
    <uses-permission android:name="heytap.permission.cloud.ACCESS_SYNC_SERVICE" />
    <!--新老版本都需要-->
    <uses-permission android:name="heytap.permission.cloud.ACCESS_CLOUD" />
    <!--    此权限为clientId针对低版本Android系统读取IMEI所需，Q以上使用stdId无需该权限,目前SDK需要此权限是因为统计SDK，和网络请求taphttp SDK在Q版本上依赖此权限，如果业务方在Q以上想删除此权限则在Q以上版本的manifest中对该权限增加 tools:node=“remove”-->
    <uses-permission
        android:name="android.permission.READ_PHONE_STATE"
        tools:node="remove" />
    <!-- android Q 及以上可以不用要这个权限，Q下是为了适配海外有些老机器没有内置支付apk-->
    <uses-permission
        android:name="android.permission.REQUEST_INSTALL_PACKAGES"
        tools:node="remove" />

    <!--  SDK必须的权限-->
    <uses-permission android:name="android.permission.INTERNET"/>

    <!--
        业务方尽量需要保证有的权限，如果对此权限也做remove操作或者不申请该权限会导致SDK的限速功能在网络切换情况下因为不能判断网络变化而失效，
        以及SDK不能判断当前用户在发生同步行为时是WIFI还是数据网络，不利于日后排查问题，SDK内部做了容错处理不会crash或者出现其他逻辑异常，业务方需要谨慎判断如要移除或者不申请该权限。
    -->
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE"/>

    <!--Push SDK权限-->
    <uses-permission android:name="com.heytap.mcs.permission.RECIEVE_MCS_MESSAGE" />
    <uses-permission android:name="com.coloros.mcs.permission.RECIEVE_MCS_MESSAGE" />

    <!--同步开关存储AppSetting必须权限，非纯净化-->
    <uses-permission android:name="oppo.permission.OPPO_COMPONENT_SAFE" />
    <!--同步开关存储AppSetting必须权限，纯净化-->
    <uses-permission android:name="oplus.permission.OPLUS_COMPONENT_SAFE" />
    <!--旧有开关访问权限，兼容处理，必须-->
    <uses-permission android:name="heytap.permission.cloud.ACCESS_CLOUD" />

    <!--账号SDK需要权限-->
    <!-- Android T -->
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE"
        android:maxSdkVersion="32"/>
    <uses-permission
        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
        android:maxSdkVersion="29"/>
    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
    <!-- Android T -->
    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />

    <uses-sdk tools:overrideLibrary="com.oplus.sdk.addon.sdk"/>

    <application>
        <!--permission必须要使用OPPO组件权限，因为会往R上发，一加外销的单独再一加外销上面处理-->
        <activity
            android:name="com.recorder.cloudkit.sync.ui.SettingRecordSyncActivity"
            android:configChanges="locale|keyboardHidden|screenSize|smallestScreenSize|screenLayout|orientation"
            android:exported="true"
            android:hardwareAccelerated="true"
            android:launchMode="singleTop"
            android:permission="oppo.permission.OPPO_COMPONENT_SAFE"
            android:screenOrientation="behind"
            android:theme="@style/AppNoTitleTheme.PreferenceFragment"
            android:uiOptions="splitActionBarWhenNarrow"
            android:windowSoftInputMode="adjustNothing">
            <intent-filter>
                <!--云服务定义action-->
                <action android:name="oplus.intent.action.RECORD_CLOUD_SETTINGS" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>
        <meta-data
            android:name="com.recorder.cloudkit.CloudSyncConfigModule"
            android:value="ConfigModule" />

        <activity
            android:name="com.recorder.cloudkit.oldcompat.CloudPermissionActivity"
            android:configChanges="locale|orientation|keyboardHidden|screenSize|screenLayout|fontScale|density|layoutDirection|uiMode|smallestScreenSize"
            android:exported="true"
            android:hardwareAccelerated="true"
            android:label="@string/app_name_main"
            android:launchMode="singleTop"
            android:permission="oplus.permission.OPLUS_COMPONENT_SAFE"
            android:screenOrientation="behind"
            android:theme="@style/CloudPermissionActivityTheme"
            android:uiOptions="splitActionBarWhenNarrow"
            android:windowSoftInputMode="adjustResize">
            <intent-filter>
                <!--去品牌化后修改action，此处不能加老版本action，云同步是通过action调用的，若加上老action，会同时启动2个activity供用户选择-->
                <action android:name="com.oplus.soundrecorder.openCloudSwitch" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <!--保持oppo原有路径、permission及action-->
        <activity-alias
            android:name="oppo.multimedia.soundrecorder.CloudPermissionActivity"
            android:exported="true"
            android:permission="oppo.permission.OPPO_COMPONENT_SAFE"
            android:targetActivity="com.recorder.cloudkit.oldcompat.CloudPermissionActivity">
            <intent-filter>
                <action android:name="com.coloros.soundrecorder.openCloudSwitch" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity-alias>

        <service
            android:name="com.heytap.cloudkit.libsync.service.CloudSyncService"
            android:enabled="true"
            android:exported="false">
            <intent-filter>
                <action android:name="com.heytap.cloudkit.libsync.service" />

                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </service>

        <!--SDK内部用来判断用户是否切换了账号的provider，用来避免切换账号造成的private数据混淆的兜底逻辑，业务方需要根据CloudSyncService的process来配置是否跨进程，如果CloudSyncService跨进程了，那么这里的provider也必须跨进程，且android：procoss必须一致
        注意该Provider配置为必选，如果业务配置了该Provider，那么SDK会做一个兜底的账号逻辑切换后清除SDK保存的Private数据。如果业务不配置，那么SDK不会做账号切换清除数据这个逻辑。
        process 需同 CloudSyncService 一致-->
        <provider
            android:name="com.heytap.cloudkit.libcommon.provider.CloudAcrossProcDataProvider"
            android:authorities="${applicationId}.CloudAcrossProcDataProvider"
            android:enabled="true"
            android:exported="false"
            android:multiprocess="false" />


        <!-- SDK内部用来判断用户是否切换了账号的provider，用来避免切换账号造成的private数据混淆的兜底逻辑，业务方需要根据CloudSyncService的process来配置是否跨进程，如果CloudSyncService跨进程了，那么这里的provider也必须跨进程，且android：procoss必须一致，且authorities必须为“com.heytap.cloudkit.sync”
         注意该Provider配置为可选，如果业务配置了该Provider，那么SDK会做一个兜底的账号逻辑切换后清除SDK保存的Private数据。如果业务不配置，那么SDK不会做账号切换清除数据这个逻辑。-->
        <provider
            android:name="com.heytap.usercenter.accountsdk.utils.UCAccountSDKInitProvider"
            android:authorities="${applicationId}.UCAccountSDKInitProvider"
            android:enabled="true"
            android:exported="false"
            android:multiprocess="true" />

        <!--如果应用需要解析和处理Push消息（如透传消息），则继承PushService来处理，并在此申明如果不需要处理Push消息，则直接申明PsuhService即可 -->
        <service
            android:name="com.recorder.cloudkit.push.CloudPushService"
            android:exported="true"
            android:permission="com.heytap.mcs.permission.SEND_PUSH_MESSAGE">
            <intent-filter>
                <action android:name="com.heytap.mcs.action.RECEIVE_MCS_MESSAGE" />
                <action android:name="com.heytap.msp.push.RECEIVE_MCS_MESSAGE" />
            </intent-filter>
        </service>

        <!--云同步重试receiver-->
        <receiver
            android:name="com.recorder.cloudkit.SyncRetryReceiver"
            android:exported="false">

            <intent-filter>
                <action android:name="com.multimedia.newsoundrecorder.action.RETRY_SYNC" />
            </intent-filter>
            <intent-filter>
                <action android:name="com.multimedia.newsoundrecorder.action.CLEAR_FAILED_ACTION" />
            </intent-filter>

        </receiver>
        <!--兼容Q版本以下，需增加声明以下service-->
        <!--        <service
                    android:name=".push.CloudPushServiceBelowQ"
                    android:permission="com.coloros.mcs.permission.SEND_MCS_MESSAGE"
                    android:exported="true">
                    <intent-filter>
                        <action android:name="com.coloros.mcs.action.RECEIVE_MCS_MESSAGE"/>
                    </intent-filter>
                </service>-->
    </application>

</manifest>