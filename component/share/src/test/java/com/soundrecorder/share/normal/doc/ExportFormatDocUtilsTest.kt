/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/12/5
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.share.normal.doc

import android.app.Activity
import android.net.Uri
import android.os.Build
import androidx.appcompat.app.AlertDialog
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.common.databean.ConvertContentItem
import com.soundrecorder.common.permission.PermissionProxyActivity
import com.soundrecorder.common.share.ShareTextContent
import org.junit.After
import org.junit.Assert.assertEquals
import org.junit.Assert.assertNotNull
import org.junit.Assert.assertNull
import org.junit.Assert.assertTrue
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mockito.mock
import org.robolectric.Robolectric
import org.robolectric.RuntimeEnvironment
import org.robolectric.Shadows.shadowOf
import org.robolectric.annotation.Config
import org.robolectric.shadows.ShadowDialog

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S])
class ExportFormatDocUtilsTest {

    private val EXPORT_DOC_WPS_CLASS_NAME = "cn.wps.moffice.documentmanager.PreStartActivity2"

    private var mContext: PermissionProxyActivity? = null

    @Before
    fun setUp() {
        mContext = Robolectric.buildActivity(PermissionProxyActivity::class.java).get()
    }

    @After
    fun tearDown() {
        mContext = null
    }

    @Test
    fun should_create_file_when_convertToOoxmlFileByVadList() {
        val convertList = ArrayList<ConvertContentItem>()
        val roleNameHashMap = LinkedHashMap<String, String>()
        var result = ExportFormatDocUtils.convertToOoxmlFileByVadList(
            "",
            "timeString",
            convertList,
            roleNameHashMap,
            true
        )
        assertNull(result)
        result = ExportFormatDocUtils.convertToOoxmlFileByVadList(
            "displayName",
            "timeString",
            convertList,
            roleNameHashMap,
            true
        )
        assertNotNull(result)
    }

    @Test
    fun should_start_activity_when_jumpToWpsExport() {
        assertNotNull(mContext)
        ExportFormatDocUtils.jumpToWpsExport(
            mContext!!,
            true,
            hasSpeakerName = true,
            fileUri = null
        )
        var nextStartedActivity = shadowOf(RuntimeEnvironment.application).nextStartedActivity
        assertNull(nextStartedActivity)
        val fileUri = mock(Uri::class.java)
        ExportFormatDocUtils.jumpToWpsExport(
            mContext!!,
            true,
            hasSpeakerName = true,
            fileUri = fileUri
        )
        nextStartedActivity = shadowOf(RuntimeEnvironment.application).nextStartedActivity
        assertNull(nextStartedActivity)
        ExportFormatDocUtils.jumpToWpsExport(
            mContext!!,
            true,
            hasSpeakerName = true,
            fileUri = fileUri
        )
        nextStartedActivity = shadowOf(RuntimeEnvironment.application).nextStartedActivity
        assertNotNull(nextStartedActivity)
        assertEquals(EXPORT_DOC_WPS_CLASS_NAME, nextStartedActivity.component?.className)
    }
}