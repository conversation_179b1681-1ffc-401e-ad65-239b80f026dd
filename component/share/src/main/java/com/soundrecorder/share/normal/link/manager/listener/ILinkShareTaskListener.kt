/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: - ILinkShareTaskListener.kt
 ** Description: ILinkShareTaskListener.
 ** Version: 1.0
 ** Date : 2025/3/10
 ** Author: zhangmeng
 **
 ** ---------------------Revision History: ---------------------
 ** <author> <data> <version > <desc>
 ** zhangmeng    2025/3/10    1.0    create
 ****************************************************************/
package com.soundrecorder.share.normal.link.manager.listener

interface ILinkShareTaskListener {
    fun onLinkShareTask(audioFilePath: String?, progress: Int)
    fun onLinkShareTaskComplete(audioFilePath: String?, link: String?)
    fun onLinkShareTaskError(audioFilePath: String?, error: Int)
}