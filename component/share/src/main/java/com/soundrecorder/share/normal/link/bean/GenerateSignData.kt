/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: - GenerateSignData.kt
 ** Description: GenerateSignData.
 ** Version: 1.0
 ** Date : 2025/3/21
 ** Author: zhangmeng
 **
 ** ---------------------Revision History: ---------------------
 ** <author> <data> <version > <desc>
 ** zhangmeng    2025/3/21    1.0    create
 ****************************************************************/
package com.soundrecorder.share.normal.link.bean

data class GenerateSignData(
    val requestBody: String?,
    val timestamp: String?,
    val appKey: String?,
    val secret: String?,
    val nonce: String?,
    val requestPath: String?,
    val requestParams: Map<String, String?>?
)