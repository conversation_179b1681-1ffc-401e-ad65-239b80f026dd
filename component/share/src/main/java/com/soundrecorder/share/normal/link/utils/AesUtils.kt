/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: - AesUtils.kt
 ** Description: AesUtils.
 ** Version: 1.0
 ** Date : 2025/3/10
 ** Author: zhangmeng
 **
 ** ---------------------Revision History: ---------------------
 ** <author> <data> <version > <desc>
 ** zhangmeng    2025/3/10    1.0    create
 ****************************************************************/
package com.soundrecorder.share.normal.link.utils

import java.nio.charset.StandardCharsets
import java.security.SecureRandom
import java.util.Base64
import javax.crypto.Cipher
import javax.crypto.spec.IvParameterSpec
import javax.crypto.spec.SecretKeySpec

object AesUtils {

    const val AES_KEY_LENGTH  = 32
    const val IV_LENGTH = 16
    const val NONCE_LENGTH = 6
    private const val KEY_ALGORITHM = "AES"
    private const val ALGORITHM = "AES/CBC/PKCS5Padding"
    private const val CHARACTERS = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789"

    @JvmStatic
    fun encrypt(data: String, aesKey: String, iv: String): String {
        val ivSpec = IvParameterSpec(iv.toByteArray(StandardCharsets.UTF_8))
        val skeySpec = SecretKeySpec(aesKey.toByteArray(StandardCharsets.UTF_8), KEY_ALGORITHM)
        val cipher = Cipher.getInstance(ALGORITHM)
        cipher.init(Cipher.ENCRYPT_MODE, skeySpec, ivSpec)
        val encrypted = cipher.doFinal(data.toByteArray(StandardCharsets.UTF_8))
        return Base64.getEncoder().encodeToString(encrypted)
    }

    @JvmStatic
    fun decrypt(base64EncryptedData: String, aesKey: String, iv: String): String {
        val ivSpec = IvParameterSpec(iv.toByteArray(StandardCharsets.UTF_8))
        val skeySpec = SecretKeySpec(aesKey.toByteArray(StandardCharsets.UTF_8), KEY_ALGORITHM)
        val cipher = Cipher.getInstance(ALGORITHM)
        cipher.init(Cipher.DECRYPT_MODE, skeySpec, ivSpec)
        val decodedBytes = Base64.getDecoder().decode(base64EncryptedData)
        val decrypted = cipher.doFinal(decodedBytes)
        return String(decrypted, StandardCharsets.UTF_8)
    }

    @JvmStatic
    fun randomToken(length: Int): String {
        val random = SecureRandom()
        val token = StringBuilder(length)
        repeat(length) {
            val randomIndex = random.nextInt(CHARACTERS.length)
            token.append(CHARACTERS[randomIndex])
        }
        return token.toString()
    }
}