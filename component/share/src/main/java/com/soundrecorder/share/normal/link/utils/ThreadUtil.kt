/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: - ThreadUtil.kt
 ** Description: ThreadUtil.
 ** Version: 1.0
 ** Date : 2025/3/13
 ** Author: zhangmeng
 **
 ** ---------------------Revision History: ---------------------
 ** <author> <data> <version > <desc>
 ** zhangmeng    2025/3/13    1.0    create
 ****************************************************************/
package com.soundrecorder.share.normal.link.utils

import android.os.Handler
import android.os.HandlerThread
import android.os.Process

object ThreadUtil {
    private const val THREAD_NAME = "SoundRecorder_HandlerThread"
    private val mAsyncHandlerThread by lazy {
        HandlerThread(THREAD_NAME, Process.THREAD_PRIORITY_BACKGROUND)
            .apply {
                start()
            }
    }

    val mIOHandler by lazy {
        Handler(mAsyncHandlerThread.looper)
    }
}