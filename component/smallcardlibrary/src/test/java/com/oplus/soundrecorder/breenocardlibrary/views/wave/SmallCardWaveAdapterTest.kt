/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: WaveAdapterTest
 * Description:
 * Version: 1.0
 * Date: 2023/4/18
 * Author: ********(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * ******** 2023/4/18 1.0 create
 */

package com.oplus.soundrecorder.breenocardlibrary.views.wave

import android.os.Build
import android.widget.FrameLayout
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import org.junit.Assert
import org.junit.Ignore
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mockito
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S])
@Ignore
class SmallCardWaveAdapterTest {
    @Test
    fun should_correct_when_getItemViewType() {
        val waveRecyclerView =
            Mockito.spy(SmallCardWaveRecyclerView(ApplicationProvider.getApplicationContext()))
        val waveAdapter = SmallCardWaveAdapter(waveRecyclerView)
        Assert.assertTrue(waveAdapter.getItemViewType(0) == SmallCardWaveAdapter.VIEW_TYPE_HEADER)
        Assert.assertTrue(waveAdapter.getItemViewType(Int.MAX_VALUE - 1) == SmallCardWaveAdapter.VIEW_TYPE_FOOTER)
        Assert.assertTrue(waveAdapter.getItemViewType(1) == SmallCardWaveAdapter.VIEW_TYPE_WAVE)
    }

    @Test
    fun should_correct_when_onCreateViewHolder() {
        val waveRecyclerView =
            Mockito.spy(SmallCardWaveRecyclerView(ApplicationProvider.getApplicationContext()))
        val waveAdapter = SmallCardWaveAdapter(waveRecyclerView)
        waveAdapter.onCreateViewHolder(
            FrameLayout(ApplicationProvider.getApplicationContext()),
            SmallCardWaveAdapter.VIEW_TYPE_HEADER
        )
        waveAdapter.onCreateViewHolder(
            FrameLayout(ApplicationProvider.getApplicationContext()),
            SmallCardWaveAdapter.VIEW_TYPE_WAVE
        )
        waveAdapter.onCreateViewHolder(
            FrameLayout(ApplicationProvider.getApplicationContext()),
            SmallCardWaveAdapter.VIEW_TYPE_FOOTER
        )
    }

    @Test
    fun should_correct_when_onBindViewHolder() {
        val waveRecyclerView =
            Mockito.spy(SmallCardWaveRecyclerView(ApplicationProvider.getApplicationContext()))
        val waveAdapter = SmallCardWaveAdapter(waveRecyclerView)
        waveAdapter.onBindViewHolder(
            waveAdapter.onCreateViewHolder(
                FrameLayout(ApplicationProvider.getApplicationContext()), SmallCardWaveAdapter.VIEW_TYPE_WAVE
            ), 1
        )
    }
}