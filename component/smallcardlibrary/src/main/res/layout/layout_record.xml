<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/root_view"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/breeno_card_background"
    tools:ignore="SpUsage,HardcodedText,UnusedAttribute">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/recordWaveView"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <FrameLayout
            android:id="@+id/fl_wave_container"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginBottom="10dp"
            app:layout_constraintHeight_percent="@dimen/small_card_wave_view_height_percent"
            android:layout_marginHorizontal="@dimen/small_card_content_margin_horizontal"
            android:layoutDirection="ltr"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/recordTime"
            app:layout_constraintBottom_toTopOf="@id/ivStartOrPause">

            <com.oplus.soundrecorder.breenocardlibrary.views.wave.SmallCardWaveRecyclerView
                android:id="@+id/rv_wave"
                android:layout_width="match_parent"
                android:layout_height="match_parent" />
        </FrameLayout>

        <include layout="@layout/include_wave_view_version_two"/>


        <TextView
            android:id="@+id/recordTime"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            style="@style/SmallCard_Record_Time"
            android:importantForAccessibility="no"
            android:text="00:00"
            android:textFontWeight="600"
            android:layout_marginTop="9dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
        <View
            android:id="@+id/ivStartView"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintTop_toTopOf="@+id/ivStartOrPause"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"/>


        <com.oplus.soundrecorder.breenocardlibrary.views.button.AppCardButton
            android:id="@+id/ivAddMark"
            style="@style/Button_Style"
            app:layout_constraintWidth_percent="@dimen/small_card_mark_button_width_percent"
            app:layout_constraintDimensionRatio="1:1"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginStart="@dimen/small_card_content_margin_horizontal"
            android:src="@drawable/breeno_card_record_mark"
            android:alpha="0"
            app:layout_constraintBottom_toBottomOf="@id/ivStartOrPause"
            app:layout_constraintEnd_toEndOf="@id/ivStartOrPause"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintTop_toTopOf="@id/ivStartOrPause" />

        <com.oplus.soundrecorder.breenocardlibrary.views.button.AppCardButton
            android:id="@+id/ivSaveFile"
            style="@style/Button_Style"
            app:layout_constraintWidth_percent="@dimen/small_card_mark_button_width_percent"
            app:layout_constraintDimensionRatio="1:1"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginEnd="@dimen/small_card_content_margin_horizontal"
            android:src="@drawable/breeno_card_record_save"
            app:layout_constraintBottom_toBottomOf="@id/ivStartOrPause"
            app:layout_constraintStart_toStartOf="@id/ivStartOrPause"
            app:layout_constraintTop_toTopOf="@id/ivStartOrPause" />

        <com.oplus.soundrecorder.breenocardlibrary.views.ClickScaleImageView
            android:id="@+id/ivStartOrPause"
            app:layout_constraintWidth_percent="@dimen/small_card_record_button_width_percent"
            app:layout_constraintDimensionRatio="1:1"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginBottom="@dimen/small_card_content_margin_horizontal"
            android:src="@drawable/breeno_card_record_init"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <include layout="@layout/include_save_success_view"/>

    <include layout="@layout/include_loading_view"/>

</androidx.constraintlayout.widget.ConstraintLayout>