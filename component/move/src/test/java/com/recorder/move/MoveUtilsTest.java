package com.recorder.move;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;

import android.os.Build;

import androidx.test.ext.junit.runners.AndroidJUnit4;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.reflect.Whitebox;

import org.robolectric.annotation.Config;

import java.io.File;

import com.recorder.move.shadows.ShadowBaseUtilBelowQ;
import com.recorder.move.shadows.ShadowBaseUtils;
import com.recorder.move.shadows.ShadowFeatureOption;

@RunWith(AndroidJUnit4.class)
@Config(sdk = Build.VERSION_CODES.S, shadows = {ShadowBaseUtils.class, ShadowFeatureOption.class})
public class MoveUtilsTest {
    private static final String INPUT_PATH_MP3 = "emulated/0/Music/Recordings/Standard Recordings/1.mp3";
    private static final String TEST_PATH_B_TXT = "testPath/B.txt";

    @Test
    @Config(shadows = ShadowBaseUtils.class)
    public void should_correct_getDataByData() {
        String result = MoveUtils.getDataByData(INPUT_PATH_MP3);
        assertNotNull(result);
        assertEquals(INPUT_PATH_MP3, result);
        //emulated/0/Music/Recordings/Standard Recordings/1.mp3
        result = MoveUtils.getDataByData(INPUT_PATH_MP3.replace(File.separator + "Music", ""));
        assertEquals(INPUT_PATH_MP3, result);
    }

    @Test
    @Config(shadows = ShadowBaseUtilBelowQ.class)
    public void should_correct_getDataByData_belowQ() {
        String result = MoveUtils.getDataByData(INPUT_PATH_MP3);
        assertNotNull(result);
        assertEquals(INPUT_PATH_MP3.replace(File.separator + "Music", ""), result);
        result = MoveUtils.getDataByData(TEST_PATH_B_TXT);
        assertEquals(TEST_PATH_B_TXT, result);
    }

    @Test
    public void should_size_when_getParentDirectoryFromFullPath() throws Exception {
        String str = Whitebox.invokeMethod(MoveUtils.class, "getParentDirectoryFromFullPath", "emulated\\0\\Music\\Recordings\\Standard Recordings\\123.mp3");
        assertNotNull(str);
    }
}
