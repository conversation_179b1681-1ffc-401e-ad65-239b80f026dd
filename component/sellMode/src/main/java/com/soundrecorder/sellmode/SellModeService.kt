/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: SellModeApi
 Description:
 Version: 1.0
 Date: 2022/12/13
 Author: W9013333(v-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@oppo.com)
 -----------Revision History-----------
 <author> <date> <version> <desc>
 W9013333 2022/12/13 1.0 create
 */

package com.soundrecorder.sellmode

import android.content.ContentUris
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Environment
import android.provider.MediaStore
import android.provider.MediaStore.Audio.Media.EXTERNAL_CONTENT_URI
import android.webkit.MimeTypeMap
import androidx.lifecycle.LifecycleService
import androidx.lifecycle.lifecycleScope
import com.oplus.recorderlog.log.constants.XLogRecordConstants.Companion.CALL_RECORDINGS
import com.oplus.recorderlog.log.constants.XLogRecordConstants.Companion.INTERVIEW_RECORDINGS
import com.oplus.recorderlog.log.constants.XLogRecordConstants.Companion.MEETING_RECORDINGS
import com.oplus.recorderlog.log.constants.XLogRecordConstants.Companion.RECORDINGS
import com.oplus.recorderlog.log.constants.XLogRecordConstants.Companion.STANDARD_RECORDINGS
import com.oplus.recorderlog.util.CommonFlavor
import com.oplus.recorderlog.util.GsonUtil
import com.soundrecorder.base.ext.title
import com.soundrecorder.base.utils.BaseUtil
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.FeatureOption
import com.soundrecorder.base.utils.MD5Utils
import com.soundrecorder.base.utils.MediaDataScanner
import com.soundrecorder.base.utils.OplusCompactUtil
import com.soundrecorder.base.utils.UrlEncodeUtil
import com.soundrecorder.common.constant.DatabaseConstant
import com.soundrecorder.common.constant.DatabaseConstant.PATH_CONVERT
import com.soundrecorder.common.databean.ConvertRecord
import com.soundrecorder.common.databean.ConvertStatus
import com.soundrecorder.common.databean.NoteData
import com.soundrecorder.common.db.CursorHelper
import com.soundrecorder.common.db.MediaDBUtils
import com.soundrecorder.common.db.NoteDbUtils
import com.soundrecorder.common.db.PictureMarkDbUtils
import com.soundrecorder.common.permission.PermissionUtils
import com.soundrecorder.common.utils.ConvertDbUtil
import com.soundrecorder.common.utils.ConvertDbUtil.CONVERT_COMP_STATUS_COMPLETE
import com.soundrecorder.common.utils.ConvertDbUtil.SHOW_SWITH_TRUE
import com.soundrecorder.common.utils.CoroutineUtils
import com.soundrecorder.common.utils.RecordFileChangeNotify
import com.soundrecorder.modulerouter.summary.SummaryInterface
import com.soundrecorder.modulerouter.utils.Injector
import kotlinx.coroutines.Dispatchers.IO
import kotlinx.coroutines.launch
import java.io.File
import java.util.Collections.emptyList
import java.util.Locale
import kotlin.coroutines.resume
import kotlin.coroutines.suspendCoroutine

@Suppress("TooGenericExceptionCaught")
class SellModeService : LifecycleService() {
    companion object {
        private const val TAG = "SellModeService"
        private val SELL_MODE_DATA_DIR =
            if (OplusCompactUtil.isOver11dot3()) {
                "/data/oplus/common/screensavers/${CommonFlavor.getInstance().getPackageName()}"
            } else {
                "/data/oppo/common/screensavers/${CommonFlavor.getInstance().getPackageName()}"
            }
        private val RECORDING_DIR =
            arrayOf(CALL_RECORDINGS, STANDARD_RECORDINGS, MEETING_RECORDINGS, INTERVIEW_RECORDINGS)

        /**
         * 1、录音冷启动，在BaseApplication的onCreate中初始调用
         * 2、BrowseFile有读取音频权限时调用
         * 3、在录音App进程存活时，可以收到卖场重置虚拟数据广播时调用
         */
        @JvmStatic
        internal fun Context.checkAndStartSellModeService() {
            if (!FeatureOption.hasSellMode()) {
                DebugUtil.i(TAG, "checkAndStartSellModeService hasSellMode = false")
                return
            }
            CoroutineUtils.ioToMain({
                if (!PermissionUtils.hasReadAudioPermission()) {
                    DebugUtil.i(TAG, "checkAndStartSellModeService hasReadAudioPermission = false")
                    return@ioToMain false
                }
                val count = getAllRecordingsCount()
                DebugUtil.i(TAG, "checkAndStartSellModeService getAllRecordingsCount = $count")
                if (count > 0) {
                    return@ioToMain false
                }
                return@ioToMain true
            }, {
                if (it) {
                    DebugUtil.i(TAG, "checkAndStartSellModeService startService")
                    Intent(this, SellModeService::class.java).apply {
                        setPackage(packageName)
                        startService(this)
                    }
                }
            })
        }

        /**
         * 获取所有录音Recordings总条数
         */
        @JvmStatic
        fun Context.getAllRecordingsCount(): Int {
            var allCount = 0
            try {
                val whereClause = CursorHelper.getAllRecordContainCallWhereClause(this)
                val selectionArgs = CursorHelper.getsAcceptableAudioTypes()
                contentResolver.query(
                    EXTERNAL_CONTENT_URI,
                    arrayOf(MediaStore.Audio.Media._ID),
                    whereClause,
                    selectionArgs,
                    null
                )?.use {
                    allCount = it.count
                }
            } catch (e: Exception) {
                DebugUtil.e(TAG, "getAllRecordingsCount", e, false)
            }
            return allCount
        }
    }

    private val audioToConvertMap = mutableMapOf<String, SellData>()

    // 不同国家码不同的值，比如内销就是""，德国就是"DE/"，英国就是"GB/"
    private var countryCodeDir = ""

    private val summaryApi by lazy {
        Injector.injectFactory<SummaryInterface>()
    }

    override fun onCreate() {
        super.onCreate()
        lifecycleScope.launch(IO) {
            try {
                presetDataBySellMode()
            } catch (e: Exception) {
                DebugUtil.e(TAG, "onCreate", e, false)
            } finally {
                stopSelf()
            }
        }
    }

    /**
     * 卖场模式预置数据流程
     */
    private suspend fun presetDataBySellMode() {
        suspendCoroutine {
            initCountryCodeDir()
            clearDataBySellMode()
            copeAudioAndConvertBySellMode()
            MediaDataScanner.getInstance()
                .mediaScanWithCallback(this, arrayOf(recordingsDir().toString())) { path, _ ->
                    DebugUtil.i(TAG, "mediaScanWithCallback path = $path")
                    insertDb()
                    BaseUtil.sendLocalBroadcast(
                        this,
                        Intent(RecordFileChangeNotify.FILE_UPDATE_ACTION)
                    )
                    it.resume(Unit)
                }
        }
    }

    /**
     * 创建音频和转文本及摘要的关联关系
     */
    private fun insertDb() {
        audioToConvertMap.forEach { (audio, data) ->
            val mediaId =
                MediaDBUtils.getMediaUriForAbsolutePath(audio)?.run { ContentUris.parseId(this) }
            if (mediaId == null) {
                DebugUtil.i(TAG, "insertConvertDb,mediaId = null")
                return@forEach
            }
            DebugUtil.i(TAG, "insertDb,mediaId = $mediaId")
            insertConvertDb(mediaId, audio, data.convertFileAbsPath)
            insertSummaryDb(mediaId, audio, data.noteData)
        }
    }

    /**
     * 创建音频和转文本关联关系
     */
    private fun insertConvertDb(mediaId: Long, audioPath: String, convertAbsPath: String?) {
        val convertPath = convertAbsPath ?: return
        val convertRecord = ConvertRecord(mediaId).apply {
            mediaPath = audioPath
            convertTextfilePath = convertPath
            convertStatus = ConvertStatus.CONVERT_STATUS_QUERY_SUC
            uploadRecordList = emptyList()
            completeStatus = CONVERT_COMP_STATUS_COMPLETE
            canShowSpeakerRole = SHOW_SWITH_TRUE
        }
        ConvertDbUtil.insertCheckRecordId(convertRecord)
    }

    /**
     * 拷贝虚拟音频和虚拟转文本数据，并创建转文本数据与录音数据关联
     */
    private fun copeAudioAndConvertBySellMode() {
        try {
            File(SELL_MODE_DATA_DIR, "$countryCodeDir$RECORDINGS").listFiles()?.forEach {
                if (it.name.replace("_", " ") in RECORDING_DIR) {
                    DebugUtil.i(TAG, "copeAudioAndConvertBySellMode: ${it.absolutePath}")
                    var sellData: SellData
                    it.listFiles()?.forEach { audioFile ->
                        if (audioFile.mimeType() in CursorHelper.getsAcceptableAudioTypes()) {
                            val newAudioFile = copeAudioBySellMode(audioFile) ?: return
                            val convertData = copeConvertBySellMode(audioFile)
                            sellData = SellData(convertData?.absolutePath, parseSummaryBySellMode(audioFile))
                            audioToConvertMap[newAudioFile.absolutePath] = sellData
                        }
                    }
                }
            }
        } catch (e: Exception) {
            DebugUtil.e(TAG, "copeAudioAndConvertBySellMode", e, false)
        }
    }

    /**
     * 预置卖场模式虚拟数据时，先删除录音数据
     */
    private fun clearDataBySellMode() {
        try {
            contentResolver.delete(DatabaseConstant.RecordUri.RECORD_CONTENT_URI, null, null)
            contentResolver.delete(PictureMarkDbUtils.getPictureMarkUri(), null, null)
            contentResolver.delete(DatabaseConstant.ConvertUri.CONVERT_CONTENT_URI, null, null)
            filesDir.newDir(PATH_CONVERT).delete()
            NoteDbUtils.clearAllNoteData()
        } catch (e: Exception) {
            DebugUtil.e(TAG, "clearDataBySellMode", e, false)
        }
    }

    /**
     * 拷贝音频文件至录音音频公共目录
     * /storage/emulated/0/Music/Recordings/Interview Recordings
     * /storage/emulated/0/Music/Recordings/Meeting Recordings
     * /storage/emulated/0/Music/Recordings/Standard Recordings
     * /storage/emulated/0/Music/Recordings/Call Recordings
     */
    private fun copeAudioBySellMode(audioFile: File): File? {
        try {
            val relativePath = audioFile.parentFile?.name?.replace("_", " ")
            if (relativePath.isNullOrEmpty()) {
                DebugUtil.i(TAG, "$audioFile,copeAudio relativePath.isNullOrEmpty()")
                return null
            }
            val audioName = UrlEncodeUtil.decode(audioFile.name)
            DebugUtil.i(TAG, "relativePath = $relativePath, audioName = $audioName")
            val newAudioFile = recordingsDir()?.newDir(relativePath)?.newFile(audioName)
            if (newAudioFile == null) {
                DebugUtil.i(TAG, "$audioFile,copeAudio newAudioFile == null")
                return null
            }
            audioFile.inputStream().use { input ->
                newAudioFile.outputStream().use { output ->
                    input.copyTo(output, DEFAULT_BUFFER_SIZE)
                }
            }
            return newAudioFile
        } catch (e: Exception) {
            DebugUtil.e(TAG, "copeAudioBySellMode", e, false)
            return null
        }
    }

    /**
     * 拷贝转文本文件至filesDir的convert目录
     */
    private fun copeConvertBySellMode(audioFile: File): File? {
        if (BaseUtil.isLightOS()) {
            DebugUtil.w(TAG, "lightOs not convert text so return null")
            return null
        }
        if (BaseUtil.isEXP()) {
            DebugUtil.w(TAG, "exp not convert text so return null")
            return null
        }
        try {
            val convertFile = audioFile.parentFile?.newFile("${audioFile.name.title()}.txt")
            if (convertFile == null || !convertFile.exists()) {
                DebugUtil.i(TAG, "convertFile is null or not exists")
                return null
            }
            val newConvertFilePath = "${audioFile.parentFile?.path}${File.separator}${UrlEncodeUtil.decode(audioFile.name).title()}.txt"
            DebugUtil.i(TAG, "copeConvertBySellMode convertFilePath = $newConvertFilePath")
            val newConvertFileName = "${MD5Utils.calcMd5(newConvertFilePath)}.txt"
            val newConvertFile = filesDir.newDir(PATH_CONVERT).newFile(newConvertFileName)
            convertFile.inputStream().use { input ->
                newConvertFile.outputStream().use { output ->
                    input.copyTo(output, DEFAULT_BUFFER_SIZE)
                }
            }
            return newConvertFile
        } catch (e: Exception) {
            DebugUtil.e(TAG, "copeConvertBySellMode", e, false)
            return null
        }
    }

    /**
     * 创建文件目录
     */
    private fun File.newDir(name: String): File {
        val dir = File(this, name)
        if (dir.exists()) {
            if (dir.isFile) {
                dir.delete()
                dir.mkdirs()
            }
        } else {
            dir.mkdirs()
        }
        return dir
    }

    /**
     * 创建文件
     */
    private fun File.newFile(name: String): File {
        val file = File(this, name)
        if (exists()) {
            file.delete()
        }
        file.createNewFile()
        return file
    }

    /**
     * 录音Recordings目录
     */
    private fun recordingsDir(): File? {
        return BaseUtil.getPhoneStorageFile(this)?.newDir(Environment.DIRECTORY_MUSIC)
            ?.newDir(RECORDINGS)
    }

    /**
     * 获取File对应的mimeType
     */
    private fun File.mimeType(): String {
        DebugUtil.i(TAG, "absolutePath = $absolutePath")
        return try {
            val extension = MimeTypeMap.getFileExtensionFromUrl(Uri.fromFile(this).toString())
            val mimeType = MimeTypeMap.getSingleton().getMimeTypeFromExtension(extension) ?: ""
            DebugUtil.i(TAG, "extension = $extension, format = $mimeType")
            mimeType
        } catch (e: Exception) {
            DebugUtil.e(TAG, "mimeType", e, false)
            ""
        }
    }

    /***
     * 获取国家码
     */
    private fun getCountryCode(): String {
        return Locale.getDefault().country
    }

    /**
     * 通过内外销和国家码获取资源路径目录
     */
    private fun initCountryCodeDir() {
        val countryCode = getCountryCode()
        // 根据国家码来核查是否有该国家的资源文件夹
        File(SELL_MODE_DATA_DIR).listFiles()?.find {
            it.name.equals(countryCode, true)
        }?.let {
            countryCodeDir = "${it.name}/"
        }
        DebugUtil.e(TAG, "checkResourceDir countryCode=$countryCode; countryCodeDir=$countryCodeDir")
    }

    /***
     * 将json配置文件转换为NoteData
     */
    private fun parseSummaryBySellMode(audioFile: File): NoteData? {
        if (BaseUtil.isLightOS()) {
            DebugUtil.w(TAG, "lightOs not support summary so return null")
            return null
        }
        if (!FeatureOption.supportRecordSummaryFunction()) {
            DebugUtil.w(TAG, "parseSummaryBySellMode,not support record summary function")
            return null
        }
        if (summaryApi?.getSupportRecordSummaryValue()?.value != true) {
            DebugUtil.w(TAG, "parseSummaryBySellMode, not support record summary")
            return null
        }
        kotlin.runCatching {
            val path = audioFile.parentFile?.absolutePath.plus("/${audioFile.name.title()}.json")
            DebugUtil.d(TAG, "parseSummaryBySellMode path=$path")
            val summaryFile = File(path)
            if (!summaryFile.exists()) {
                DebugUtil.i(TAG, "parseSummaryBySellMode summaryFile is null or not exists")
                return null
            }
            val jsonString = summaryFile.readText()
            DebugUtil.d(TAG, "parseSummaryBySellMode $jsonString")
            return GsonUtil.fromJson(jsonString, NoteData::class.java).apply {
                DebugUtil.d(TAG, "parseSummaryBySellMode $this")
            }
        }.onFailure {
            DebugUtil.e(TAG, "parseSummaryBySellMode e=$it")
        }
        return null
    }

    /***
     * 将NoteData插入到摘要数据库中
     */
    private fun insertSummaryDb(mediaId: Long, audioPath: String, noteData: NoteData?) {
        noteData?.let {
            it.mediaId = mediaId.toString()
            it.mediaPath = audioPath
            DebugUtil.i(TAG, "insertSummaryDb noteData=$it")
            NoteDbUtils.updateOrInsertNote(it)
        }
    }
}