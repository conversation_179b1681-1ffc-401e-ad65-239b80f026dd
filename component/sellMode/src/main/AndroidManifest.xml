<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android">

    <application>
        <receiver
            android:name="com.soundrecorder.sellmode.SellModeReceiver"
            android:exported="true"
            android:permission="oppo.permission.OPPO_COMPONENT_SAFE">
            <intent-filter>
                <action android:name="com.oplus.daydreamvideo.REQUEST_RESTORE_DATA" />
            </intent-filter>
        </receiver>
        <service android:name="com.soundrecorder.sellmode.SellModeService" />
    </application>
</manifest>