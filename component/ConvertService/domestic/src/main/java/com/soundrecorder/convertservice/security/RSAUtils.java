/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        : RSAUtils
 * * Description : RSA工具类
 * * Version     : 1.0
 * * Date        : 2022/6/23
 * * Author      : W9013204
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.convertservice.security;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.security.Key;
import java.security.KeyFactory;
import java.security.PublicKey;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.Base64;

import javax.crypto.Cipher;

import com.soundrecorder.common.constant.Constants;

public class RSAUtils {

    /**
     * 获取公钥的key
     */
    private static final String PUBLIC_KEY = "RSAPublicKey";

    /**
     * 获取私钥的key
     */
    private static final String PRIVATE_KEY = "RSAPrivateKey";

    /**
     * 加密算法RSA
     */
    private static final String RSA_KEY_ALGORITHM = "RSA";

    /**
     * transformation，用于Cipher实例化
     */
    private static final String RSA_NONE_OAEPPADDING_TRANSFORMATION = "RSA/NONE/OAEPPadding";

    /**
     * RSA公钥私钥长度
     */
    private static final int RSA_KEY_SIZE = 2048;

    private static final int NUM_8 = 8;
    private static final int NUM_11 = 11;

    /**
     * 公钥加密
     *
     * @param content
     * @param base64PublicKey
     * @return
     * <AUTHOR>
     * @since 2019-12-05 14:33:21
     */
    public static String publicEncrypt(String content, String base64PublicKey) throws Exception {
        //1.先将公钥从base64中解析出来；2.公钥以X509格式存储的，将其解密出来
        X509EncodedKeySpec keySpec = new X509EncodedKeySpec(Base64.getDecoder().decode(base64PublicKey.getBytes(Constants.UTF_8)));
        KeyFactory keyFactory = KeyFactory.getInstance(RSA_KEY_ALGORITHM);
        PublicKey publicKey = keyFactory.generatePublic(keySpec);
        //对数据加密
        Cipher cipher = Cipher.getInstance(RSA_NONE_OAEPPADDING_TRANSFORMATION);
        cipher.init(Cipher.ENCRYPT_MODE, publicKey);
        return Base64.getEncoder().encodeToString(rsaSplitCodec(cipher, Cipher.ENCRYPT_MODE, content));
    }

    /**
     * 私钥解密
     *
     * @param content
     * @param base64PrivateKey
     * @return
     * <AUTHOR>
     * @since 2019-12-05 14:30:59
     */
    public static String privateDecrypt(String content, String base64PrivateKey) throws Exception {
        PKCS8EncodedKeySpec pkcs8EncodedKeySpec = new PKCS8EncodedKeySpec(Base64.getDecoder().decode(base64PrivateKey));
        KeyFactory keyFactory = KeyFactory.getInstance(RSA_KEY_ALGORITHM);
        Key privateKey = keyFactory.generatePrivate(pkcs8EncodedKeySpec);
        Cipher cipher = Cipher.getInstance(RSA_NONE_OAEPPADDING_TRANSFORMATION);
        cipher.init(Cipher.DECRYPT_MODE, privateKey);
        return new String(rsaSplitCodec(cipher, Cipher.DECRYPT_MODE, content), StandardCharsets.UTF_8);
    }

    /**
     * 处理String内容
     *
     * @param cipher
     * @param cipherMode Cipher.ENCRPT_MODE/Cipher.DECRYPT_MODE
     * @param content    需要处理的String内容
     * @return
     * <AUTHOR>
     * @since 2019-12-05 15:42:26
     */
    private static byte[] rsaSplitCodec(Cipher cipher, int cipherMode, String content) throws Exception {
        int maxBlock = 0;
        byte[] contentBytes = null;
        //之所以有长度限制是因为RSA通常与AES配合使用，不会去加解密这么长的内容
        if (cipherMode == Cipher.DECRYPT_MODE) {
            maxBlock = RSA_KEY_SIZE / NUM_8;
            contentBytes = Base64.getDecoder().decode(content);
        } else {
            //PKCS1Padding占用11位
            maxBlock = RSA_KEY_SIZE / NUM_8 - NUM_11;
            contentBytes = content.getBytes(Constants.UTF_8);
        }

        ByteArrayOutputStream writer = new ByteArrayOutputStream();
        InputStream ins = new ByteArrayInputStream(contentBytes);
        byte[] buf = new byte[maxBlock];
        int index = -1;
        while ((index = ins.read(buf)) != -1) {
            byte[] block = null;
            if (buf.length == index) {
                block = buf;
            } else {
                block = new byte[index];
                System.arraycopy(buf, 0, block, 0, index);
            }
            writer.write(cipher.doFinal(block));
        }
        return writer.toByteArray();
    }
}

