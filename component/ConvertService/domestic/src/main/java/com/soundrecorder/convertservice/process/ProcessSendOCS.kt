/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  ProcessSendOCS
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/6/23
 * * Author      : W9013204
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.convertservice.process

import android.accounts.NetworkErrorException
import android.net.Uri
import com.soundrecorder.convertservice.bean.Constant
import com.soundrecorder.convertservice.convert.IConvertCallback
import com.soundrecorder.convertservice.okhttphelper.*
import com.google.gson.JsonParseException
import com.soundrecorder.common.databean.ConvertRecord
import com.soundrecorder.common.utils.ConvertDbUtil.CONVERT_COMP_STATUS_COMPLETE
import com.soundrecorder.common.utils.ConvertDbUtil.VERSION_2_SPEAKER
import com.soundrecorder.convertservice.convert.ConvertServiceUtils
import com.soundrecorder.common.databean.ConvertStatus
import com.soundrecorder.convertservice.security.EncryptException
import com.soundrecorder.convertservice.convert.ConvertCheckUtils
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.FileUtils
import com.soundrecorder.common.databean.UploadRecord
import com.soundrecorder.common.db.MediaDBUtils
import com.soundrecorder.common.db.UploadDbUtil
import com.soundrecorder.common.utils.ConvertDbUtil
import com.soundrecorder.convertservice.bean.BaseResponse
import com.soundrecorder.convertservice.bean.BeanGetPresignedURLs
import com.soundrecorder.convertservice.bean.RequestGetUploadResult
import java.lang.Long.min
import java.util.*
import kotlin.collections.ArrayList
import kotlin.math.ceil

class ProcessSendOCS(
    override val nextProcess: IBackgroundProcess?,
    override var convertCallback: IConvertCallback?,
    private val convertAiTitle: Boolean
) : IBackgroundProcess {

    companion object {
        private val TAG: String = "ProcessSendOCS"
        private val seperateFlag = "-"
    }

    val mOkhttpHelper = OkhttpHelperImpl()

    @Volatile
    var mIsAbort = false
    var mLocalUri: Uri? = null
    var mFileDuration: Long = -1L
    var mFileSize: Long = -1L
    var mFileFormat: String = ""

    override fun process(req: ConvertRecord): Boolean {
        mLocalUri = MediaDBUtils.genUri(req.recordId)
        mFileSize = FileUtils.getFileSize(mLocalUri)
        val pairFormatAndDuration = MediaDBUtils.getFileFormatAndDurationFromUri(mLocalUri)
        mFileFormat = pairFormatAndDuration.first
        mFileDuration = pairFormatAndDuration.second

        val fielName = FileUtils.getDisplayNameByPath(req.mediaPath)
        DebugUtil.i(TAG,
            "==>fielName$fielName, mLocalUri: $mLocalUri, mFileFormat: $mFileFormat, mFileSize: $mFileSize, mFileDuration: $mFileDuration")

        if (!isConditionMet(req.recordId)) {
            return false
        }

        val reqFromDb = ConvertDbUtil.selectByRecordId(req.recordId)
        val reqFromDbAfterFilt = filterDirtyDataFromDb(reqFromDb)

        if (reqFromDbAfterFilt == null) {
            DebugUtil.i(TAG, "==>Init Req!")

            val isInitSuc = initReq(req)
            if (!isInitSuc) {
                updateUI(req.recordId, ConvertStatus.UPLOAD_STATUS_EXCEPTION, Code.EXCEPTION_INIT_REQ)
                DebugUtil.e(TAG, "==>INIT_RECORD_WRONG!")
                return false
            }

            val isInsertSuc = ConvertDbUtil.insert(req)
            if (!isInsertSuc) {
                updateUI(req.recordId, ConvertStatus.UPLOAD_STATUS_EXCEPTION, Code.EXCEPTION_INSERT_REQ)
                DebugUtil.e(TAG, "==>INSERT_DB_WRONG!")
                return false
            }
        } else {
            DebugUtil.i(TAG, "==>Copy Req!")
            req.copy(reqFromDbAfterFilt)
        }
        req.printConvertRecord()
        setStatus(req, ConvertStatus.UPLOAD_STATUS_UPLOADING)

        val isGetPresignedURLsSuc = getPresignedURLs(req)
        if (!isGetPresignedURLsSuc) {
            DebugUtil.e(TAG, "get presignedURLs fail")
            return false
        }
        if (isNeedSendOCS(req)) {
            val isSendOCSSuc = sendOCS(req)
            if (!isSendOCSSuc) {
                DebugUtil.e(TAG, "send OCS fail")
                return false
            }

            val isGetUploadResultSuc = getUploadResult(req)
            if (!isGetUploadResultSuc) {
                DebugUtil.e(TAG, "getUploadResult fail")
                return false
            }
        }
        DebugUtil.i(TAG, "======next process, req.uploadAllUrl: ${req.uploadAllUrl} ")
        return nextProcess?.process(req) ?: false
    }

    private fun getPresignedURLs(req: ConvertRecord): Boolean {
        DebugUtil.i(TAG, "===>getpresignedURLs")
        val isDirtyData = checkIsDirtyDataGetURLs(req)
        if (isDirtyData) {
            deleteDirtyData(req)
            setStatus(req, ConvertStatus.UPLOAD_STATUS_UPLOAD_FAIL, Code.DIRTY_DATA, "getPresignedURLs isDirtyData")
            return false
        }

        val responsePreURLs: BaseResponse<BeanGetPresignedURLs>? =
                try {
                    mOkhttpHelper.getPresignedURLs(
                            req.onlyId!!,
                            req.uploadKey!!,
                            req.partCount,
                            req.uploadRequestId)
                } catch (e: Exception) {
                    handleException(req, e)
                    return false
                }
        if (responsePreURLs?.httpData == null) {
            DebugUtil.e(TAG, "ResponsePreURLs data is null!")
            setStatus(req, ConvertStatus.UPLOAD_STATUS_UPLOAD_FAIL, Code.DATA_GETURL, "httpData is null,${responsePreURLs?.httpMessage}")
            return false
        }

        if (responsePreURLs.httpData.urlList.isNullOrEmpty()) {
            DebugUtil.e(TAG, "presignedURLs is null!")
            setStatus(
                req,
                ConvertStatus.UPLOAD_STATUS_UPLOAD_FAIL,
                Code.DATA_GETURL,
                "httpData.urlList is null or empty,${responsePreURLs.httpMessage}")
            return false
        }
        val needUpload = responsePreURLs.httpData.needUpload
        if (needUpload) {
            if (req.uploadRequestId != responsePreURLs.httpData.uploadId) {
                // fist upload or preURLS are uneffective
                if (responsePreURLs.httpData.uploadId.isNullOrEmpty()) {
                    DebugUtil.e(TAG, "uploadId is null!")
                    setStatus(req, ConvertStatus.UPLOAD_STATUS_UPLOAD_FAIL, Code.DATA_GETURL, "httpData.uploadId is null or empty")
                    return false
                }
                req.uploadRequestId = responsePreURLs.httpData.uploadId
                ConvertDbUtil.updateUploadId(req.recordId, req.uploadRequestId)
                updateReqAllUploadUrlsAndCleanETag(req, responsePreURLs.httpData.urlList)
                updateDbAllUploadUrlsAndCleanETag(req, responsePreURLs.httpData.urlList)
                resetTaskIdAndUploadAllUrl(req)
                setStatus(req, ConvertStatus.UPLOAD_STATUS_UPLOADING)
            }
        } else {
            if (responsePreURLs.httpData.urlList[0].isNullOrEmpty()) {
                setStatus(req, ConvertStatus.UPLOAD_STATUS_UPLOAD_FAIL, Code.DATA_GETURL, "httpData.urlList[0] is null or empty")
                return false
            }
            req.uploadAllUrl = responsePreURLs.httpData.urlList[0]
            ConvertDbUtil.updateAllUrl(req.recordId, req.uploadAllUrl)
            setStatus(req, ConvertStatus.UPLOAD_STATUS_UPLOAD_SUC)
            DebugUtil.i(TAG, "object is already in OCS! req.uploadAllUrl: ${req.uploadAllUrl}")
        }
        return true
    }


    private fun sendOCS(req: ConvertRecord): Boolean {
        DebugUtil.i(TAG, "=====> send OCS")
        var uploadTryCount = 0
        var tempException: Exception? = null
        while (true) {
            /**************   prepare to send    ***************/
            if (uploadTryCount > 2) {
                DebugUtil.e(TAG, "Reach retry limit!")
                if (tempException != null) {
                    handleException(req, tempException, Code.REACH_RETRY_LIMIT)
                } else {
                    setStatus(req, ConvertStatus.UPLOAD_STATUS_UPLOAD_FAIL, Code.REACH_RETRY_LIMIT, "sendOCS exception:$tempException")
                }
                return false
            }
            uploadTryCount += 1
            DebugUtil.i(TAG, "uploadTryCount: $uploadTryCount")
            if (!FileUtils.isFileExist(mLocalUri)) {
                DebugUtil.e(TAG, " LocalUri isFileNotExist!")
                setStatus(req, ConvertStatus.UPLOAD_STATUS_UPLOAD_FAIL, Code.LOCAL_URL_WRONG, "file not exist:$mLocalUri")
                return false
            }

            try {
                val status = sendOCSPartly(req)
                DebugUtil.i(TAG, "sendOCSPartly: status:$status")
                when (status) {
                    SendOCSPartlyStatus.ABORT -> return false
                    SendOCSPartlyStatus.SEND_PART_SUCCESS -> return true
                    SendOCSPartlyStatus.DIRTY_DATA -> return false
                    SendOCSPartlyStatus.ETAG_WRONG -> DebugUtil.e(TAG, "uploadTryCount: $uploadTryCount, ETAG_WRONG")
                }
            } catch (e: Exception) {
                tempException = e
            }
            sleepThreadOneSeconds()
        }
    }

    private fun sleepThreadOneSeconds() {
        try {
            Thread.sleep(1000)
        } catch (e: Exception) {
            DebugUtil.e(TAG, e.toString())
        }
    }

    private enum class SendOCSPartlyStatus {
        ABORT,
        SEND_PART_SUCCESS,
        DIRTY_DATA,
        ETAG_WRONG,
    }

    private fun sendOCSPartly(req: ConvertRecord): SendOCSPartlyStatus {
        DebugUtil.i(TAG, "===>sendOCSPartly")
        while (true) {
            if (mIsAbort) {
                /************** abort when uploading***************/
                DebugUtil.i(TAG, " abort when uploading!")
                setStatus(req, ConvertStatus.UPLOAD_STATUS_UPLOAD_ABORT_SUC)
                return SendOCSPartlyStatus.ABORT
            }
            val targetUploadRecord = checkAllPartDone(req)
            if (targetUploadRecord == null) {
                DebugUtil.i(TAG, " send part success ")
                return SendOCSPartlyStatus.SEND_PART_SUCCESS
            }

            val isDirtyData = checkIsDirtyDataSendOCS(req, targetUploadRecord)
            if (isDirtyData) {
                deleteDirtyData(req)
                setStatus(req, ConvertStatus.UPLOAD_STATUS_UPLOAD_FAIL, Code.DIRTY_DATA, "sendOCSPartly isDirtyData")
                return SendOCSPartlyStatus.DIRTY_DATA
            }
            try {
                val response =
                        mOkhttpHelper.uploadOCS(
                                req.onlyId!!,
                                targetUploadRecord.mUrl,
                                mLocalUri!!,
                                targetUploadRecord.mFileStartRange.toInt(),
                                targetUploadRecord.mFileEndRange.toInt())
                if (response.httpData.isNullOrEmpty()) {
                    DebugUtil.e(TAG, " etag is null!")
                    return SendOCSPartlyStatus.ETAG_WRONG
                } else {
                    targetUploadRecord.mETag = response.httpData
                    UploadDbUtil.updateEtagByOnlyIdAndSeqNum(
                        BaseApplication.getAppContext(),
                        req.onlyId,
                        targetUploadRecord.mSeqNumber,
                        response.httpData
                    )
                }
            } catch (e: Exception) {
                DebugUtil.e(TAG, "HTTP_UPLOADOCS!")
                throw e
            }
        }
    }


    private fun getUploadResult(req: ConvertRecord): Boolean {
        DebugUtil.i(TAG, "======> get upload result in!")

        val eTagList = genETagList(req)

        val isDirtyData = checkIsDirtyDataGetResult(req, eTagList)
        if (isDirtyData) {
            deleteDirtyData(req)
            setStatus(req, ConvertStatus.UPLOAD_STATUS_UPLOAD_FAIL, Code.DIRTY_DATA, "getUploadResult isDirtyData")
            return false
        }

        try {
            val responseGetUploadResult =
                    mOkhttpHelper.getUploadResult(
                            req.onlyId!!,
                            req.uploadRequestId,
                            req.uploadKey!!,
                            false,
                            eTagList
                    )
            if (responseGetUploadResult.httpData == null) {
                setStatus(
                    req,
                    ConvertStatus.UPLOAD_STATUS_UPLOAD_FAIL,
                    Code.DATA_GETUPLOADRESULT,
                    "httpData is null,${responseGetUploadResult.httpMessage}")
                return false
            }
            if (responseGetUploadResult.httpData.locationUrl.isNullOrEmpty()) {
                DebugUtil.e(TAG, " locationUrl is wrong!")
                setStatus(
                    req,
                    ConvertStatus.UPLOAD_STATUS_UPLOAD_FAIL,
                    Code.LOCATION_URL_WRONG,
                    "httpData.locationUrl is null or empty,${responseGetUploadResult.httpMessage}")
                return false
            }
            req.uploadAllUrl = responseGetUploadResult.httpData.locationUrl
            DebugUtil.e(TAG, " uploadAllUrl:  ${req.uploadAllUrl}")
            ConvertDbUtil.updateAllUrl(req.recordId, req.uploadAllUrl)
            setStatus(req, ConvertStatus.UPLOAD_STATUS_UPLOAD_SUC)
        } catch (e: Exception) {
            DebugUtil.e(TAG, "$e!")
            handleException(req, e)
            return false
        }
        return true
    }

    private fun checkIsDirtyDataFromDb(queryResult: ConvertRecord): Boolean {

        when (queryResult.version) {
            VERSION_2_SPEAKER -> {
                if (queryResult.onlyId == "" || queryResult.uploadKey == "" || queryResult.partCount <= 0) {
                    return true
                }

                if (queryResult.uploadRecordList.isNullOrEmpty()) {
                    return true
                }

                if (queryResult.partCount != queryResult.uploadRecordList.size) {
                    return true
                }
                return false
            }
            else -> return queryResult.completeStatus != CONVERT_COMP_STATUS_COMPLETE
        }
    }


    private fun checkIsDirtyDataGetURLs(req: ConvertRecord?): Boolean {
        if (req == null) {
            return true
        }
        if (req.onlyId.isNullOrEmpty()) {
            return true
        }
        if (req.uploadKey.isNullOrEmpty()) {
            return true
        }
        if (req.partCount <= 0) {
            return true
        }
        return false
    }

    private fun checkIsDirtyDataSendOCS(req: ConvertRecord?, uploadRecord: UploadRecord?): Boolean {
        if ((req == null) || (uploadRecord == null)) {
            return true
        }
        if (req.onlyId.isNullOrEmpty()) {
            return true
        }

        if (uploadRecord.mUrl.isNullOrEmpty()) {
            return true
        }

        if ((uploadRecord.mFileStartRange < 0)
                || (uploadRecord.mFileEndRange <= 0)
                || (uploadRecord.mFileStartRange >= uploadRecord.mFileEndRange)) {
            return true
        }

        if (!FileUtils.isFileExist(mLocalUri)) {
            return true
        }
        return false
    }


    private fun checkIsDirtyDataGetResult(req: ConvertRecord?, eTagList: List<RequestGetUploadResult.ETags>): Boolean {
        if ((req == null) || (eTagList.isEmpty())) {
            return true
        }
        if (req.onlyId.isNullOrEmpty()) {
            return true
        }
        if (req.uploadKey.isNullOrEmpty()) {
            return true
        }
        if (req.uploadRequestId.isNullOrEmpty()) {
            return true
        }

        for (i in eTagList.indices) {
            if (eTagList[i].etag.isNullOrEmpty()) {
                return true
            }
        }
        return false
    }

    private fun filterDirtyDataFromDb(req: ConvertRecord?): ConvertRecord? {
        if (req == null) {
            return null
        }
        if (checkIsDirtyDataFromDb(req)) {
            DebugUtil.d(TAG, "delete dirty data")
            deleteDirtyData(req)
            return null
        }
        return req
    }

    private fun deleteDirtyData(req: ConvertRecord) {
        DebugUtil.i(TAG, "===>deleteDirtyData: req.recordId =  ${req.recordId}")
        ConvertDbUtil.deleteByRecordId(req.recordId)
    }


    private fun checkAllPartDone(req: ConvertRecord): UploadRecord? {
        for (i in req.uploadRecordList.indices) {
            if (req.uploadRecordList[i].mETag == "") {
                DebugUtil.i(TAG, "partnum: ${req.uploadRecordList[i].mSeqNumber}")
                DebugUtil.i(TAG, "partnum_mUrl: ${req.uploadRecordList[i].mUrl}")
                DebugUtil.i(TAG, "begin: ${req.uploadRecordList[i].mFileStartRange}")
                DebugUtil.i(TAG, "end: ${req.uploadRecordList[i].mFileEndRange}")
                return req.uploadRecordList[i]
            }
        }
        DebugUtil.e(TAG, "checkAllPartDone: done")
        return null
    }

    private fun genETagList(req: ConvertRecord): List<RequestGetUploadResult.ETags> {
        val eTagList = ArrayList<RequestGetUploadResult.ETags>()
        for (i in req.uploadRecordList.indices) {
            eTagList.add(RequestGetUploadResult.ETags(req.uploadRecordList[i].mSeqNumber, req.uploadRecordList[i].mETag))
        }
        eTagList.sortBy { it.partNumber }
        return eTagList
    }


    private fun isConditionMet(recordId: Long): Boolean {
        if (!isFileFormatMet(recordId)) {
            DebugUtil.e(TAG, "isFileFormatMet : false")
            return false
        }

        if (!isFileSizeMet(recordId)) {
            DebugUtil.e(TAG, "isFileSizeMet : false")
            return false
        }

        if (!isFileDurationMet(recordId)) {
            DebugUtil.e(TAG, "isFileDurationMet : false")
            return false
        }
        return true
    }


    private fun isFileSizeMet(recordId: Long): Boolean {
        DebugUtil.i(TAG, "isFileSizeMet: $mFileSize")
        if (!ConvertCheckUtils.isFileSizeMinMet(mFileSize)) {
            DebugUtil.e(TAG, "mFileSize < 0!")
            updateUI(recordId, ConvertStatus.UPLOAD_STATUS_EXCEPTION, Code.FILESIZE_WRONG)
            return false
        }

        if (!ConvertCheckUtils.isFileSizeMaxMet(mFileSize)) {
            updateUI(recordId, ConvertStatus.UPLOAD_STATUS_UNSUPPORTED_FILE_SIZE, Code.FILESIZE_WRONG)
            return false
        }
        return true
    }

    private fun isFileDurationMet(recordId: Long): Boolean {
        DebugUtil.i(TAG, "isFileDurationMet: $mFileDuration")
        if (!ConvertCheckUtils.isFileDurationMinMet(mFileDuration)) {
            DebugUtil.w(TAG, "mFileDuration <= 0!")
            updateUI(recordId, ConvertStatus.UPLOAD_STATUS_UNSUPPORTED_FILE_DURATION_ZERO, Code.FILEDURATION_WRONG)
            return false
        }

        if (!ConvertCheckUtils.isFileDurationMaxMet(mFileDuration)) {
            updateUI(recordId, ConvertStatus.UPLOAD_STATUS_UNSUPPORTED_FILE_DURATION, Code.FILEDURATION_WRONG)
            return false
        }
        return true
    }

    private fun isFileFormatMet(recordId: Long): Boolean {
        DebugUtil.i(TAG, "isFileFormatMet: $mFileFormat")
        return ConvertCheckUtils.isFileFormatMet(mFileFormat).apply {
            if (!this) {
                updateUI(
                    recordId,
                    ConvertStatus.UPLOAD_STATUS_UNSUPPORTED_FILE_FORMAT,
                    Code.FILEFORMAT_WRONG
                )
            }
        }
    }

    private fun genPartCount(fileSize: Long): Int {
        DebugUtil.i(TAG, "fileSize: $fileSize")
        val a = (fileSize.toDouble() / Constant.SIZE_10M)
        var partCount = ceil(a).toInt()
        if (partCount <= 0) {
            DebugUtil.e(TAG, "checkSplit: partNum <= 0 !")
            partCount = 0
        }
        DebugUtil.i(TAG, "partCount: $partCount")
        return partCount
    }

    private fun genRequestId(req: ConvertRecord): String {
        val uniqueID = UUID.randomUUID().toString()
        val result = req.uploadKey + "-" + uniqueID
        DebugUtil.i(TAG, "RequestId: $result")
        return result
    }

    private fun genUploadKey(req: ConvertRecord): String {
        val tempUri = MediaDBUtils.genUri(req.recordId)
        val uploadKey = HeaderHelper.brand + seperateFlag + ConvertServiceUtils.getSHA256HEX(tempUri)
        DebugUtil.i(ConvertServiceUtils.TAG, ":uploadKey $uploadKey")
        return uploadKey
    }


    private fun isNeedSendOCS(req: ConvertRecord): Boolean {
        return when (req.uploadStatus) {
            ConvertStatus.UPLOAD_STATUS_UPLOAD_SUC -> false
            else -> true
        }
    }


    private fun setStatus(
        req: ConvertRecord,
        status: Int = ConvertStatus.UPLOAD_STATUS_UNINIT,
        errorCode: Int = Code.NORMAL,
        errorMessage: String = ""
    ) {
        DebugUtil.i(TAG, "req.recordId = ${req.recordId}, status = $status, error = $errorCode,message = $errorMessage")
        req.uploadStatus = status
        ConvertDbUtil.updateConvertStatusOnSendOCS(req.recordId, status)
        convertCallback?.onConvertStatusChange(
            req.recordId,
            ConvertStatus(status, ConvertStatus.CONVERT_STATUS_UNINIT),
            errorCode,
            errorMessage,
            convertAiTitle
        )
    }


    private fun updateUI(recordId: Long, status: Int, errorCode: Int) {
        DebugUtil.i(TAG, "recordId = $recordId, status = $status, error = $errorCode")
        convertCallback?.onConvertStatusChange(recordId,
            ConvertStatus(status, ConvertStatus.CONVERT_STATUS_UNINIT),
            errorCode,
            convertAiTitle = convertAiTitle)
    }


    private fun updateDbAllUploadUrlsAndCleanETag(req: ConvertRecord, presignedURLs: List<String>) {
        val tempList = ArrayList<Pair<Int, String>>()
        for (i in presignedURLs.indices) {
            tempList.add(Pair(i + 1, presignedURLs[i]))
        }
        UploadDbUtil.updateAllUrlAndCleanETagByOnlyId(BaseApplication.getAppContext(), req.onlyId, tempList)
    }

    private fun updateReqAllUploadUrlsAndCleanETag(req: ConvertRecord, presignedURLs: List<String>) {
        for (i in presignedURLs.indices) {
            req.uploadRecordList[i].mUrl = presignedURLs[i]
            req.uploadRecordList[i].mETag = ""
        }
    }


    private fun initReq(req: ConvertRecord): Boolean {
        //do not resort these
        req.uploadKey = genUploadKey(req)
        req.onlyId = genRequestId(req)
        req.uploadStatus = ConvertStatus.UPLOAD_STATUS_UNINIT
        req.convertStatus = ConvertStatus.CONVERT_STATUS_UNINIT
        req.partCount = genPartCount(mFileSize)
        req.completeStatus = 0
        req.uploadAllUrl = null
        req.uploadRequestId = ""
        req.taskId = null
        req.convertTextfilePath = null
        req.uploadRecordList = genUploadRecordList(req)
        req.mediaPath = genMediaPath(req)
        req.version = VERSION_2_SPEAKER
        if (req.onlyId == "" || req.uploadKey == "" || req.partCount <= 0) {
            DebugUtil.e(TAG, "initReq false")
            return false
        }
        return true
    }

    private fun genMediaPath(req: ConvertRecord): String {
        val localUri = MediaDBUtils.genUri(req.recordId)
        var mediaPath = ""
        mediaPath = MediaDBUtils.getRecordFromMediaByUriId(localUri).data
        return mediaPath
    }

    private fun genUploadRecordList(req: ConvertRecord): List<UploadRecord> {
        DebugUtil.i(TAG, "===> genUploadRecordList: req.recordId =  ${req.recordId}")
        val tempList = LinkedList<UploadRecord>()
        for (i in 0 until req.partCount) {
            val benginOffet = i * Constant.SIZE_10M.toLong()
            val endOffet = min(mFileSize, (i + 1) * Constant.SIZE_10M.toLong())
            DebugUtil.i(TAG, "===> genUploadRecordList: benginOffet:$benginOffet, endOffet:$endOffet")
            val temp = UploadRecord(0, req.onlyId, benginOffet, endOffet, "", "", i + 1)
            tempList.add(temp)
        }
        return tempList
    }

    private fun resetTaskIdAndUploadAllUrl(req: ConvertRecord) {
        req.taskId = ""
        req.uploadAllUrl = ""
        ConvertDbUtil.updateTaskIdAndUploadAllUrl(req.recordId, req.taskId, req.uploadAllUrl)
    }

    private fun handleException(req: ConvertRecord, e: Exception, code: Int = Code.EXCEPTION) {
        DebugUtil.e(TAG, "e:$e")
        when (e) {
            is OkhttpHelperImpl.UserTimeOutException -> {
                setStatus(req, ConvertStatus.USERTIMEOUT_EXCEPTION, code, "handleException:$e")
                deleteDirtyData(req)
            }
            is EncryptException -> {
                setStatus(req, ConvertStatus.ENCRYPT_EXCEPTION, code, "handleException:$e")
                deleteDirtyData(req)
            }
            is JsonParseException -> {
                setStatus(req, ConvertStatus.JSONPARSE_EXCEPTION, code, "handleException:$e")
                deleteDirtyData(req)
            }
            is NetworkErrorException -> setStatus(req, ConvertStatus.NETWORKERROR_EXCEPTION, code, "handleException:$e")
            else -> setStatus(req, ConvertStatus.EXCEPTION, code, "handleException:$e")
        }
    }

    fun cancel() {
        mIsAbort = true
    }
}
