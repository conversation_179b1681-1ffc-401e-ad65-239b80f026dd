/***********************************************************
 * * Copyright (C), 2010-2024, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:  ISmartSummaryCallback
 * * Description: ISmartSummaryCallback
 * * Version: 1.0
 * * Date : 2025/4/16
 * * Author: W9035969
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>    <date>    <version>    <desc>
 * *  W9035969    2025/4/16   1.0    build this module
 ****************************************************************/
package com.soundrecorder.convertservice.smartname

import com.oplus.unified.summary.sdk.callback.ISummaryCallback

interface ISmartSummaryCallback : ISummaryCallback {

    override fun onCloudState(
        sessionId: String,
        state: Int,
        fileId: String?,
        extras: Map<String, Any>?
    ) {
    }

    override fun onDataAvailable(sessionId: String, jsonResult: String, extras: Map<String, Any>?) {
    }

    override fun onStart(sessionId: String?, extras: Map<String, Any>?) {
    }

    override fun onStop(sessionId: String, extras: Map<String, Any>?) {
    }

    override fun onUploading(sessionId: String, extras: Map<String, Any>?) {
    }
}