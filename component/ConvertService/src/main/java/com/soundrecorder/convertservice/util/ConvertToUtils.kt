/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/12/5
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.convertservice.util

import android.content.Context
import android.text.TextUtils
import com.soundrecorder.base.ext.splitOddTrimEnd
import com.soundrecorder.base.ext.splitTrimEnd
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.NumberConstant
import com.soundrecorder.common.databean.ConvertContentItem
import com.soundrecorder.common.db.MediaDBUtils
import com.soundrecorder.common.share.OShareConvertUtil
import com.soundrecorder.common.share.OShareConvertUtil.KEY_END_FLAG
import com.soundrecorder.common.share.OShareConvertUtil.KEY_START_FLAG
import com.soundrecorder.common.utils.ConvertDbUtil
import com.soundrecorder.common.utils.FunctionOption
import com.soundrecorder.convertservice.bean.BeanConvert
import com.soundrecorder.common.databean.BeanConvertText
import com.soundrecorder.convertservice.convert.NewConvertResultUtil
import java.io.BufferedReader
import java.io.BufferedWriter
import java.io.File
import java.io.FileInputStream
import java.io.FileOutputStream
import java.io.IOException
import java.io.InputStreamReader
import java.io.OutputStreamWriter
import java.nio.charset.StandardCharsets

object ConvertToUtils {
    private const val TAG: String = "ConvertToUtils"
    private const val COUNT_OSHARE_CONVERT_HEADER_LINE: Int = 4

    @JvmStatic
    fun toConvertContentItem(subItem: BeanConvertText.SubItem, isSupportXunFeiOrByte: Boolean): ConvertContentItem? {
        if (TextUtils.isEmpty(subItem.recgText)) {
            return null
        }
        val item = ConvertContentItem()
        item.startTime = (subItem.beginTime.toFloat() * BeanConvert.MS).toLong()
        item.endTime = (subItem.endTime.toFloat() * BeanConvert.MS).toLong()
        item.textContent = subItem.recgText
        item.roleId = subItem.roleId
        item.roleName = RoleNameUtil.genRoleNameByRoleId(subItem.roleId)
        item.textWithWords = BeanConvertTextUtil.genTextWithWords(subItem.rawText)
        item.textWithWordsTimeStamp =
            BeanConvertTextUtil.genTextWithWordsTimeStamp(subItem.timestamp)
        BeanConvertTextUtil.genListSubSentence(item, isSupportXunFeiOrByte)
        DebugUtil.i("=======>ConvertToUtils", "toConvertContentItem=>$item")
        return item
    }

    @Suppress("LongMethod", "TooGenericExceptionCaught")
    @Synchronized
    @Throws(IOException::class, NumberFormatException::class)
    @JvmStatic
    fun readConvertContent(
        appContext: Context?,
        filename: String?,
        convertType: Int?
    ): ArrayList<ConvertContentItem> {
        val startTime = System.currentTimeMillis()
        if (!NewConvertResultUtil.isExternalStorageReadable()) {
            throw IOException("external storage not readable!")
        }
        val file = File(NewConvertResultUtil.getConvertSavePath(appContext), filename)
        DebugUtil.d(TAG, "readConvertFile: ${file.name}, convertType=$convertType")
        if (!file.exists()) {
            throw IOException("$filename not found!")
        }
        val hasSupportXunFeinOrByte = convertImplByXunFeinOrByte(convertType)
        val convertContentItems = ArrayList<ConvertContentItem>()
        val br = BufferedReader(InputStreamReader(FileInputStream(file), StandardCharsets.UTF_8))
        try {
            while (true) {
                val line = br.readLine()
                if (TextUtils.isEmpty(line)) {
                    break
                }
                val contents = line.split(NewConvertResultUtil.SPLIT_FLAG).toTypedArray()
                if (contents.size < NewConvertResultUtil.CONTENT_3) {
                    DebugUtil.i(
                        TAG,
                        "contents.length < 3, the text content is empty, read next line, continue. "
                    )
                    continue
                }
                val item = ConvertContentItem()
                val textStartTime =
                    contents[NewConvertResultUtil.POS_0].toLong() / NumberConstant.NUM_1000
                val textEndTime =
                    contents[NewConvertResultUtil.POS_1].toLong() / NumberConstant.NUM_1000
                val textContent = contents[NewConvertResultUtil.POS_2]

                item.startTime = textStartTime
                item.endTime = textEndTime
                item.textContent = textContent
                if (contents.size >= NewConvertResultUtil.CONTENT_5) {
                    val rawText = contents[NewConvertResultUtil.POS_3] //"H E L "
                    val timestamp = contents[NewConvertResultUtil.POS_4] //"0.81 0.96 1.11 "
                    item.textWithWords = rawText.splitOddTrimEnd()

                    // timestamp 被写入 string “null” 需要过滤一下
                    if (timestamp != "null") {
                        item.textWithWordsTimeStamp =
                            timestamp.splitTrimEnd(NewConvertResultUtil.SPLIT_SPACE)
                    }
                }
                if (contents.size >= NewConvertResultUtil.CONTENT_7) {
                    val stringRoleId = contents[NewConvertResultUtil.POS_5]
                    val roleName = contents[NewConvertResultUtil.POS_6]
                    val roleId = stringRoleId.toInt()
                    item.roleId = roleId
                    item.roleName = roleName
                }
                BeanConvertTextUtil.genListSubSentence(item, hasSupportXunFeinOrByte)
                convertContentItems.add(item)
            }
        } catch (e: Exception) {
            DebugUtil.e(TAG, "readConvertContent exception!" + e.message)
        } finally {
            DebugUtil.e(TAG, "readConvertContent finally")
            br.close()
        }
        val endTime = System.currentTimeMillis()
        DebugUtil.d(
            TAG,
            "readConvertContent: size:" + convertContentItems.size + ", time:" + (endTime - startTime)
        )
        return convertContentItems
    }

    /**
     *
     * 封装此格式位置：OShareConvertUtil.getTextContent
     *
     * 解析OShare目录下的转文本文件
     * 文本内容格式：
     * 文本标题
     *
     * 时间：2025年2月7日 17:03:30 time:unix时间戳
     * 主题:  录音文件名称
     * 参会人：<讲话人序号><讲话人 1>， <讲话人序号><讲话人2>
     *
     * 讲话人 1 该句话起始时间字符串00:01 <讲话人序号 该句话起始unix时间戳>
     * 说话内容
     *
     * 讲话人 1该句话起始时间字符串00:03 <讲话人序号 该句话起始unix时间戳>
     * 说话内容
     */
    @Suppress("LongMethod", "TooGenericExceptionCaught")
    @Synchronized
    @Throws(IOException::class, NumberFormatException::class)
    @JvmStatic
    fun readOShareConvertContent(filePath: String?, convertType: Int?): ArrayList<ConvertContentItem> {
        val startTime = System.currentTimeMillis()
        if (!NewConvertResultUtil.isExternalStorageReadable()) {
            throw IOException("external storage not readable!")
        }
        val file = File(filePath)
        DebugUtil.d(TAG, "readOShareConvertContent: " + file.name)
        if (!file.exists()) {
            throw IOException("$filePath not found!")
        }
        val hasSupportXunFeinOrByte = convertImplByXunFeinOrByte(convertType)
        val convertContentItems = ArrayList<ConvertContentItem>()
        val br = BufferedReader(InputStreamReader(FileInputStream(file), StandardCharsets.UTF_8))
        val headers = mutableListOf<String>()
        try {
            while (true) {
                val line = br.readLine()
                if (line == null) {
                    DebugUtil.i(TAG, "readOShareConvertContent line is null")
                    break
                }
                if (line.isEmpty() || headers.size < COUNT_OSHARE_CONVERT_HEADER_LINE) {
                    if (line.isNotEmpty()) {
                        headers.add(line) // 获取文件标题、时间、主题、参会人
                    }
                    continue
                }
                val contents = line.split(KEY_START_FLAG).toTypedArray() // 讲话人 1 该句话起始时间字符串00:01 <讲话人序号 该句话起始unix时间戳>
                val lineNext = br.readLine() // 内容文本
                if (contents.size > 1 && lineNext.isNotEmpty()) {
                    val item = ConvertContentItem()
                    val roleNameAndTime =
                        contents[NewConvertResultUtil.POS_0].split(" ") //讲话人 1 00:00
                    val roleIdAndTime =
                        contents[NewConvertResultUtil.POS_1].replace(KEY_END_FLAG, "")
                            .split(" ") // 1 259> 讲话人id与文本时间戳
                    var roleId: Int = 0
                    var textStartTime: Long = 0
                    if (roleIdAndTime.size > 1) {
                        kotlin.runCatching {
                            roleId = roleIdAndTime[0].toInt() // 讲话人id
                            textStartTime = roleIdAndTime[1].toLong() // 文本时间时间戳
                        }.onFailure {
                            DebugUtil.w(TAG, "readOShareConvertContent role info read failed:$it")
                        }
                    }
                    item.roleId = roleId
                    item.startTime = textStartTime
                    if (roleNameAndTime.size > 1) {
                        val speakTime = roleNameAndTime.last() // 讲话时间
                        item.roleName =
                            contents[NewConvertResultUtil.POS_0].replace(speakTime, "").trim() // 讲话人昵称
                    } else {
                        item.roleName = contents[NewConvertResultUtil.POS_0]
                    }
                    item.textContent = lineNext
                    BeanConvertTextUtil.genListSubSentence(item, hasSupportXunFeinOrByte)
                    convertContentItems.add(item)
                }
            }
        } catch (e: Exception) {
            DebugUtil.e(TAG, "readConvertContent exception!" + e.message)
        } finally {
            DebugUtil.e(TAG, "readConvertContent finally")
            br.close()
        }
        DebugUtil.d(
            TAG,
            "readConvertContent: size:" + convertContentItems.size + ", time:" + (System.currentTimeMillis() - startTime)
        )
        return convertContentItems
    }

    @Suppress("LongMethod", "TooGenericExceptionCaught")
    @Synchronized
    @Throws(IOException::class)
    @JvmStatic
    fun reWriteConvertFile(
        filePath: String?,
        dataList: List<ConvertContentItem?>?
    ) {
        val startTime = System.currentTimeMillis()
        if (!NewConvertResultUtil.isExternalStorageWritable()) {
            throw IOException("external storage not writeable!")
        }
        val file = File(filePath)
        if (!file.exists()) {
            if (file.parentFile != null && !file.parentFile.exists() && !file.parentFile.mkdirs()) {
                throw IOException("$filePath make file path failed!")
            }
            if (!file.createNewFile()) {
                throw IOException("$filePath make file failed!")
            }
        }
        DebugUtil.d(TAG, "writeConvertFile: $filePath")
        var fileOutputStream: FileOutputStream? = null
        var outputStreamWriter: OutputStreamWriter? = null
        var bw: BufferedWriter? = null
        if (dataList != null && dataList.size > 0) {
            try {
                fileOutputStream = FileOutputStream(file)
                outputStreamWriter = OutputStreamWriter(fileOutputStream, StandardCharsets.UTF_8)
                bw = BufferedWriter(outputStreamWriter)
                for (vad in dataList) {
                    vad?.let {
                        bw.write(combineContents(it))
                    }
                    bw.flush()
                }
            } catch (e: java.lang.Exception) {
                DebugUtil.e(TAG, "writeConvertFile error", e)
            } finally {
                try {
                    fileOutputStream?.close()
                } catch (e: java.lang.Exception) {
                    DebugUtil.e(
                        TAG,
                        "writeConvertFile fileOutputStream close error",
                        e
                    )
                }
                try {
                    outputStreamWriter?.close()
                } catch (e: java.lang.Exception) {
                    DebugUtil.e(
                        TAG,
                        "writeConvertFile outputStreamWriter close error",
                        e
                    )
                }
                try {
                    bw?.close()
                } catch (e: java.lang.Exception) {
                    DebugUtil.e(
                        TAG,
                        "writeConvertFile BufferedWriter close error",
                        e
                    )
                }
            }
        }
        val endTime = System.currentTimeMillis()
        DebugUtil.d(
            TAG,
            "writeConvertFile: size:" + (dataList?.size ?: 0) + ", time:" + (endTime - startTime)
        )
    }

    /**
     * 更新OShare目录下的转文本文件
     */
    @Suppress("LongMethod", "TooGenericExceptionCaught")
    @Synchronized
    @Throws(IOException::class)
    @JvmStatic
    fun reWriteOShareConvertFile(
        mediaId: Long,
        filePath: String?,
        dataList: List<ConvertContentItem?>?
    ) {
        val startTime = System.currentTimeMillis()
        if (!NewConvertResultUtil.isExternalStorageWritable()) {
            throw IOException("external storage not writeable!")
        }
        val record = MediaDBUtils.queryRecordById(mediaId)
        if (record == null) {
            DebugUtil.e(TAG, "reWriteOShareConvertFile record is null")
            return
        }
        val file = File(filePath)
        if (!file.exists()) {
            if (file.parentFile != null && !file.parentFile.exists() && !file.parentFile.mkdirs()) {
                throw IOException("$filePath make file path failed!")
            }
            if (!file.createNewFile()) {
                throw IOException("$filePath make file failed!")
            }
        }
        DebugUtil.d(TAG, "reWriteOShareConvertFile: $filePath")
        var fileOutputStream: FileOutputStream? = null
        var outputStreamWriter: OutputStreamWriter? = null
        if (dataList != null && dataList.size > 0) {
            try {
                fileOutputStream = FileOutputStream(file)
                outputStreamWriter = OutputStreamWriter(fileOutputStream, StandardCharsets.UTF_8)
                //获取拼接好的文本
                val textContent = OShareConvertUtil.getTextContent(
                    record, dataList
                )
                fileOutputStream.write(textContent.toByteArray())
            } catch (e: java.lang.Exception) {
                DebugUtil.e(TAG, "reWriteOShareConvertFile error", e)
            } finally {
                try {
                    fileOutputStream?.close()
                } catch (e: java.lang.Exception) {
                    DebugUtil.e(
                        TAG,
                        "reWriteOShareConvertFile fileOutputStream close error",
                        e
                    )
                }
                try {
                    outputStreamWriter?.close()
                } catch (e: java.lang.Exception) {
                    DebugUtil.e(
                        TAG,
                        "reWriteOShareConvertFile outputStreamWriter close error",
                        e
                    )
                }
            }
        }
        val endTime = System.currentTimeMillis()
        DebugUtil.d(
            TAG,
            "reWriteOShareConvertFile: size:" + (dataList?.size ?: 0) + ", time:" + (endTime - startTime)
        )
    }

    @JvmStatic
    private fun combineContents(item: ConvertContentItem): String? {
        val sb = StringBuilder()
        val startTime: Long = item.startTime * NewConvertResultUtil.MS
        val endTime: Long = item.endTime * NewConvertResultUtil.MS
        sb.append(startTime).append(NewConvertResultUtil.SPLIT_FLAG)
        sb.append(endTime).append(NewConvertResultUtil.SPLIT_FLAG)
        val text: String = item.textContent.replace(NewConvertResultUtil.SPLIT_FLAG, " ")
        sb.append(text).append(NewConvertResultUtil.SPLIT_FLAG)
        sb.append(genListToString(item.textWithWords))
            .append(NewConvertResultUtil.SPLIT_FLAG) //add for deleted RawText
        sb.append(genListToString(item.textWithWordsTimeStamp))
            .append(NewConvertResultUtil.SPLIT_FLAG) //add for deleted Timestamp
        sb.append(item.roleId).append(NewConvertResultUtil.SPLIT_FLAG)
        sb.append(item.roleName)
        sb.append(NewConvertResultUtil.LINE_END)
        DebugUtil.e("combineContents: ConvertContentItem", sb.toString())
        return sb.toString()
    }

    @JvmStatic
    private fun genListToString(stringList: List<String>?): String? {
        if (stringList == null || stringList.isEmpty()) {
            return ""
        }
        val sb = java.lang.StringBuilder()
        for (s in stringList) {
            sb.append(s).append(NewConvertResultUtil.SPLIT_SPACE)
        }
        return sb.toString()
    }

    @JvmStatic
    fun convertImplByXunFeinOrByte(convertType: Int?): Boolean {
        if (convertType == ConvertDbUtil.SERVER_PLAN_ASR) {
            return false
        }
        return FunctionOption.hasSupportXunFei() || FunctionOption.hasSupportByte()
    }
}