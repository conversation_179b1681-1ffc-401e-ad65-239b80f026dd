package com.soundrecorder.convertservice.okhttphelper;

import android.content.Context;
import android.os.Build;
import androidx.test.ext.junit.runners.AndroidJUnit4;
import com.soundrecorder.convertservice.shadows.ShadowFeatureOption;
import com.soundrecorder.convertservice.shadows.ShadowOS12FeatureUtil;
import com.soundrecorder.base.BaseApplication;
import org.hamcrest.Matchers;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.reflect.Whitebox;
import org.robolectric.annotation.Config;
import org.robolectric.shadows.ShadowLog;
import okhttp3.Request;

@RunWith(AndroidJUnit4.class)
@Config(sdk = Build.VERSION_CODES.S, shadows = {ShadowLog.class, ShadowOS12FeatureUtil.class, ShadowFeatureOption.class})
public class OkhttpHelperImplTest {

    private static final String TEST_JSON = "test_json";
    private static final String TEST_REQUEST_ID = "request_id";
    private static final String TEST_URL = "http://test_url";

    private Context mContext;
    private OkhttpHelperImpl mOkhttpHelper;

    @Before
    public void setUp() {
        ShadowLog.stream = System.out;
        mContext = BaseApplication.getAppContext();
        System.setProperty("javax.net.ssl.trustStore", "NONE");
        mOkhttpHelper = new OkhttpHelperImpl();
    }

    @After
    public void tearDown() {
        mContext = null;
        mOkhttpHelper = null;
    }

    @Test
    @Ignore
    public void should_not_null_when_getRequestBuilderWithHeader() throws Exception {
        Request.Builder actual = Whitebox.invokeMethod(mOkhttpHelper, "getRequestBuilderWithHeader", new Object[]{TEST_REQUEST_ID, TEST_JSON});
        Assert.assertNotNull(actual);
        String signValue = actual.url(TEST_URL).build().header("sign");
        Assert.assertNotNull(signValue);
        Assert.assertThat(signValue.length(), Matchers.greaterThan(0));
    }

    @Test
    public void should_throw_exception_when_checkUserTimeOut() {
        String stringResponseBody = "{\n" + "    \"code\": 1011\n"+ "}";
        String errorMsg = "";
        boolean isThrowException = false;
        try {
            Whitebox.invokeMethod(mOkhttpHelper, "checkUserTimeOut", new Object[]{stringResponseBody, TEST_JSON});
        } catch (Exception e) {
            if(e instanceof OkhttpHelperImpl.UserTimeOutException) {
                isThrowException = true;
                errorMsg = e.getMessage();
            }
        }
        Assert.assertTrue(isThrowException);
        Assert.assertEquals(TEST_JSON, errorMsg);
    }

}
