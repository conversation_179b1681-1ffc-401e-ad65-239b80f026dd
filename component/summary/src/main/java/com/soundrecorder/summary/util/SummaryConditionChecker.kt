/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: SummaryConditionChecker
 * Description:
 * Version: 1.0
 * Date: 2024/3/1
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2024/3/1 1.0 create
 */

package com.soundrecorder.summary.util

import android.content.ComponentName
import android.content.Context
import android.content.Intent.FLAG_GRANT_PERSISTABLE_URI_PERMISSION
import android.content.Intent.FLAG_GRANT_READ_URI_PERMISSION
import android.content.pm.PackageManager
import android.net.Uri
import androidx.core.content.FileProvider
import com.oplus.aiunit.core.AIUnit
import com.soundrecorder.base.utils.AppUtil
import com.soundrecorder.base.utils.BaseUtil
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.MetaDataUtils
import com.soundrecorder.base.utils.ToastManager
import com.soundrecorder.common.constant.RecordConstant
import com.soundrecorder.common.databean.Record
import com.soundrecorder.common.task.RecordRouterManager
import com.soundrecorder.modulerouter.summary.ERROR_CODE_DURATION_LARGE
import com.soundrecorder.modulerouter.summary.ERROR_CODE_DURATION_LARGE_WARN
import com.soundrecorder.modulerouter.summary.ERROR_CODE_DURATION_SMALL
import com.soundrecorder.modulerouter.summary.ERROR_CODE_FORMAT
import com.soundrecorder.modulerouter.summary.ERROR_CODE_SIZE_LARGE
import com.soundrecorder.modulerouter.summary.ERROR_CODE_SUCCESS
import com.soundrecorder.modulerouter.summary.ERROR_CODE_SUPER_SAVE_MODE
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File

object SummaryConditionChecker {
    /**从aiunit:nlp、av中定义的常量复制处理，升级unitsdk中，有冲突;不能随意修改*/
    const val AUDIO_RT_ASR = "realtime_asr"
    /**从aiunit:nlp、av中定义的常量复制处理，升级unitsdk中，有冲突 end*/
    /*摘要支持最大文件size*/
    private const val MAX_SUPPORT_RECORD_SIZE = 500 * 1024 * 1024

    /*摘要支持最大警告文件时长：2小时*/
    private const val MAX_SUPPORT_RECORD_DURATION_WARN = 2 * 60 * 60 * 1000

    /*摘要支持最大文件时长：5小时*/
    private const val MAX_SUPPORT_RECORD_DURATION = 5 * 60 * 60 * 1000

    /*摘要支持最小文件时长*/
    private const val MIN_SUPPORT_RECORD_DURATION = 20 * 1000

    /*便签支持录音摘要的metadata最小值*/
    private const val NOTE_SUPPORT_SUMMARY_METADATA_MIN_VALUE = 1
    private const val NOTE_SUPPORT_SUMMARY_METADATA_MIN_VALUE_EXPORT = 2
    /*摘要支持最大时长*/
    private const val SUMMARY_MAX_DURATION_HOUR = 5
    private const val TAG = "SummaryConditionChecker"

    private const val PACKAGE_NAME_NOTE = "com.coloros.note"
    private const val NOTE_PROVIDER = "com.nearme.note.db.NotesProvider"
    private const val NOTE_PROVIDER_META_DATA = "com.oplus.note.db.insert_note_api_version"
    private const val NOTE_API_VERSION = 3

    @JvmStatic
    fun checkSummaryPreCondition(context: Context, record: Record): Int {
        if (RecordRouterManager.instance?.isSuperPowerSaveModeState(context) == true) { // 超级省电模式
            ToastManager.showShortToast(context, com.soundrecorder.common.R.string.summary_error_super_power_mode)
            return ERROR_CODE_SUPER_SAVE_MODE
        }

        if (!isFileFormatMet(record.mimeType)) { // 格式不支持
            ToastManager.showShortToast(context, com.soundrecorder.common.R.string.summary_error_record_format_not_support)
            return ERROR_CODE_FORMAT
        }
        if (!isDamageFileDuration(record.duration)) { // 时长<=0
            ToastManager.showShortToast(context, com.soundrecorder.common.R.string.summary_error_damage_file)
            return ERROR_CODE_DURATION_SMALL
        }

        if (!isFileDurationMinMet(record.duration)) { // 时长<=20s
            ToastManager.showShortToast(context, com.soundrecorder.common.R.string.summary_error_record_short)
            return ERROR_CODE_DURATION_SMALL
        }

        if (!isFileSizeMaxMet(record.fileSize)) { // 大于500M
            ToastManager.showShortToast(context, com.soundrecorder.common.R.string.toast_summary_error_size_over)
            return ERROR_CODE_SIZE_LARGE
        }

        if (!isFileDurationMaxMet(record.duration)) { //大于5小时
            ToastManager.showShortToast(
                context, context.resources.getQuantityString(
                    com.soundrecorder.common.R.plurals.summary_error_record_long,
                    SUMMARY_MAX_DURATION_HOUR,
                    SUMMARY_MAX_DURATION_HOUR
                )
            )
            return ERROR_CODE_DURATION_LARGE
        }

        if (!isFileDurationMaxWarnMet(record.duration)) { // 大于2小时
            return ERROR_CODE_DURATION_LARGE_WARN
        }
        return ERROR_CODE_SUCCESS
    }

    /**
     * AI_UNIT 是否支持录音摘要
     */
    @JvmStatic
    fun isAIUnitSupportSummary(context: Context): Boolean {
        if (!AIUnit.isDeviceSupported(context)) { // 判断设备是否支持或安装AIUnit
            DebugUtil.w(TAG, "isAIUnitSupportSummary false by device enable")
            return false
        }
        return AIUnitApi.summaryAvailable(context)
    }

    /**
     * 判断便签是否支持录音摘要
     * 如果metaData["record_summary_support"] >= 1，代表支持录音摘要
     * 如果metaData["record_summary_support"] >= 2，代表支持录音摘要外销
     */
    @JvmStatic
    fun isNotesSupportSummary(context: Context): Boolean {
        val minVersion =
            if (BaseUtil.isEXP()) NOTE_SUPPORT_SUMMARY_METADATA_MIN_VALUE_EXPORT else NOTE_SUPPORT_SUMMARY_METADATA_MIN_VALUE
        return MetaDataUtils.getMetaDataInt(
            context, AppUtil.getNotesPackageName(), "record_summary_support") >= minVersion
    }

    @JvmStatic
    fun isSupportRecordSummary(context: Context): Boolean {
        return isAIUnitSupportSummary(context)
    }

    @JvmStatic
    suspend fun isNoteSupportAudio(context: Context): Boolean {
        return withContext(Dispatchers.IO) {
            var isSupportAudio = false
            try {
                val component = ComponentName(
                    PACKAGE_NAME_NOTE,
                    NOTE_PROVIDER
                )
                val providerInfo = context.packageManager.getProviderInfo(component, PackageManager.GET_META_DATA)
                val metadata = providerInfo.metaData ?: return@withContext false
                val apiVersion = metadata.getInt(NOTE_PROVIDER_META_DATA)
                if (apiVersion >= NOTE_API_VERSION) {
                    isSupportAudio = true
                }
                DebugUtil.d(TAG, "Note support Audio: apiVersion=$apiVersion, isSupportAudio=$isSupportAudio")
            } catch (e: PackageManager.NameNotFoundException) {
                DebugUtil.e(TAG, "get Note metadata error", e)
            }
            isSupportAudio
        }
    }

    @JvmStatic
    fun grantUriPermission(context: Context, path: String): Uri {
        val uri = FileProvider.getUriForFile(context, "${context.packageName}.fileProvider", File(path))
        context.grantUriPermission(
            AppUtil.getNotesPackageName(), uri, FLAG_GRANT_READ_URI_PERMISSION or FLAG_GRANT_PERSISTABLE_URI_PERMISSION
        )
        return uri
    }

    @JvmStatic
    fun revokeUriPermission(context: Context, uri: Uri) {
        context.revokeUriPermission(uri, FLAG_GRANT_READ_URI_PERMISSION)
    }

    /**
     * 校验文件格式mimeType
     */
    @JvmStatic
    fun isFileFormatMet(fileFormat: String): Boolean {
        return when (fileFormat) {
            RecordConstant.MIMETYPE_MP3,
            RecordConstant.MIMETYPE_3GPP,
            RecordConstant.MIMETYPE_AMR,
            RecordConstant.MIMETYPE_AMR_WB,
            RecordConstant.MIMETYPE_WAV,
            RecordConstant.MIMETYPE_ACC,
            RecordConstant.MIMETYPE_ACC_ADTS,
            -> true

            else -> false
        }
    }

    /**
     * 文件大小是否满足最大限制
     */
    @JvmStatic
    fun isFileSizeMaxMet(fileSize: Long): Boolean {
        return fileSize < MAX_SUPPORT_RECORD_SIZE
    }

    /**
     * 文件时长是否满足最小限制
     */
    @JvmStatic
    fun isFileDurationMinMet(fileDuration: Long): Boolean {
        return fileDuration > MIN_SUPPORT_RECORD_DURATION
    }

    @JvmStatic
    fun isDamageFileDuration(fileDuration: Long): Boolean {
        return fileDuration > 0
    }

    /**
     * 文件时长是否满足最大限制警告
     */
    @JvmStatic
    fun isFileDurationMaxWarnMet(fileDuration: Long): Boolean {
        return fileDuration <= MAX_SUPPORT_RECORD_DURATION_WARN
    }

    /**
     * 文件时长是否满足最大限制
     */
    @JvmStatic
    fun isFileDurationMaxMet(fileDuration: Long): Boolean {
        return fileDuration <= MAX_SUPPORT_RECORD_DURATION
    }
}