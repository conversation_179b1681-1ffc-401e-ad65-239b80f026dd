/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: SummaryApi
 * Description:
 * Version: 1.0
 * Date: 2024/3/7
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2024/3/7 1.0 create
 */

package com.soundrecorder.summary.api

import android.app.Activity
import android.content.Context
import android.content.Intent
import androidx.appcompat.app.AlertDialog
import androidx.lifecycle.MutableLiveData
import com.oplus.aiunit.core.AIUnit
import com.oplus.aiunit.core.data.DetectName
import com.oplus.aiunit.toolkits.AISettings
import com.soundrecorder.base.utils.AppUtil
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.ToastManager
import com.soundrecorder.common.databean.Record
import com.soundrecorder.common.databean.Sentence
import com.soundrecorder.common.databean.markdata.MarkDataBean
import com.soundrecorder.common.db.NoteDbUtils
import com.soundrecorder.common.utils.EnableAppUtil
import com.soundrecorder.modulerouter.summary.IAISummaryInterface
import com.soundrecorder.modulerouter.summary.ISummaryCallback
import com.soundrecorder.modulerouter.summary.SummaryInterface
import com.soundrecorder.summary.RecordSummaryManager
import com.soundrecorder.summary.request.database.SummaryCacheDBHelper
import com.soundrecorder.summary.ui.SummaryFragment
import com.soundrecorder.summary.util.SummaryConditionChecker
import com.soundrecorder.summary.util.SummaryConditionChecker.AUDIO_RT_ASR
import com.soundrecorder.summary.util.SummarySupportManager

object SummaryApi : SummaryInterface {
    const val TAG = "SummaryApi"

    override fun isNoteSupport(context: Context): Boolean {
        return SummaryConditionChecker.isNotesSupportSummary(context)
    }

    override fun <T, R, E> startSummary(from: String, mediaRecord: T, sentenceList: List<R>?, markList: List<E>?) {
        RecordSummaryManager.startSummaryWithPreCheck(from, mediaRecord as Record, sentenceList as? List<Sentence>?, markList as? List<MarkDataBean>?)
    }

    override fun <T, R, E> startSummaryNoPreCheck(from: String, mediaRecord: T, sentenceList: List<R>?, markList: List<E>?) {
        RecordSummaryManager.startSummaryNoPreCheck(from, mediaRecord as Record, sentenceList as? List<Sentence>?, markList as? List<MarkDataBean>?)
    }

    override fun registerSummaryCallback(mediaId: Long, listener: ISummaryCallback) {
        RecordSummaryManager.registerSummaryCallback(mediaId, listener)
    }

    override fun unregisterSummaryCallback(callback: ISummaryCallback) {
        RecordSummaryManager.unregisterSummaryCallback(callback)
    }

    /**
     * 跳转到便签详情
     * -- action:com.oplus.note.action.OPEN_SUMMARY_NOTE
     *-- 参数
     *【key】: speech_log_id，【value】: 通话记录ID
     *【key】: note_id，【value】: 笔记id (录音摘要需要新增)
     *【key】: from_package，【value】: 打开方包名
     *-- package（指定便签包名）
     *除一加外销为com.oneplus.note，其余均为com.coloros.note
     *是否支持note_id的能力兼容判断：
     *<meta-data
     * android:name="com.oplus.note.open.support_note_id"
     * android:value="true" />
     * resultCode
     *-1：有数据，且正常打开相应界面
     *-10：无通话记录或便签ID对应的笔记
     *其它参数
     *把过来的intent中的参数原样带回去
     */
    override fun toNotesSummaryActivity(
        context: Activity,
        callId: String,
        noteId: String,
        requestCode: Int,
        funCallBack: ((clearSummary: Boolean, disableDialog: AlertDialog?) -> Unit)
    ) {
        DebugUtil.d(TAG, "toNotesSummaryActivity noteId=$noteId")
        val packageName = AppUtil.getNotesPackageName()
        val appState = EnableAppUtil.isAppInstallEnabled(context, packageName)
        // 1.检测是否被卸载
        if (appState == EnableAppUtil.APP_NOT_INSTALLED) {
            DebugUtil.w(TAG, "toNotesSummaryPage return by not install")
            ToastManager.showShortToast(context, com.soundrecorder.common.R.string.tip_summary_notes_uninstall)
            clearAllSummaryDBData()
            funCallBack.invoke(true, null)
            return
        }
        // 禅定模式下，便签不在白名单内
        if (AppUtil.isBreathModeNotContainApp(context, packageName)) {
            ToastManager.showShortToast(context, com.soundrecorder.common.R.string.current_mode_no_support_note)
            return
        }
        // 2. 检测是否低版本不支持摘要
        val isSupportSummary = SummaryConditionChecker.isNotesSupportSummary(context)
        if (!isSupportSummary) {
            DebugUtil.w(TAG, "toNotesSummaryPage return by notes version lower")
            ToastManager.showShortToast(context, com.soundrecorder.common.R.string.tip_notes_version_lower)
            clearAllSummaryDBData()
            funCallBack.invoke(true, null)
            return
        }
        // 3.判断是否被禁用
        if (appState == EnableAppUtil.APP_IS_DISABLED) {
            DebugUtil.w(TAG, "toNotesSummaryPage return by notes disabled")
            funCallBack.invoke(
                false, EnableAppUtil.showEnableDialog(
                    context,
                    packageName,
                    com.soundrecorder.common.R.string.enable_request_title,
                    com.soundrecorder.common.R.string.summary_notes_disable_content
                )
            )
            return
        }
        val intent = Intent("com.oplus.note.action.OPEN_SUMMARY_NOTE")
        intent.setPackage(packageName)
        /*如果note_id和speech_log_id都传，以note_id为准*/
        intent.putExtra("speech_log_id", callId)
        intent.putExtra("note_id", noteId)
        intent.putExtra("from_package", context.packageName)
        /*增加该参数，为了解决便签那边不同栈里打开同一个笔记详情，内部会有数据同步问题；
        * 解决方案：启动页面加NEW_TASK flag,但是加了以后收不到result结果，所以通过新增该参数便签内部自己处理*/
        intent.putExtra("flag_new_task", true)
        context.startActivityForResult(intent, requestCode)
    }

    @JvmStatic
    private fun clearAllSummaryDBData() {
        NoteDbUtils.clearAllNoteData()
    }

    /**
     * aiunit等依赖模块是否支持第三方通话录音的摘要功能
     */
    override fun isSupportThirdRecordSummary(context: Context): Boolean {
        if (!AIUnit.isDeviceSupported(context)) { // 判断设备是否支持或安装AIUnit
            DebugUtil.w(TAG, "isSupportThirdRecordSummary false by device enable")
            return false
        }
        if (!AISettings.isRecordSummarySupported(context)) { // 判断当前版本是否是录音摘要版本
            DebugUtil.w(TAG, "isSupportThirdRecordSummary false by record summary enable")
            return false
        }
        if (!AISettings.isDetectSupported(context, DetectName.AUDIO_ASR)) {
            DebugUtil.w(TAG, "isSupportThirdRecordSummary false by asr not support")
            return false
        }

        if (!AISettings.isDetectSupported(context, DetectName.NLP_CALL_SUMMARY)) {
            DebugUtil.w(TAG, "isSupportThirdRecordSummary false by summary not support")
            return false
        }
        return true
    }

    /**
     * aiunit等依赖模块是否支持全局语音摘要功能
     */
    override fun isSupportGlobalSummary(context: Context): Boolean {
        if (!AIUnit.isDeviceSupported(context)) { // 判断设备是否支持或安装AIUnit
            DebugUtil.w(TAG, "isSupportGlobalSummary false by device enable")
            return false
        }
        if (!AISettings.isDetectSupported(context, AUDIO_RT_ASR)) {
            DebugUtil.w(TAG, "isSupportGlobalSummary false by asr not support")
            return false
        }

        if (!AISettings.isDetectSupported(context, DetectName.NLP_CALL_SUMMARY)) {
            DebugUtil.w(TAG, "isSupportGlobalSummary false by summary not support")
            return false
        }
        return true
    }

    /**
     * 获取能力支持状态
     * @param forceUpdate 强制更新
     */
    override fun initSupportSummary(forceUpdate: Boolean) {
        SummarySupportManager.loadSupportRecordSummary(forceUpdate)
    }

    /**
     * 根据需要加载能力支持状态
     * @param countryCode 当前国家码
     */
    override fun loadSupportSummaryByCountry(countryCode: String?) {
        SummarySupportManager.loadSupportSummaryByCountry(countryCode)
    }

    override fun getSupportRecordSummaryValue(): MutableLiveData<Boolean> {
        return SummarySupportManager.supportRecordSummary
    }

    override fun getAISummaryInterface(): IAISummaryInterface {
        return SummaryFragment()
    }

    override fun getSummaryContent(context: Context, mediaId: Long): String {
        return SummaryCacheDBHelper.getChooseSummaryByMediaId(mediaId)?.summaryContent ?: ""
    }
}