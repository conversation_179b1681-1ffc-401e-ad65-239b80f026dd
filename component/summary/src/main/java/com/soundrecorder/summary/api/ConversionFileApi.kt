/*********************************************************************
 * * Copyright (C), 2023, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  ConversionFileApi.kt
 * * Description :
 * * Version     : 1.0
 * * Date        : 2025/5/29
 * * Author      : W9067780
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.summary.api

import android.content.Context
import com.soundrecorder.modulerouter.summary.ConversionFileAction
import com.soundrecorder.summary.exportfile.ConversionFileHelper

object ConversionFileApi : ConversionFileAction {

    override fun conversionFileToWord(
        act: Context,
        targetPath: String,
        title: String,
        text: String
    ): Boolean {
        val convertFileToWord = ConversionFileHelper.convertFileToWord(act, targetPath, title, text)
        return convertFileToWord
    }

    override fun conversionFileToPdf(
        act: Context,
        targetPath: String,
        title: String,
        text: String
    ): Boolean {
        val convertFileToPdf = ConversionFileHelper.convertFileToPdf(act, targetPath, title, text)
        return convertFileToPdf
    }

    override fun conversionFileToTxt(
        targetPath: String,
        text: String
    ) {
    }
}