#Questionnaire begin
-keep class com.oplus.recorder.**{ *; }
-keep class com.oplus.questionnaire.**{ *; }
-keep class com.oplus.questionnaire.ui.**{ *; }
-keep class com.oplus.questionnaire.data.** {
    *;
}
#Questionnaire end

# keep feedback sdk
-keep public class com.customer.feedback.sdk.**{*;}
-keep @com.oplus.baselib.database.annotation.DbEntity class * {*;}
-keepclassmembers class * {
  @com.oplus.nearx.cloudconfig.anotation.FieldIndex *;
}
-keep @androidx.anntotation.Keep class **

-keep public class com.customer.feedback.sdk.model.RequestData { *; }
-keep public class com.customer.feedback.sdk.model.RequestData$* { *;}
