/*********************************************************************
 * * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  AutoDiForQuestionnaire.kt
 * * Description : AutoDiForQuestionnaire
 * * Version     : 1.0
 * * Date        : 2025/04/18
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 *    renjiahao                             1.0        create
 ***********************************************************************/
package com.oplus.recorder.questionnaire.di

import androidx.annotation.Keep
import com.oplus.recorder.questionnaire.QuestionnaireApi
import com.soundrecorder.modulerouter.questionnaire.QuestionnaireInterface
import org.koin.dsl.module

@Keep
object AutoDiForQuestionnaire {
    val questionnaireModule = module {
        single<QuestionnaireInterface>(createdAtStart = true) {
            QuestionnaireApi
        }
    }
}