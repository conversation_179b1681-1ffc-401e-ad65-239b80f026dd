/*********************************************************************
 * * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  AutoDiForFeedBack.kt
 * * Description : AutoDiForFeedBack
 * * Version     : 1.0
 * * Date        : 2025/04/18
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 *    renjiahao                             1.0        create
 ***********************************************************************/
package com.oplus.recorder.feedback.di

import androidx.annotation.Keep
import com.oplus.recorder.feedback.FeedBackApi
import com.soundrecorder.modulerouter.FeedBackInterface
import org.koin.dsl.module

@Keep
object AutoDiForFeedBack {
    val feedBackModule = module {
        single<FeedBackInterface>(createdAtStart = true) {
            FeedBackApi
        }
    }
}