apply from: "../../common_build.gradle"

android {
    sourceSets {
        main {
            res.srcDirs += ['res']
            res.srcDirs += ['../../res-strings']
        }
    }
    namespace "com.soundrecorder.wavemark"
}
dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    implementation libs.org.kotlin.stdlib
    implementation libs.androidx.appcompat
    implementation libs.androidx.core.ktx
    implementation libs.androidx.lifecycle.viewmodel

    // base包为必须引用的包，prop_versionName需保持一致
    implementation (libs.oplus.coui.core) {
        exclude group: 'com.squareup.okio', module: 'okio'
    }
    implementation libs.oplus.coui.poplist
    implementation libs.oplus.coui.recyclerview
    implementation libs.oplus.coui.dialog
    implementation libs.oplus.coui.panel

    // Koin for Android
    implementation(libs.koin)

    implementation project(':common:libbase')
    implementation project(':common:libimageload')
    implementation project(':common:libcommon')
    implementation project(':common:modulerouter')
}
