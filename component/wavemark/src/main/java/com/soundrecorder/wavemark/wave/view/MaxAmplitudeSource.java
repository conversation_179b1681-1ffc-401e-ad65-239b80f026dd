package com.soundrecorder.wavemark.wave.view;

/***********************************************************
 ** Copyright (C), 2008-2016, OPPO Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - MaxAmplitudeSource.java
 ** Description: XXXXXXXXXXXXXXXXXXXXX.
 ** Version: 1.0
 ** Date : 2017/12/06
 ** Author: <PERSON><PERSON><PERSON>.<PERSON>@Apps.Music
 **
 ** ---------------------Revision History: ---------------------
 **  <author>	<data> 	  <version >	   <desc>
 **  <EMAIL>       2017/12/06    1.0     build this module
 **  BBB       2016/11/25     1.1     add some components
 ****************************************************************/
public interface MaxAmplitudeSource {
        int getMaxAmplitude();
        long getTime();
        int getRecorderState();
}
