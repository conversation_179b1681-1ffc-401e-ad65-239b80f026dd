/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: WaveSafeArrayList
 Description:
 Version: 1.0
 Date: 2022/8/9
 Author: W9013333(v-zhengt<PERSON><PERSON><EMAIL>)
 -----------Revision History-----------
 <author> <date> <version> <desc>
 W9013333 2022/8/9 1.0 create
 */

package com.soundrecorder.wavemark.wave.load

class WaveSafeArrayList : ArrayList<Int?>() {
    @Synchronized
    override fun add(element: Int?): <PERSON><PERSON><PERSON> {
        return super.add(element)
    }

    @Synchronized
    override fun addAll(elements: Collection<Int?>): <PERSON><PERSON>an {
        return super.addAll(elements)
    }

    @Synchronized
    override fun clear() {
        super.clear()
    }

    @Synchronized
    override fun removeAt(index: Int): Int? {
        return super.removeAt(index)
    }

    @Suppress("TooGenericExceptionCaught")
    override fun get(index: Int): Int {
        return if (index in 0 until size) {
            try {
                super.get(index) ?: 0
            } catch (e: Exception) {
                0
            }
        } else {
            0
        }
    }
}
