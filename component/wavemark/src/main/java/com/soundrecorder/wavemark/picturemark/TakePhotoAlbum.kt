package com.soundrecorder.wavemark.picturemark

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.net.Uri
import androidx.activity.result.contract.ActivityResultContract
import com.soundrecorder.base.utils.BaseUtil

class TakePhotoAlbum : ActivityResultContract<Unit, Uri?>() {
    companion object {
        private val TAG = "TakePhotoAlbum"

        fun getAlumPackageName(): String {
            return if (BaseUtil.isOnePlusExp()) {
                "com.oneplus.gallery"
            } else {
                "com.coloros.gallery3d"
            }
        }
    }

    override fun createIntent(context: Context, input: Unit): Intent {
        val packageName = getAlumPackageName()
        if (packageName.isEmpty()) {
            return Intent()
                    .setAction(Intent.ACTION_PICK)
                    .setType("image/*")
        }
        return Intent()
                .setAction(Intent.ACTION_PICK)
                .setPackage(packageName)
                .setType("image/*")
    }

    override fun getSynchronousResult(context: Context, input: Unit): SynchronousResult<Uri?>? {
        return null
    }

    override fun parseResult(resultCode: Int, intent: Intent?): Uri? {
        return if (intent == null || resultCode != Activity.RESULT_OK) null else intent.data
    }
}