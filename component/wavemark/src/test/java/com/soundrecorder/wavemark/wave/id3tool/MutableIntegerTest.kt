/*********************************************************************
 * * Copyright (C), 2024, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  MutableIntegerTest
 * * Description :
 * * Version     : 1.0
 * * Date        : 2024/1/25
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.wavemark.wave.id3tool

import android.os.Build
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.wavemark.shadows.ShadowFeatureOption
import org.junit.Assert
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowFeatureOption::class])
class MutableIntegerTest {

    @Test
    fun should_equals_when_setValue() {
        val integer = MutableInteger(-1)
        Assert.assertEquals(-1, integer.value)

        integer.value = 100
        Assert.assertEquals(100, integer.value)

        integer.increment()
        Assert.assertEquals(101, integer.value)

        val integer2 = MutableInteger(101)
        Assert.assertEquals(integer, integer2)
    }

    @Test
    fun should_success_when_equals() {
        val integer1 = MutableInteger(-1)
        val integer2 = integer1
        val integer3 = MutableInteger(1)
        val integer4: MutableInteger? = null
        val integer5 = -1

        Assert.assertEquals(integer1, integer2)
        Assert.assertNotEquals(integer1, integer4)
        Assert.assertNotEquals(integer1, integer5)
        Assert.assertNotEquals(integer1, integer3)

        integer3.value = -1
        Assert.assertEquals(integer1, integer3)
        integer1.increment()
        Assert.assertNotEquals(integer1, integer3)

        integer3.increment()
        Assert.assertEquals(integer1.hashCode(), integer3.hashCode())
    }
}