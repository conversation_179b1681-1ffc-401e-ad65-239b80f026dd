apply from: "../../common_build.gradle"

android {
    sourceSets {
        main {
            res.srcDirs += ['res']
            res.srcDirs += ['../../res-strings']
        }
    }
    namespace "com.soundrecorder.notification"
}
dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    implementation libs.org.kotlin.stdlib
    implementation libs.androidx.appcompat
    implementation libs.androidx.core.ktx

    // base包为必须引用的包，prop_versionName需保持一致
    implementation (libs.oplus.coui.core) {
        exclude group: 'com.squareup.okio', module: 'okio'
    }
    testImplementation(libs.oplus.coui.core)

    // Koin for Android
    implementation(libs.koin)

    testImplementation libs.oplus.support.adapter
    implementation project(':common:libbase')
    implementation project(':common:libcommon')
    implementation project(':common:modulerouter')
}
