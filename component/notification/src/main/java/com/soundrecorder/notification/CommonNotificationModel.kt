/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  CommonNotificationModel
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/07/28
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.notification

import androidx.lifecycle.LiveData
import com.soundrecorder.base.utils.ConditionMutableLiveData
import com.soundrecorder.base.utils.TimeUtils
import com.soundrecorder.modulerouter.notification.NotificationModel

class CommonNotificationModel {
    companion object {
        @JvmStatic
        fun copyFrom(notificationModel: NotificationModel): CommonNotificationModel {
            return CommonNotificationModel().also {
                it.playDuration = notificationModel.playDuration
                it.setPlayName(notificationModel.playName)
                it.setPlayStatus(notificationModel.playStatus)
                it.setCurTime(notificationModel.curTime)
                it.setBtnDisabled(notificationModel.isBtnDisabled)
                it.isMarkEnabled = notificationModel.isMarkEnabled
                it.markPoint = notificationModel.markPoint
                it.saveState = notificationModel.saveState

                it.canJumpIntent = notificationModel.canJumpIntent
                it.isRecycle = notificationModel.isRecycle
            }
        }
    }

    var playDuration: Long = 0
    var isRecycle: Boolean = false
    var playName: ConditionMutableLiveData<String>? = null
        private set
    var playStatus: ConditionMutableLiveData<Int>? = null
        private set
    var curTime: ConditionMutableLiveData<Long>? = null
        private set

    var isBtnDisabled: ConditionMutableLiveData<Boolean>? = null
        private set
    var isMarkEnabled: LiveData<Boolean>? = null
        set(value) {
            field = value
            lastMarkEnabled = value?.value
        }
    var markPoint:  LiveData<Long>? = null
        set(value) {
            field = value
            lastMarkPoint = value?.value
        }
    var saveState: LiveData<Int>? = null

    var canJumpIntent: Boolean = true

    var lastMarkEnabled: Boolean? = null
        private set
    var lastMarkPoint: Long? = null
        private set

    fun isMarkEnabledChanged(): Boolean {
        return isMarkEnabled?.value != lastMarkEnabled
    }

    fun isMarkPointChanged(): Boolean {
        return markPoint?.value != lastMarkPoint
    }

    fun saveCurrentMarkPoint() {
        lastMarkPoint = markPoint?.value
    }

    fun saveCurrentMarkEnabled() {
        lastMarkEnabled = isMarkEnabled?.value
    }

    fun setPlayName(playName: LiveData<String>?) {
        if (playName == null) {
            return
        }

        this.playName = ConditionMutableLiveData(playName, true)
    }

    fun setPlayStatus(playStatus: LiveData<Int>?) {
        if (playStatus == null) {
            return
        }

        this.playStatus = ConditionMutableLiveData(playStatus, true)
    }

    fun setCurTime(curTime: LiveData<Long>?) {
        if (curTime == null) {
            return
        }

        this.curTime = object : ConditionMutableLiveData<Long>(curTime, true) {
            override fun needDispatchValue(newValue: Long?): Boolean {
                return (newValue != oldValue) &&
                        (TimeUtils.getFormatTimeExclusiveMill(newValue ?: 0) !=
                                TimeUtils.getFormatTimeExclusiveMill(oldValue ?: 0))
            }
        }
    }

    fun setBtnDisabled(isBtnDisabled: LiveData<Boolean>?) {
        if (isBtnDisabled == null) {
            return
        }

        this.isBtnDisabled = ConditionMutableLiveData(isBtnDisabled, true)
    }
}