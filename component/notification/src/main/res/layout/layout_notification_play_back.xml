<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <include layout="@layout/include_notificaiton_title_content_playback" />

    <FrameLayout
        android:id="@+id/mark_btn_area"
        style="@style/Notification_Button_Container"
        android:layout_below="@id/time_rl"
        android:contentDescription="@string/talkback_flag">

        <TextView
            android:id="@+id/mark_btn"
            style="@style/Notification_Button"
            android:layout_alignParentBottom="true"
            android:background="@drawable/notification_background_button"
            android:text="@string/talkback_flag"
            android:layout_gravity="bottom"
            android:contentDescription="@string/talkback_flag"
            android:textColor="@color/notification_button_text_color"/>
    </FrameLayout>

    <FrameLayout
        android:id="@+id/play_btn_area"
        style="@style/Notification_Button_Container"
        android:layout_below="@id/time_rl"
        android:layout_toEndOf="@id/mark_btn_area">
        <TextView
            android:id="@+id/play_btn"
            android:background="@drawable/notification_background_button"
            style="@style/Notification_Button"
            tools:text="@string/talkback_flag"
            android:layout_gravity="bottom"
            android:textColor="@color/notification_button_text_color"
            />
    </FrameLayout>

    <FrameLayout
        android:id="@+id/save_btn_area"
        android:visibility="gone"
        style="@style/Notification_Button_Container"
        android:layout_below="@id/time_rl"
        android:layout_toEndOf="@id/play_btn_area">
        <TextView
            android:id="@+id/save_btn"
            style="@style/Notification_Button"
            android:layout_gravity="bottom"
            android:background="@drawable/notification_background_button"
            android:layout_alignParentBottom="true"
            android:text="@string/rename_save"
            android:textColor="@color/notification_button_text_color"
            />
    </FrameLayout>
</RelativeLayout>