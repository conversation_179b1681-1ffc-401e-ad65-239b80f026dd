<template>
    <card-template entry="notification" category="modular" onclick="cardClick">
        <compact>
            <leading>
                <lottie level="A*" src="{{$r('images.lottie_logo')}}" autoplay="{{recordInProgress}}" loop="true" voice-label="{{logoDescription}}"></lottie>
                <text level="B*"></text>
            </leading>
            <trailing>
                <widget level="A0" min="0" max="100" if="{{ showSavedLottie }}" value="{{ processPercent }}" color="#00BD13" bg-color="#292929"></widget>
                <lottie level="A0" src="{{ saveLottie }}" else></lottie>
                <text level="B0"></text>
            </trailing>
        </compact>

        <expanded level="F1*" bg-color="{{ cardBg }}" border-color="#40959595">
            <center category="common">
                <widget level="D1*" min="0" max="100" if="{{ showSavedLottie }}"  value="{{ processPercent }}" color="#00BD13" bg-color="#4A5769"></widget>
                <lottie level="D1*" src="{{ saveLottie }}" else></lottie>
                <text level="B1*" transform-effect="none" voice-label="{{timeDescription}}" voice-label="{{saveTitle2}}">{{saveTitle2}}</text>           
            </center>
        </expanded>
    
        <voice>{{ voiceLabel }}</voice>
        <image level="A1*" src="{{$r('images.logo')}}"></image>
    </card-template>
</template>
<style>
</style>
<data>
    {
        "uiData": {
            "timeDescription": "",
            "voiceLabel": true,
            "recordInProgress": true,
            "logoDescription":"",
            "cardClickType":"",
            "fileName2":"",
            "fileName":"",
            "saveTitle2":"",
            "cardBg":"$r('images.card_bg_color')",
            "processPercent":"5",
            "saveLottie":"",
            "cardBg":"$r('images.card_bg_color')",
            "showSavedLottie":true
        },
        "uiEvent": {
            "cardClick": {
                "type": "{{cardClickType}}",
                "uri": 'nativeapp://oplus.intent.action.com.soundrecorder.SEEDLING_CARD',
                "params": {
                    "should_auto_find_file_name": "{{fileName}}"
                }
            }
        }
    }
</data>
