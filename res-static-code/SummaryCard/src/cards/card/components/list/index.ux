<import name="widget-button" src="../button/base.ux"></import>

<template>
  <div class="widgetui-list {{ isDark ? 'widgetui-list--dark' : ''}}">
    <div class="widgetui-list-left">
      <div
        if="{{ type }}"
        class="widget-list-image widget-list-image--{{ type }}"
      >
        <slot></slot>
      </div>

      <div class="widgetui-list-content">
        <text class="widgetui-list-title" style="{{ titleStyle }}">
          {{ title }}
        </text>
        <text class="widgetui-list-subtitle" style="{{ subtitleStyle }}">
          {{ subtitle }}
        </text>
      </div>
    </div>

    <div if="{{ buttonText }}" class="widgetui-list-right">
      <widget-button
        type="list"
        color="{{ buttonTextColor }}"
        background-color="{{ buttonBackgroundColor }}"
        @click="onClick"
      >
        {{ buttonText }}
      </widget-button>
    </div>
  </div>
</template>

<script>
/**
 * @file 列表组件
 */
export default {
  props: {
    isDark: {
      type: Boolean,
      default: false,
    },
    type: {
      type: String,
      default: '',
    },
    title: {
      type: String,
      default: '',
    },
    titleColor: {
      type: String,
      default: '',
    },
    subtitle: {
      type: String,
      default: '',
    },
    subtitleColor: {
      type: String,
      default: '',
    },
    buttonText: {
      type: String,
      default: '',
    },
    buttonTextColor: {
      type: String,
      default: '#fff',
    },
    buttonBackgroundColor: {
      type: String,
      default: '#51C4E0',
    },
  },

  computed: {
    titleStyle() {
      if (this.titleColor) {
        return {
          color: this.titleColor,
        }
      }

      return {}
    },

    subtitleStyle() {
      if (this.subtitleColor) {
        return {
          color: this.subtitleColor,
        }
      }

      return {}
    },
  },

  onClick(e) {
    e.stopPropagation()
    this.$emit('buttonClick', e)
  },
}
</script>

<style lang="less">
.widgetui-list {
  flex-shrink: 0;
  flex-direction: row;
  justify-content: space-between;
  width: 100%;
  max-height: 40px;

  .widgetui-list-left {
    flex-grow: 1;

    .widget-list-image {
      flex-shrink: 0;
      margin-right: 8px;
      width: 40px;
      height: 40px;
      border-radius: 12px;
    }

    .widget-list-image--circle {
      border-radius: 50%;
    }

    .widget-list-image--medium {
      width: 54px;
    }

    .widget-list-image--large {
      width: 71px;
    }

    .widgetui-list-content {
      flex-direction: column;
      justify-content: center;
      max-height: 40px;

      .widgetui-list-title {
        width: 100%;
        line-height: 22px;
        font-size: 16px;
        font-weight: 500;
        color: rgba(0, 0, 0, 0.9);
        lines: 1;
        text-overflow: ellipsis;
      }

      .widgetui-list-subtitle {
        width: 100%;
        line-height: 16px;
        font-size: 12px;
        font-weight: 400;
        color: rgba(0, 0, 0, 0.54);
        lines: 1;
        text-overflow: ellipsis;
      }
    }
  }

  .widgetui-list-right {
    flex-shrink: 0;
    align-items: center;
    padding-left: 8px;
  }
}

.widgetui-list--dark {
  .widgetui-list-left {
    .widgetui-list-content {
      .widgetui-list-title {
        color: rgba(255, 255, 255, 0.9);
      }

      .widgetui-list-subtitle {
        color: rgba(255, 255, 255, 0.54);
      }
    }
  }
}
</style>
