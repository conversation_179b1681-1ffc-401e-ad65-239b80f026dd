<import name="card-template" src="../../cards/card/index.ux"></import>

<template>
  <div class="wrapper">
    <div class="middle-wrapper-a">
      <!-- 获取录音卡片 -->
      <card-template is-dark="{{ isDark }}"></card-template>
    </div>
    <div class="ml-20 middle-wrapper-a">
      <card-template is-dark="{{ isDark }}"></card-template>
    </div>
  </div>
</template>

<script>
/**
 * @file 体验卡片在不同桌面布局下的情况的展示页
 */
import configuration from '@system.configuration'
export default {
  prrivate: {
    isDark: configuration.getThemeMode() === 1
  }
}
</script>

<style lang="less">
.wrapper {
  flex-direction: row;
  align-content: center;
  display: flex;
  justify-content: center;

  .middle-wrapper-a {
    height: 148dp;
    width: 148dp;
    border-radius: 20dp;
    border: 1dp solid #000000;
  }
  .ml-20 {
    margin-left: 20dp;
  }
}
</style>
